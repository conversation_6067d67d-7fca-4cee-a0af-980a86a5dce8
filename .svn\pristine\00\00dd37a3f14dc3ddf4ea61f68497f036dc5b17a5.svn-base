package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.base;

import android.graphics.Point;
import android.graphics.PointF;

public class Port extends PointF {
	public String desc = "";
	public PointF point;

	public Port() {
	}

	public Port(PointF p, String desc) {
		this((int)p.x, (int)p.y, desc);
	}

	public Port(float x, float y, String desc) {
		super(x,y);
		this.desc = desc;
		this.point = new PointF(x, y);
	}

	public void setDesc(String desc){
		this.desc = desc;
	}

}
