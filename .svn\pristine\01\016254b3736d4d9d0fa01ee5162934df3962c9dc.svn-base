package com.kemov.visual.spcd.spcdvisualandroidapp.activity;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.baseres.base.PubApp;
import com.kemov.visual.spcd.spcdvisualandroidapp.R;
import com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.optical_fiber_tag.FiberTagsAdapter;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.GlwlLabelBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_REGION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.PrefReader;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static java.util.Arrays.asList;

public class GlwlLabelActivityDlg extends BaseActivity implements View.OnClickListener {

    private TextView title;
    private ImageButton left_back;
    private Map<String, Object> datas;
    private int type;
    private BaseDaoImp regionDao;
    private BaseDaoImp cubicleDao;
    private BaseDaoImp unitDao;
    private BaseDaoImp portDao;
    private BaseDaoImp boardDao;
    private BaseDaoImp cableDao;
    private BaseDaoImp coreDao;
    private BaseDaoImp intCoreDao;
    private String desc;
    private Integer cableId;
    private String name;
    private Integer num;
    private GlwlLabelBean glwlLabelBean;
    private GlwlLabelBean.InfoBean infoBeanA;
    private GlwlLabelBean.InfoBean infoBeanB;
    private String final_unit_port = null;
    KM_SPCD_CUBICLE km_spcd_cubicle;
    KM_SPCD_REGION km_spcd_region;
    SharedPreferences sp;
    int tag_type;//0是国标版本，1是陕西版本
    KM_SPCD_PORT km_spcd_portA = null;
    KM_SPCD_PORT km_spcd_portB = null;
    KM_SPCD_UNIT km_spcd_unitStart;
    KM_SPCD_UNIT km_spcd_unitFinal;
    private KM_SPCD_PORT km_spcd_portStart;
    private KM_SPCD_PORT km_spcd_portFinal;
    KM_SPCD_CABLE km_spcd_cable;
    private int km_spcd_coreSize;
    private KM_SPCD_CORE km_spcd_coreFinal;
    private KM_SPCD_INTCORE km_spcd_intcoreFinal;//两端连接的是装置，没有odf
    private String portA;//前一个经过odf时，出发端口
    private String portB;//经过odf时，结束端口
    private String portA1;//后一个经过odf时，出发端口
    private String portB1;//经过odf时，结束端口

    private LinearLayout ll_zoom_bar;
    private ImageView mapBiggerView0, mapSmallerView0, mapOriginalView0, mapOriginalViewFilled0;//缩放适配

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_ACTION_BAR);
        //setDlgTheme();
        setContentView(R.layout.activity_glwl_label_dlg);
        setFinishOnTouchOutside(true);
        initView();
    }

    boolean useDlgTheme = false;
    private void setDlgTheme() {
        //修改主题
        //requestWindowFeature(Window.FEATURE_ACTION_BAR);
        //requestWindowFeature(Window.FEATURE_LEFT_ICON);
        Intent intent = getIntent();
        useDlgTheme = intent.getBooleanExtra("useDlgTheme",false);
        if (useDlgTheme){
            this.setTheme(R.style.tag_view_dialog);//dialog tag_view_dialog
        }
        // 设置左边图标
        //getWindow().setFeatureDrawableResource(Window.FEATURE_LEFT_ICON,
        //        android.R.drawable.ic_dialog_alert);
        //setFinishOnTouchOutside(false);
    }

    @Override
    public void initView() {
        Intent intent = getIntent();
        datas = (Map<String,Object>)intent.getSerializableExtra("datas");
        type = intent.getIntExtra("type", 0);
        desc = (String) datas.get("desc");
        cableId = (Integer) datas.get("cableID");
        name = (String) datas.get("name");
        num = (Integer) datas.get("num");
        sp = PubApp.sp;

        tag_type = sp.getInt("tag_type", FiberTagsAdapter.USER_TYPE_SHAANXI);
        String dbName = PrefReader.getInstance(this).get(null);
        regionDao = new BaseDaoImp(this, KM_SPCD_REGION.class, dbName);
        cubicleDao = new BaseDaoImp(this, KM_SPCD_CUBICLE.class, dbName);
        unitDao = new BaseDaoImp(this, KM_SPCD_UNIT.class, dbName);
        portDao = new BaseDaoImp(this, KM_SPCD_PORT.class, dbName);
        boardDao = new BaseDaoImp(this, KM_SPCD_BOARD.class, dbName);
        intCoreDao = new BaseDaoImp(this, KM_SPCD_INTCORE.class, dbName);
        cableDao = new BaseDaoImp(this, KM_SPCD_CABLE.class, dbName);
        coreDao = new BaseDaoImp(this, KM_SPCD_CORE.class, dbName);

        findViewWithId();
        setData();
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        glwlLabelBean = GlwlLabelBean.getInstance();
        glwlLabelBean.clear();
    }

    private void setData() {
        glwlLabelBean = GlwlLabelBean.getInstance();
        glwlLabelBean.clear();
        glwlLabelBean.setTag_type(tag_type);
        if (TextUtils.isEmpty(desc)){
            return;
        }

        infoBeanA = new GlwlLabelBean.InfoBean();
        infoBeanB = new GlwlLabelBean.InfoBean();

        glwlLabelBean.setGlwlName(name);
        if (desc.contains("——")){
            String[] split = desc.split("——");
            if (split.length < 2){
                return;
            }
            String s1 = split[0];//如：R66.P4
            String s2 = split[1];//如：R66.P4
            if (type == 1){
                getRegionCubicle(s1,s2,1);
                getRegionCubicle(s2,s1,2);
            }else if (type == 2){//s1=   ODF.1.1-RT
                Integer intCore = (Integer)datas.get("intCore");
                KM_SPCD_INTCORE km_spcd_intcore = null;
                if (intCore!=null){
                    km_spcd_intcore = (KM_SPCD_INTCORE)intCoreDao.getById(intCore);
                    glwlLabelBean.setGlwlName(km_spcd_intcore.getName());
                    setIntcoreData(km_spcd_intcore);
                }
            }
        }
    }

    private void setIntcoreData(KM_SPCD_INTCORE km_spcd_intcore) {
        try {
            km_spcd_cubicle = (KM_SPCD_CUBICLE)cubicleDao.getFirstForEq("id", km_spcd_intcore.getCubicle_id());
            km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("id", km_spcd_cubicle.getRegion_id());
            String port_a = km_spcd_intcore.getPort_a();//1n.1.A-Tx
            String port_b = km_spcd_intcore.getPort_b();//2n.1.A-Rx
            if (!TextUtils.isEmpty(port_a) && !TextUtils.isEmpty(port_b)){
                String[] splitA = port_a.split("\\.");
                String[] splitB = port_b.split("\\.");
                String splitA2 = splitA[2];//A-Tx
                String splitB2 = splitB[2];//A-Rx
                String[] splitA3 = null;
                String[] splitB3 = null;
                if (!TextUtils.isEmpty(splitA2) && splitA2.contains("-")){
                    splitA3 = splitA2.split("-");
                }
                if (!TextUtils.isEmpty(splitB2) && splitB2.contains("-")){
                    splitB3 = splitB2.split("-");
                }
                //查询两个装置是否都是ied，还是有odf
                KM_SPCD_UNIT km_spcd_unitA = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", splitA[0]);
                KM_SPCD_UNIT km_spcd_unitB = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", splitB[0]);
                if (km_spcd_unitA == null || km_spcd_unitB == null){
                    showToast("未找到两侧的装置");
                    return;
                }
                if (splitA3 != null){
                    //找出端口
                    KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) boardDao.getFirstForEq("unit_id", km_spcd_unitA.getId(), "slot", splitA[1]);
                    if (km_spcd_board!=null){
                        km_spcd_portA = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", km_spcd_board.getId(), "no", splitA3[0], "direction", splitA3[1]);
                    }
                }
                if (splitB3 != null){
                    //找出端口
                    KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) boardDao.getFirstForEq("unit_id", km_spcd_unitB.getId(), "slot", splitB[1]);
                    if (km_spcd_board!=null){
                        km_spcd_portB = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", km_spcd_board.getId(), "no", splitB3[0], "direction", splitB3[1]);
                    }
                }

                if ("ODF".equals(km_spcd_unitA.getDev_class()) || "ODF".equals(km_spcd_unitB.getDev_class())){//其中一个为odf
                    //设置发出端信息
                    glwlLabelBean.setCubicleNameA(km_spcd_region.getDescription() + "-" + km_spcd_cubicle.getName() + "-" + km_spcd_cubicle.getDescription());
                    setQrcode(km_spcd_intcore);
                }else {
//                    glwlLabelBean.setGlwlName(km_spcd_intcore.getName());
                    glwlLabelBean.setCubicleNameA(km_spcd_region.getDescription() + "-" + km_spcd_cubicle.getName() + "-" + km_spcd_cubicle.getDescription());
                    glwlLabelBean.setCubicleNameB(km_spcd_region.getDescription() + "-" + km_spcd_cubicle.getName() + "-" + km_spcd_cubicle.getDescription());
                    infoBeanA.setNo(name);
                    infoBeanB.setNo(name);

                    if (splitA3 != null){
                        infoBeanA.setFr(splitA[0] + "/" + splitA[1] + "/" + splitA3[0] + splitA3[1]);
                        infoBeanB.setTo(splitA[0] + "/" + splitA[1] + "/" + splitA3[0] + splitA3[1]);
                    }else {
                        infoBeanA.setFr(splitA[0] + "/" + splitA[1]);
                        infoBeanB.setTo(splitA[0] + "/" + splitA[1]);
                    }
                    if (splitB3 != null){
                        infoBeanA.setTo(splitB[0] + "/" + splitB[1] + "/" + splitB3[0] + splitB3[1]);
                        infoBeanB.setFr(splitB[0] + "/" + splitB[1] + "/" + splitB3[0] + splitB3[1]);
                    }else {
                        infoBeanA.setTo(splitB[0] + "/" + splitB[1]);
                        infoBeanB.setFr(splitB[0] + "/" + splitB[1]);
                    }
                    //2边都是本屏柜的装置ied，二维码设置应该如何
                    setQrcode(km_spcd_intcore);

                    //--改造陕西版本二维码
                    km_spcd_intcoreFinal = km_spcd_intcore;
                    setCubicleName();
                    int odfSwitchIed1= getisOdfSwitchIed(km_spcd_unitA);
                    int odfSwitchIed2= getisOdfSwitchIed(km_spcd_unitB);
                    if (splitA2.contains("Tx")){
                        setUnitName(km_spcd_unitA,km_spcd_unitB);
                        setGsBm(km_spcd_portA,km_spcd_portB,port_a,port_b,1,odfSwitchIed1,odfSwitchIed2);
                    }else if (splitA2.contains("Rx")){
                        setUnitName(km_spcd_unitB,km_spcd_unitA);
                        setGsBm(km_spcd_portA,km_spcd_portB, port_a, port_b, 2,odfSwitchIed1,odfSwitchIed2);
                    }else {//如果是RT
                        if (splitB2.contains("Tx")){
                            setUnitName(km_spcd_unitB,km_spcd_unitA);
                            setGsBm(km_spcd_portA,km_spcd_portB, port_a, port_b, 2,odfSwitchIed1,odfSwitchIed2);
                        }else if (splitB2.contains("Rx")){
                            setUnitName(km_spcd_unitA,km_spcd_unitB);
                            setGsBm(km_spcd_portA,km_spcd_portB, port_a, port_b, 1,odfSwitchIed1,odfSwitchIed2);
                        }else {
                            setUnitName(km_spcd_unitA,km_spcd_unitB);
                            setGsBm(km_spcd_portA,km_spcd_portB, port_a, port_b, 1,odfSwitchIed1,odfSwitchIed2);
                        }
                    }
                }
                glwlLabelBean.setInfoBeanA(infoBeanA);
                glwlLabelBean.setInfoBeanB(infoBeanB);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private int getisOdfSwitchIed(KM_SPCD_UNIT km_spcd_unit) {
        if (km_spcd_unit.getDev_class().equals("IED")){
            return 0;
        }else if (km_spcd_unit.getDev_class().equals("ODF")){
            return 1;
        }else if (km_spcd_unit.getDev_class().equals("SWITCH")){
            return 2;
        }
        return 0;
    }

    private void setGsBm(KM_SPCD_PORT km_spcd_portA, KM_SPCD_PORT km_spcd_portB, String port_a, String port_b, int type,int odfSwitchIed1,int odfSwitchIed2){//odfSwitchIed，0是ied，1是odf，2是交换机
        String[] splitA = port_a.split("\\.");
        String[] splitB = port_b.split("\\.");
        String bm1 = getBm(km_spcd_portA.getUsage());
        String bm2 = getBm(km_spcd_portB.getUsage());
        if (type == 1){
            if (km_spcd_portA!=null){
                infoBeanA.setStartGS(bm1);
                infoBeanB.setEndGS(bm1);
            }
            if (km_spcd_portB!=null){
                infoBeanB.setStartGS(bm2);
                infoBeanA.setEndGS(bm2);
            }
            //设置端口
            setStartEndPort(splitA,infoBeanA,infoBeanB,odfSwitchIed1);
            setStartEndPort(splitB,infoBeanB,infoBeanA,odfSwitchIed2);
        }else if (type == 2){
            if (km_spcd_portA!=null){
                infoBeanA.setEndGS(bm1);
                infoBeanB.setStartGS(bm1);
            }
            if (km_spcd_portB!=null){
                infoBeanB.setEndGS(bm2);
                infoBeanA.setStartGS(bm2);
            }
            //设置端口
            setStartEndPort(splitA,infoBeanB,infoBeanA,odfSwitchIed1);
            setStartEndPort(splitB,infoBeanA,infoBeanB,odfSwitchIed2);
        }
    }

    private String getBm(String usage){
        if (TextUtils.isEmpty(usage)){
            return "";
        }
        if (usage.contains("-")){
            String[] split = usage.split("-");
            return split[1];
        }else {
            return usage;
        }
    }

    private void setStartEndPort(String[] splitA, GlwlLabelBean.InfoBean infoBean1, GlwlLabelBean.InfoBean infoBean2,int odfSwitchIed){
        if (splitA!=null && splitA.length!=0){
            String boardPort = splitA[2];
            String[] split = boardPort.split("-");
            infoBean1.setStartPort(splitA[1]+"_"+split[0]+"_"+split[1]);
            if (odfSwitchIed == 1){//odf
                infoBean2.setEndPort("H_"+splitA[1]+"_"+split[0]+"_"+split[1]);
            }else if (odfSwitchIed == 2){//交换机
                infoBean2.setEndPort("S_"+splitA[1]+"_"+split[0]+"_"+split[1]);
            }else {
                infoBean2.setEndPort(splitA[1]+"_"+split[0]+"_"+split[1]);
            }
        }
    }

    private void getRegionCubicle(String regionCubicle, String regionCubicle1, int type) {
        if (!TextUtils.isEmpty(regionCubicle) && regionCubicle.contains(".")){
            String region = regionCubicle.substring(0, regionCubicle.indexOf("."));
            String cubicle = regionCubicle.substring(regionCubicle.indexOf(".")+1,regionCubicle.length());

            if (TextUtils.isEmpty(regionCubicle1)){
                return;
            }
            String region1 = regionCubicle1.substring(0, regionCubicle1.indexOf("."));
            String cubicle1 = regionCubicle1.substring(regionCubicle1.indexOf(".")+1,regionCubicle1.length());
            try {
                //查询小室
                KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION) regionDao.getFirstForEq("substation_id", 1, "name", region);
                KM_SPCD_REGION km_spcd_region1 = (KM_SPCD_REGION) regionDao.getFirstForEq("substation_id", 1, "name", region1);
                //查询线缆
                KM_SPCD_CABLE km_spcd_cable = (KM_SPCD_CABLE)cableDao.getFirstForEq("name", this.name);
                List<KM_SPCD_CORE> cores = coreDao.getListForEq("cable_id", km_spcd_cable.getId());
                int i = 0;
                int y = 0;
                if (cores!=null && cores.size()!=0){
                    for (KM_SPCD_CORE km_spcd_core:cores){
                        if (!TextUtils.isEmpty(km_spcd_core.getPort_a()) && !TextUtils.isEmpty(km_spcd_core.getPort_b())){
                            i++;
                        }
                    }
                    y = cores.size() - i;
                }

                if (km_spcd_region!=null){
                    //查询屏柜
                    KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE)cubicleDao.getFirstForEq("substation_id", 1, "region_id", km_spcd_region.getId(), "name", cubicle);
                    KM_SPCD_CUBICLE km_spcd_cubicle1 = (KM_SPCD_CUBICLE)cubicleDao.getFirstForEq("substation_id", 1, "region_id", km_spcd_region1.getId(), "name", cubicle1);
                    if (km_spcd_cubicle!=null){
                        if (type == 1){
                            glwlLabelBean.setCubicleNameA(km_spcd_region.getDescription() + "-" + km_spcd_cubicle.getName() + "-" + km_spcd_cubicle.getDescription());
                            infoBeanA.setNo(this.name);
                            infoBeanA.setInfo(cores.size() + "芯（" + i + "备" + y + ")");
                            infoBeanA.setFr(km_spcd_cubicle.getDescription() + "(" + km_spcd_cubicle.getName() +")");
                            infoBeanA.setTo(km_spcd_cubicle1.getDescription() + "(" + km_spcd_cubicle1.getName() +")");
                            infoBeanA.setQrCode(km_spcd_cable.getName()+"/"+km_spcd_cubicle.getName());
                            glwlLabelBean.setInfoBeanA(infoBeanA);
                        }else if (type == 2){
                            glwlLabelBean.setCubicleNameB(km_spcd_region.getDescription() + "-" + km_spcd_cubicle.getName() + "-" + km_spcd_cubicle.getDescription());
                            infoBeanB.setNo(this.name);
                            infoBeanB.setInfo(cores.size() + "芯（" + i + "备" + y + ")");
                            infoBeanB.setFr(km_spcd_cubicle.getDescription() + "(" + km_spcd_cubicle.getName() +")");
                            infoBeanB.setTo(km_spcd_cubicle1.getDescription() + "(" + km_spcd_cubicle1.getName() +")");
                            infoBeanB.setQrCode(km_spcd_cable.getName()+"/"+km_spcd_cubicle.getName());
                            glwlLabelBean.setInfoBeanB(infoBeanB);
                        }
                    }
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void findViewWithId() {
        title = (TextView)findViewById(R.id.title);
        left_back = (ImageButton)findViewById(R.id.left_back);
        left_back.setOnClickListener(this);
        title.setText("标签图");
        //全局还原图
        ll_zoom_bar = findViewById(R.id.ll_zoom_bar);
        mapBiggerView0 = (ImageView) findViewById(R.id.mapBigger0);
        mapSmallerView0 = (ImageView) findViewById(R.id.mapSmaller0);
        mapOriginalView0 = (ImageView) findViewById(R.id.mapOriginal0);
        mapOriginalViewFilled0 = (ImageView) findViewById(R.id.mapOriginalFilled);
        mapBiggerView0.setOnClickListener(this);
        mapSmallerView0.setOnClickListener(this);
        mapOriginalView0.setOnClickListener(this);
        mapOriginalViewFilled0.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        int i = view.getId();
        if (i == R.id.left_back) {
            finish();
        }
    }

    private void setQrcode(KM_SPCD_INTCORE km_spcd_intcore) {
        //根据odf去查找Cable连接
        String cubicleC =km_spcd_region .getName() +"." + km_spcd_cubicle.getName();//如：cubicleA="R66.XLKZ1A"

        String port_a = km_spcd_intcore.getPort_a();
        String port_b = km_spcd_intcore.getPort_b();
        String[] splitA = port_a.split("\\.");
        String[] splitB = port_b.split("\\.");
        //判断两侧port_a、port_b都是装置的情况
        try {
            KM_SPCD_UNIT km_spcd_unitA = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", splitA[0]);
            KM_SPCD_UNIT km_spcd_unitB = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", splitB[0]);
            if ("IED".equals(km_spcd_unitA.getDev_class()) || "SWITCH".equals(km_spcd_unitA.getDev_class())){
                if ("IED".equals(km_spcd_unitB.getDev_class()) || "SWITCH".equals(km_spcd_unitB.getDev_class())){
                    infoBeanA.setQrCode(km_spcd_intcore.getName()+"/"+km_spcd_cubicle.getName());
                    infoBeanB.setQrCode(km_spcd_intcore.getName()+"/"+km_spcd_cubicle.getName());
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        if (splitA!=null && splitA.length!=0){
            String name = splitA[0];//如4n
            String name1 = splitB[0];//如4n
            selectCables(name,name1,port_a,port_b,cubicleC,1);
        }
        if (TextUtils.isEmpty(infoBeanA.getQrCode()) && splitB!=null && splitB.length!=0){//如果上面没有设置到Qrcode的值
            String name = splitB[0];//如4n
            String name1 = splitA[0];//如4n
            selectCables(name,name1,port_a,port_b,cubicleC,2);
        }
        setinfoBean();
    }

    private void selectCables(String name,String name1, String port_a, String port_b,String cubicleC,int type) {
        try {
            KM_SPCD_UNIT km_spcd_unit = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", name);
            KM_SPCD_UNIT km_spcd_unit1 = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", name1);
            if (km_spcd_unit!=null){
                if ("ODF".equals(km_spcd_unit.getDev_class())){//如果是odf，就去找km_spcd_core
                    km_spcd_unitStart = km_spcd_unit1;
                    if (type == 1){
                        final_unit_port = port_b;
                        km_spcd_portStart = km_spcd_portB;
                        portA = port_b;//起点端口
                        portB = port_a;//终点端口
                    }else if (type == 2){
                        final_unit_port = port_a;
                        km_spcd_portStart = km_spcd_portA;
                        portA = port_a;//起点端口
                        portB = port_b;//终点端口
                    }
                    setFrTo(null,null,null,final_unit_port,1);

                    //查找与portOdf相连的屏柜
                    List<String> keys = new ArrayList<String>(asList("station_id", "cubicleA"));
                    List<Object> value = new ArrayList<Object>(asList(1,cubicleC));
                    List<KM_SPCD_CABLE> cablesA = (List<KM_SPCD_CABLE>) cableDao.getListForEq(keys, value);//cubicleA是当前屏柜

                    List<String> keys0 = new ArrayList<String>(asList("station_id", "cubicleB"));
                    List<KM_SPCD_CABLE> cablesB = (List<KM_SPCD_CABLE>) cableDao.getListForEq(keys0, value);//cubicleA是当前屏柜
                    if (type == 1){
                        analysisCables(cablesA,port_a,1);
                        analysisCables(cablesB,port_a,2);
                    }else if (type == 2){
                        analysisCables(cablesA,port_b,1);
                        analysisCables(cablesB,port_b,2);
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private void setinfoBean() {
        if (km_spcd_portStart == null || km_spcd_portFinal == null){
            return;
        }
        //--改造陕西版本二维码
        String direction = km_spcd_portStart.getDirection();
        String direction1 = km_spcd_portFinal.getDirection();
        int odfSwitchIed1 = getisOdfSwitchIed(km_spcd_unitStart);
        int odfSwitchIed2 = getisOdfSwitchIed(km_spcd_unitFinal);
        if (direction.contains("Tx")){
            setUnitName(km_spcd_unitStart,km_spcd_unitFinal);
            setGsBm(km_spcd_portStart,km_spcd_portFinal,portA,portB,1,odfSwitchIed1,odfSwitchIed2);
        }else if (direction.contains("Rx")){
            setUnitName(km_spcd_unitFinal,km_spcd_unitStart);
            setGsBm(km_spcd_portStart,km_spcd_portFinal,portA,portB,2,odfSwitchIed1,odfSwitchIed2);
        }else {//如果是RT
            if (direction1.contains("Tx")){
                setUnitName(km_spcd_unitFinal,km_spcd_unitStart);
                setGsBm(km_spcd_portStart,km_spcd_portFinal,portA1,portB1,1,odfSwitchIed1,odfSwitchIed2);
            }else if (direction1.contains("Rx")){
                setUnitName(km_spcd_unitStart,km_spcd_unitFinal);
                setGsBm(km_spcd_portStart,km_spcd_portFinal,portA1,portB1,2,odfSwitchIed1,odfSwitchIed2);
            }else {
                setUnitName(km_spcd_unitStart,km_spcd_unitFinal);
                setGsBm(km_spcd_portStart,km_spcd_portFinal, portA,portB,1,odfSwitchIed1,odfSwitchIed2);
            }
        }
        //光缆、光纤
        setCubicleName();
    }

    private void setUnitName(KM_SPCD_UNIT km_spcd_unitStart, KM_SPCD_UNIT km_spcd_unitFinal){
        if (TextUtils.isEmpty(km_spcd_unitStart.getDescription())){
            infoBeanA.setStartUnit(km_spcd_unitStart.getIed_name()+"");
            infoBeanB.setEndUnit(km_spcd_unitStart.getIed_name()+"");
        }else{
            infoBeanA.setStartUnit(km_spcd_unitStart.getDescription()+"");
            infoBeanB.setEndUnit(km_spcd_unitStart.getDescription()+"");
        }
        if (TextUtils.isEmpty(km_spcd_unitFinal.getDescription())){
            infoBeanA.setEndUnit(km_spcd_unitFinal.getIed_name()+"");
            infoBeanB.setStartUnit(km_spcd_unitFinal.getIed_name()+"");
        }else {
            infoBeanA.setEndUnit(km_spcd_unitFinal.getDescription()+"");
            infoBeanB.setStartUnit(km_spcd_unitFinal.getDescription()+"");
        }
    }

    private void setCubicleName(){
        if (km_spcd_cable!=null){
            infoBeanA.setGlName(km_spcd_cable.getName()+"");
            infoBeanB.setGlName(km_spcd_cable.getName()+"");
            infoBeanA.setGlgq(km_spcd_coreSize+"-"+km_spcd_coreFinal.getNo());
            infoBeanB.setGlgq(km_spcd_coreSize+"-"+km_spcd_coreFinal.getNo());
        }else if (km_spcd_intcoreFinal!=null){
            infoBeanA.setGlName(km_spcd_intcoreFinal.getName()+"");
            infoBeanB.setGlName(km_spcd_intcoreFinal.getName()+"");
        }
    }

    private void analysisCables(List<KM_SPCD_CABLE> cables, String portOdf, int type) {
        if (cables!=null && cables.size()!=0){
            List<Integer> ids = new ArrayList<>();
            for (KM_SPCD_CABLE km_spcd_cable:cables){
//                if ("GL".equals(km_spcd_cable.getType())){
                ids.add(km_spcd_cable.getId());
//                }
            }
            List<KM_SPCD_CORE> km_spcd_cores = null;//找到与当前屏柜cubicleA相连的KM_SPCD_CORE集合
            try {
                km_spcd_cores = coreDao.getListForIn("cable_id", ids);
                if (km_spcd_cores!=null && km_spcd_cores.size()!=0){
                    for (KM_SPCD_CORE km_spcd_core:km_spcd_cores){
                        //cubicleA是相连的odf
                        String port_b1 = null;
                        String cubicleB = null;
                        if (type == 1 && km_spcd_core.getPort_a().equals(portOdf)){
                            port_b1 = km_spcd_core.getPort_b();//与之相连的对侧屏柜的odf
                            Integer cable_id = km_spcd_core.getCable_id();
                            km_spcd_cable = (KM_SPCD_CABLE)cableDao.getById(cable_id);
                            km_spcd_coreSize = km_spcd_cores.size();
                            km_spcd_coreFinal = km_spcd_core;
                            cubicleB = km_spcd_cable.getCubicleB();//对侧屏柜信息，如：R66.XLP1A
                            setQrcodeDetail(km_spcd_cable,km_spcd_core,1);
                            break;
                        }else if (type == 2 && km_spcd_core.getPort_b().equals(portOdf)){
                            port_b1 = km_spcd_core.getPort_a();//与之相连的对侧屏柜的odf
                            Integer cable_id = km_spcd_core.getCable_id();
                            km_spcd_cable = (KM_SPCD_CABLE)cableDao.getById(cable_id);
                            km_spcd_coreSize = km_spcd_cores.size();
                            km_spcd_coreFinal = km_spcd_core;
                            cubicleB = km_spcd_cable.getCubicleA();//对侧屏柜信息，如：R66.XLP1A
                            setQrcodeDetail(km_spcd_cable,km_spcd_core,2);
                            break;
                        }
                    }
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    private void setQrcodeDetail(KM_SPCD_CABLE km_spcd_cable, KM_SPCD_CORE km_spcd_core,Integer type) {
        if (!TextUtils.isEmpty(final_unit_port)){
            String[] split = final_unit_port.split("\\.");
            String port = split[2];

//            glwlLabelBean.setGlwlName(km_spcd_cable.getName());
            infoBeanA.setNo(km_spcd_cable.getName());
            infoBeanB.setNo(km_spcd_cable.getName());
            String cubicle = null;
            String portAB = null;
            if (type == 1){
                cubicle = km_spcd_cable.getCubicleB();//对侧屏柜
                portAB = km_spcd_core.getPort_b();
            }else if (type == 2){
                cubicle = km_spcd_cable.getCubicleA();//对侧屏柜
                portAB = km_spcd_core.getPort_a();
            }
            String[] splitB = cubicle.split("\\.");
            //查询小室和屏柜
            if (splitB!=null){
                try {
                    KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION) regionDao.getFirstForEq("substation_id", 1, "name", splitB[0]);
                    if (km_spcd_region == null){
                        return;
                    }
                    KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE)cubicleDao.getFirstForEq("substation_id", 1, "region_id", km_spcd_region.getId(), "name", splitB[1]);
                    glwlLabelBean.setCubicleNameB(km_spcd_region.getDescription() + "-" + km_spcd_cubicle.getName() + "-" + km_spcd_cubicle.getDescription());

                    String[] split1 = portAB.split("\\.");
                    if (split1!=null && split1.length!=0){
                        km_spcd_unitFinal = (KM_SPCD_UNIT) unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", split1[0]);
                    }

                    //找对侧屏柜与odf相连的装置
                    List<KM_SPCD_INTCORE> cores = intCoreDao.getListForEq("cubicle_id",km_spcd_cubicle.getId());
                    for (KM_SPCD_INTCORE km_spcd_intcore:cores){
                        if (km_spcd_intcore.getPort_a().equals(portAB)){
                            String port_b = km_spcd_intcore.getPort_b();
                            setFrTo(km_spcd_cable,km_spcd_core,km_spcd_cubicle,port_b,2);
                            //最终连接的对侧装置及端口
                            if (km_spcd_unitFinal!=null){
                                String[] split2 = port_b.split("\\.");
                                KM_SPCD_UNIT km_spcd_unitOther = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", split2[0]);
                                KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) boardDao.getFirstForEq("unit_id", km_spcd_unitOther.getId(), "slot", split2[1]);
                                if (km_spcd_board!=null){
                                    String boardPort = split2[2];
                                    String[] split3 = boardPort.split("-");
                                    km_spcd_portFinal = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", km_spcd_board.getId(), "no", split3[0], "direction", split3[1]);
                                    //设置端口
                                    portA1 = port_b;
                                    portB1 = portAB;
                                }
                            }
                            break;
                        }
                        if (km_spcd_intcore.getPort_b().equals(portAB)){
                            String port_a = km_spcd_intcore.getPort_a();
                            setFrTo(km_spcd_cable,km_spcd_core,km_spcd_cubicle,port_a,2);
                            //最终连接的对侧装置及端口
                            if (km_spcd_unitFinal!=null){
                                String[] split2 = port_a.split("\\.");
                                KM_SPCD_UNIT km_spcd_unitOther = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", split2[0]);
                                KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) boardDao.getFirstForEq("unit_id", km_spcd_unitOther.getId(), "slot", split2[1]);
                                if (km_spcd_board!=null){
                                    String boardPort = split2[2];
                                    String[] split3 = boardPort.split("-");
                                    km_spcd_portFinal = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", km_spcd_board.getId(), "no", split3[0], "direction", split3[1]);
                                    //设置端口
                                    portA1 = port_a;
                                    portB1 = portAB;
                                }
                            }
                            break;
                        }
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            //  JBLF22ZS/WL-2211-734-1/11C/11n/1/5Rx
            if (!TextUtils.isEmpty(port) && port.contains("-")){
                String[] split1 = port.split("-");
                infoBeanA.setQrCode(km_spcd_cable.getName()+ "-" + km_spcd_core.getNo() + "/"+ km_spcd_cubicle.getName()+"/" + split[0] +"/" + split[1] +"/" + split1[0] + split1[1]);
            }else {
                infoBeanA.setQrCode(km_spcd_cable.getName()+ "-"+ km_spcd_core.getNo() + "/"+ km_spcd_cubicle.getName()+"/" + split[0] +"/" + split[1] +"/" + port);
            }
        }
    }

    public void setFrTo(KM_SPCD_CABLE km_spcd_cable,KM_SPCD_CORE km_spcd_core,KM_SPCD_CUBICLE km_spcd_cubicle,String port,Integer type){
        String[] split1 = port.split("\\.");
        String split2 = split1[2];
        String[] split3 = null;
        if (!TextUtils.isEmpty(split2) && split2.contains("-")){
            split3 = split2.split("-");
        }
        if (type == 1){
            if (split3!=null){
                infoBeanA.setFr(split1[0] + "/" + split1[1] + "/" + split3[0] + split3[1]);
                infoBeanB.setTo(split1[0] + "/" + split1[1] + "/" + split3[0] + split3[1]);
            }else {
                infoBeanA.setFr(split1[0] + "/" + split1[1]);
                infoBeanB.setTo(split1[0] + "/" + split1[1]);
            }
        }else if (type == 2){
            if (split3!=null){
                infoBeanA.setTo(split1[0] + "/" + split1[1] + "/" + split3[0] + split3[1]);
                infoBeanB.setFr(split1[0] + "/" + split1[1] + "/" + split3[0] + split3[1]);
            }else {
                infoBeanA.setTo(split1[0] + "/" + split1[1]);
                infoBeanB.setFr(split1[0] + "/" + split1[1]);
            }
        }
        if (km_spcd_cable!=null){
            if (split3 != null){
                infoBeanB.setQrCode(km_spcd_cable.getName()+ "-" + km_spcd_core.getNo() + "/"+ km_spcd_cubicle.getName()+"/" + split1[0] +"/" + split1[1] +"/" + split3[0] + split3[1]);
            }else {
                infoBeanB.setQrCode(km_spcd_cable.getName()+ "-" + km_spcd_core.getNo() + "/"+ km_spcd_cubicle.getName()+"/" + split1[0] +"/" + split1[1]);
            }
        }
    }
}
