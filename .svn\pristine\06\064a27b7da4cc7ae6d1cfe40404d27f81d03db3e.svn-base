package com.kemov.visual.spcd.spcdvisualandroidapp.visualview;

import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.RectF;
import android.os.Build;
import android.os.SystemClock;
import android.support.annotation.Nullable;
import android.text.Layout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Toast;

import com.kemov.visual.spcd.spcdvisualandroidapp.R;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.GlwlLabelBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.GlwlLabelRectfBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.OdfLogicBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.OdfLogicDrawBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.utils.DrawUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Config;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Constants;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.QRCode;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Utils;

import org.apache.poi.hslf.model.Fill;

import java.util.ArrayList;
import java.util.List;

import static com.kemov.visual.spcd.spcdvisualandroidapp.utils.Config.LABEL_ARROW_WIDTH;
import static com.kemov.visual.spcd.spcdvisualandroidapp.utils.Config.LABEL_HEIGHT;
import static com.kemov.visual.spcd.spcdvisualandroidapp.utils.Config.LABEL_INTERVAL;

public class GlwlLabelView extends View {

    private static final String TAG = "GlwlLabelView";
    private float eventX, eventY;

    private GlwlLabelBean glwlLabelBean = GlwlLabelBean.getInstance();
    private GlwlLabelRectfBean glwlLabelRectfBean;

    Paint mPaint = null;
    TextPaint txtPaint = null;
    private BaseDaoImp boardDao;
    private Paint dPaint;
    private TextPaint gqtxtPaint;

    public GlwlLabelView(Context context) {
        this(context,null);
    }

    public GlwlLabelView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }

    public GlwlLabelView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr,0);
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public GlwlLabelView(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    @SuppressLint("ResourceAsColor")
    private void init() {
        txtPaint = new TextPaint();
        txtPaint.setAntiAlias(true);
        //txtPaint.setStrokeWidth(DisplayUtil.dip2sp(mCtx,2));
        txtPaint.setTextAlign(Paint.Align.CENTER);
        //txtPaint.setTextSize(DisplayUtil.dip2sp(mCtx,24));

        gqtxtPaint = new TextPaint();
        gqtxtPaint.setAntiAlias(true);
        gqtxtPaint.setTextAlign(Paint.Align.LEFT);
        gqtxtPaint.setTextSize(18);

        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        //mPaint.setColor(0x7aff0000);
        mPaint.setColor(0xff000000);//0x7a008080 IED_BG_COLOR
        mPaint.setStrokeWidth(2);
        //mPaint.setStyle(Paint.Style.FILL);
        mPaint.setStyle(Paint.Style.STROKE);

        dPaint = new Paint();//画背景颜色
        dPaint.setAntiAlias(true);
        dPaint.setColor(R.color.green_color);
        dPaint.setStyle(Paint.Style.FILL);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        if (glwlLabelBean == null){
            Toast.makeText(getContext(),"未找到两端连接装置",Toast.LENGTH_SHORT).show();
            return;
        }else {
            GlwlLabelBean.InfoBean infoBeanA = glwlLabelBean.getInfoBeanA();
            GlwlLabelBean.InfoBean infoBeanB = glwlLabelBean.getInfoBeanB();
            String glwlName = glwlLabelBean.getGlwlName();
            if (glwlLabelBean.getTag_type()!=null && glwlLabelBean.getTag_type() == 1){
                if (infoBeanA == null || infoBeanB ==null || TextUtils.isEmpty(glwlName) ||
                        TextUtils.isEmpty(infoBeanA.getStartUnit()) || TextUtils.isEmpty(infoBeanA.getEndUnit())||
                        TextUtils.isEmpty(infoBeanB.getStartUnit()) || TextUtils.isEmpty(infoBeanB.getEndUnit())){
                    Toast.makeText(getContext(),"未找到两端连接装置",Toast.LENGTH_SHORT).show();
                    return;
                }
            }
        }

        glwlLabelRectfBean = new GlwlLabelRectfBean();
        GlwlLabelRectfBean.CubicleRectFBean cubicleRectFBeanA = new GlwlLabelRectfBean.CubicleRectFBean();
        GlwlLabelRectfBean.CubicleRectFBean cubicleRectFBeanB = new GlwlLabelRectfBean.CubicleRectFBean();
        GlwlLabelRectfBean.InfoRectFBean infoRectFBeanA = new GlwlLabelRectfBean.InfoRectFBean();
        GlwlLabelRectfBean.InfoRectFBean infoRectFBeanB = new GlwlLabelRectfBean.InfoRectFBean();

        int screenWidth = Utils.getScreenWidth(getContext());
        int screenHeight = Utils.getScreenHeight(getContext());
        int navigationBarHeight = Utils.getNavigationBarHeight(getContext());//获取虚拟键盘高度

        float top = (screenHeight - navigationBarHeight - LABEL_HEIGHT * 2) / 2;
        //设置大小和值
        if (glwlLabelBean.getInfoBeanA()== null || glwlLabelBean.getInfoBeanB()== null){
            return;
        }
        glwlLabelRectfBean.setGlwlName(glwlLabelBean.getGlwlName());

        cubicleRectFBeanA.setCubicleName(glwlLabelBean.getCubicleNameA());
        cubicleRectFBeanA.setCubicleRectF(new RectF(Config.LABEL_LEFT,top,Config.LABEL_LEFT+Config.LABEL_WIDTH,top+ LABEL_HEIGHT));

        cubicleRectFBeanB.setCubicleName(glwlLabelBean.getCubicleNameB());
        cubicleRectFBeanB.setCubicleRectF(new RectF(screenWidth - Config.LABEL_LEFT - Config.LABEL_WIDTH,top,screenWidth - Config.LABEL_LEFT,top+ LABEL_HEIGHT));
        glwlLabelRectfBean.setCubicleRectFBeanA(cubicleRectFBeanA);
        glwlLabelRectfBean.setCubicleRectFBeanB(cubicleRectFBeanB);
        //上侧框
        RectF cubicleRectFA = cubicleRectFBeanA.getCubicleRectF();
        float upside_top = cubicleRectFA.top - Config.LABEL_TOP_HEIGHT - LABEL_INTERVAL;
        infoRectFBeanA.setCubicleRectF(new RectF(Config.LABEL_LEFT,upside_top,screenWidth - Config.LABEL_LEFT,upside_top + Config.LABEL_TOP_HEIGHT));
        infoRectFBeanA.setNo(glwlLabelBean.getInfoBeanA().getNo());
        infoRectFBeanA.setInfo(glwlLabelBean.getInfoBeanA().getInfo());
        infoRectFBeanA.setFr(glwlLabelBean.getInfoBeanA().getFr());
        infoRectFBeanA.setTo(glwlLabelBean.getInfoBeanA().getTo());
        infoRectFBeanA.setQrCode(glwlLabelBean.getInfoBeanA().getQrCode());
        glwlLabelRectfBean.setInfoRectFA(infoRectFBeanA);
        //下册框
        float downside_top = cubicleRectFA.bottom + LABEL_INTERVAL;
        infoRectFBeanB.setCubicleRectF(new RectF(Config.LABEL_LEFT,downside_top,screenWidth - Config.LABEL_LEFT,downside_top + Config.LABEL_TOP_HEIGHT));
        infoRectFBeanB.setNo(glwlLabelBean.getInfoBeanB().getNo());
        infoRectFBeanB.setInfo(glwlLabelBean.getInfoBeanB().getInfo());
        infoRectFBeanB.setFr(glwlLabelBean.getInfoBeanB().getFr());
        infoRectFBeanB.setTo(glwlLabelBean.getInfoBeanB().getTo());
        infoRectFBeanB.setQrCode(glwlLabelBean.getInfoBeanB().getQrCode());
        glwlLabelRectfBean.setInfoRectFB(infoRectFBeanB);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.scale(mScale, mScale);//缩放
        canvas.translate(0,-100);//gy added20200120
        //画左右2个屏柜
        txtPaint.setTextSize(20);
        txtPaint.setTextAlign(Paint.Align.CENTER);
        if (glwlLabelRectfBean == null){
            Toast.makeText(getContext(),"未找到两端连接装置",Toast.LENGTH_SHORT).show();
            return;
        }
        if (glwlLabelRectfBean.getCubicleRectFBeanA() == null || glwlLabelRectfBean.getCubicleRectFBeanB() == null
                || glwlLabelRectfBean.getInfoRectFA() == null || glwlLabelRectfBean.getInfoRectFB() == null){
            return;
        }
        canvas.drawRect(glwlLabelRectfBean.getCubicleRectFBeanA().getCubicleRectF(),mPaint);
        canvas.drawRect(glwlLabelRectfBean.getCubicleRectFBeanA().getCubicleRectF(),dPaint);
        DrawUtils.textCenter(glwlLabelRectfBean.getCubicleRectFBeanA().getCubicleName(),txtPaint,canvas,glwlLabelRectfBean.getCubicleRectFBeanA().getCubicleRectF(),
                Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);

        canvas.drawRect(glwlLabelRectfBean.getCubicleRectFBeanB().getCubicleRectF(),mPaint);
        canvas.drawRect(glwlLabelRectfBean.getCubicleRectFBeanB().getCubicleRectF(),dPaint);
        DrawUtils.textCenter(glwlLabelRectfBean.getCubicleRectFBeanB().getCubicleName(),txtPaint,canvas,glwlLabelRectfBean.getCubicleRectFBeanB().getCubicleRectF(),
                Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);
        //画连接线
        RectF cubicleRectFA = glwlLabelRectfBean.getCubicleRectFBeanA().getCubicleRectF();
        RectF cubicleRectFB = glwlLabelRectfBean.getCubicleRectFBeanB().getCubicleRectF();
        canvas.drawLine(cubicleRectFA.right,cubicleRectFA.top + cubicleRectFA.height()/2,cubicleRectFB.left,cubicleRectFA.top + cubicleRectFA.height()/2,mPaint);

        RectF cubicleRectFTop = glwlLabelRectfBean.getInfoRectFA().getCubicleRectF();
        RectF cubicleRectFBottom = glwlLabelRectfBean.getInfoRectFB().getCubicleRectF();
        RectF cubicleRectFLeft = glwlLabelRectfBean.getCubicleRectFBeanA().getCubicleRectF();
        RectF cubicleRectRight = glwlLabelRectfBean.getCubicleRectFBeanB().getCubicleRectF();
        canvas.drawText(glwlLabelRectfBean.getGlwlName(),cubicleRectFTop.centerX(),cubicleRectFLeft.top + Config.LABEL_HEIGHT/2 - 8,txtPaint);

        //画上下侧框
        canvas.drawRect(glwlLabelRectfBean.getInfoRectFA().getCubicleRectF(),mPaint);
        canvas.drawRect(glwlLabelRectfBean.getInfoRectFA().getCubicleRectF(),dPaint);
        canvas.drawRect(glwlLabelRectfBean.getInfoRectFB().getCubicleRectF(),mPaint);
        canvas.drawRect(glwlLabelRectfBean.getInfoRectFB().getCubicleRectF(),dPaint);
        //上下框文字
        GlwlLabelRectfBean.InfoRectFBean infoRectFA = glwlLabelRectfBean.getInfoRectFA();
        GlwlLabelRectfBean.InfoRectFBean infoRectFB = glwlLabelRectfBean.getInfoRectFB();
        if (glwlLabelBean.getTag_type() == 1){
            GlwlLabelBean.InfoBean infoBeanA = glwlLabelBean.getInfoBeanA();
            GlwlLabelBean.InfoBean infoBeanB = glwlLabelBean.getInfoBeanB();
            txtPaint.setTextAlign(Paint.Align.LEFT);

            if (!TextUtils.isEmpty(infoBeanA.getGlgq())){
//                canvas.drawText(infoBeanA.getGlName()+" S "+infoBeanA.getStartUnit()+" "+infoBeanA.getStartGS() + " " +infoBeanA.getStartPort() ,cubicleRectFTop.left+10,cubicleRectFTop.top + 30,txtPaint);
//                canvas.drawText(infoBeanA.getGlgq()+" E "+infoBeanA.getEndUnit()+" "+infoBeanA.getEndGS() + " " +infoBeanA.getEndPort() ,cubicleRectFTop.left+10,cubicleRectFTop.top + 60,txtPaint);
                drawInfo(canvas,infoBeanA,cubicleRectFTop,1);


//                canvas.drawText(infoBeanB.getGlName()+" S "+infoBeanB.getStartUnit()+" "+infoBeanB.getStartGS() + " " +infoBeanB.getStartPort() ,cubicleRectFBottom.left+10,cubicleRectFBottom.top + 30,txtPaint);
//                canvas.drawText(infoBeanB.getGlgq()+" E "+infoBeanB.getEndUnit()+" "+infoBeanB.getEndGS() + " " +infoBeanB.getEndPort() ,cubicleRectFBottom.left+10,cubicleRectFBottom.top + 60,txtPaint);
                drawInfo(canvas,infoBeanB,cubicleRectFBottom,1);
            }else {
//                canvas.drawText(infoBeanA.getGlName()+" S "+infoBeanA.getStartUnit()+" "+infoBeanA.getStartGS() + " " +infoBeanA.getStartPort() ,cubicleRectFTop.left+10,cubicleRectFTop.top + 30,txtPaint);
//                canvas.drawText(" E "+infoBeanA.getEndUnit()+" "+infoBeanA.getEndGS() + " " +infoBeanA.getEndPort() ,cubicleRectFTop.left+10,cubicleRectFTop.top + 60,txtPaint);
                drawInfo(canvas,infoBeanA,cubicleRectFTop,2);

//                canvas.drawText(infoBeanB.getGlName()+" S "+infoBeanB.getStartUnit()+" "+infoBeanB.getStartGS() + " " +infoBeanB.getStartPort() ,cubicleRectFBottom.left+10,cubicleRectFBottom.top + 30,txtPaint);
//                canvas.drawText(" E "+infoBeanB.getEndUnit()+" "+infoBeanB.getEndGS() + " " +infoBeanB.getEndPort() ,cubicleRectFBottom.left+10,cubicleRectFBottom.top + 60,txtPaint);
                drawInfo(canvas,infoBeanB,cubicleRectFBottom,2);
            }
        }else {
            txtPaint.setTextAlign(Paint.Align.LEFT);
            canvas.drawText("No:"+infoRectFA.getNo(),cubicleRectFTop.left+10,cubicleRectFTop.top + 30,txtPaint);
            if (TextUtils.isEmpty(infoRectFA.getInfo())){
                canvas.drawText("Fr:"+infoRectFA.getFr(),cubicleRectFTop.left+10,cubicleRectFTop.top + 60,txtPaint);
                canvas.drawText("To:"+infoRectFA.getTo(),cubicleRectFTop.left+10,cubicleRectFTop.top + 90,txtPaint);
            }else {
                canvas.drawText("Info:"+infoRectFA.getInfo(),cubicleRectFTop.left+10,cubicleRectFTop.top + 60,txtPaint);
                canvas.drawText("Fr:"+infoRectFA.getFr(),cubicleRectFTop.left+10,cubicleRectFTop.top + 90,txtPaint);
                canvas.drawText("To:"+infoRectFA.getTo(),cubicleRectFTop.left+10,cubicleRectFTop.top + 120,txtPaint);
            }
            canvas.drawText("No:"+infoRectFB.getNo(),cubicleRectFBottom.left+10,cubicleRectFBottom.top + 30,txtPaint);
            if (TextUtils.isEmpty(infoRectFB.getInfo())){
                canvas.drawText("Fr:"+infoRectFB.getFr(),cubicleRectFBottom.left+10,cubicleRectFBottom.top + 60,txtPaint);
                canvas.drawText("To:"+infoRectFB.getTo(),cubicleRectFBottom.left+10,cubicleRectFBottom.top + 90,txtPaint);
            }else {
                canvas.drawText("Info:"+infoRectFB.getInfo(),cubicleRectFBottom.left+10,cubicleRectFBottom.top + 60,txtPaint);
                canvas.drawText("Fr:"+infoRectFB.getFr(),cubicleRectFBottom.left+10,cubicleRectFBottom.top + 90,txtPaint);
                canvas.drawText("To:"+infoRectFB.getTo(),cubicleRectFBottom.left+10,cubicleRectFBottom.top + 120,txtPaint);
            }
        }
        //画二维码
        if (!TextUtils.isEmpty(infoRectFA.getQrCode())){
            Bitmap qrCodeA = QRCode.createQRCode(infoRectFA.getQrCode(),130);
            canvas.drawBitmap(qrCodeA,cubicleRectFTop.right - 140,cubicleRectFTop.top + 10,mPaint);
        }
        if (!TextUtils.isEmpty(infoRectFB.getQrCode())){
            Bitmap qrCodeB = QRCode.createQRCode(infoRectFB.getQrCode(),130);
            canvas.drawBitmap(qrCodeB,cubicleRectFBottom.right - 140,cubicleRectFBottom.top + 10,mPaint);
        }

        //画上下框与中间线的连线
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawLine(cubicleRectFLeft.right + 50,cubicleRectFLeft.top + Config.LABEL_HEIGHT/2,cubicleRectFLeft.right + 50,cubicleRectFTop.bottom,mPaint);
        Path arrow = getArrow(cubicleRectFLeft.right + 50, cubicleRectFLeft.top + LABEL_HEIGHT / 2, false, LABEL_ARROW_WIDTH, Config.LABEL_ARROW_HEIGH);
        canvas.drawPath(arrow,mPaint);

        canvas.drawLine(cubicleRectRight.left - 50,cubicleRectRight.top + Config.LABEL_HEIGHT/2,cubicleRectRight.left - 50,cubicleRectFBottom.top,mPaint);
        Path arrow1 = getArrow(cubicleRectRight.left - 50,cubicleRectRight.top + Config.LABEL_HEIGHT/2, true, LABEL_ARROW_WIDTH, Config.LABEL_ARROW_HEIGH);
        canvas.drawPath(arrow1,mPaint);
        mPaint.setStyle(Paint.Style.STROKE);
    }

    private void drawInfo(Canvas canvas, GlwlLabelBean.InfoBean infoBeanA, RectF cubicleRectFTop, int type){
//        canvas.drawText(infoBeanA.getGlName()+"",cubicleRectFTop.left+10,cubicleRectFTop.top + 30,gqtxtPaint);
//        canvas.drawText("S",cubicleRectFTop.left+120,cubicleRectFTop.top + 30,gqtxtPaint);
//        canvas.drawText(infoBeanA.getStartUnit()+"",cubicleRectFTop.left+140,cubicleRectFTop.top + 30,gqtxtPaint);
//        canvas.drawText(infoBeanA.getStartGS()+"",cubicleRectFTop.left+450,cubicleRectFTop.top + 30,gqtxtPaint);
//        canvas.drawText(infoBeanA.getStartPort()+"",cubicleRectFTop.left+490,cubicleRectFTop.top + 30,gqtxtPaint);
//
//        if (type == 1){
//            canvas.drawText(infoBeanA.getGlgq()+"",cubicleRectFTop.left+10,cubicleRectFTop.top + 60,gqtxtPaint);
//        }
//        canvas.drawText("E",cubicleRectFTop.left+120,cubicleRectFTop.top + 60,gqtxtPaint);
//        canvas.drawText(infoBeanA.getEndUnit()+"",cubicleRectFTop.left+140,cubicleRectFTop.top + 60,gqtxtPaint);
//        canvas.drawText(infoBeanA.getEndGS()+"",cubicleRectFTop.left+450,cubicleRectFTop.top + 60,gqtxtPaint);
//        canvas.drawText(infoBeanA.getEndPort()+"",cubicleRectFTop.left+490,cubicleRectFTop.top + 60,gqtxtPaint);

        //修改
        int length = 0;//光缆名称
        int length1 = 0;//起点装置名称
        int length11 = 0;//终点装置名称
        int length2 = 0;//
        if (!TextUtils.isEmpty(infoBeanA.getGlName())){
            length = infoBeanA.getGlName().length();
        }
        if (!TextUtils.isEmpty(infoBeanA.getStartUnit())){
            length1 = infoBeanA.getStartUnit().length();
        }
        if (!TextUtils.isEmpty(infoBeanA.getEndUnit())){
            length11 = infoBeanA.getEndUnit().length();
        }
        if (!TextUtils.isEmpty(infoBeanA.getStartGS())){
            length2 = infoBeanA.getStartGS().length();
        }
        int length0;
        if (length1>length11){
            length0 = length1;
        }else{
            length0 = length11;
        }

        canvas.drawText(infoBeanA.getGlName()+"",cubicleRectFTop.left+10,cubicleRectFTop.top + 30,gqtxtPaint);
        canvas.drawText("S",cubicleRectFTop.left+length*14,cubicleRectFTop.top + 30,gqtxtPaint);
        canvas.drawText(infoBeanA.getStartUnit()+"",cubicleRectFTop.left+length*14+20,cubicleRectFTop.top + 30,gqtxtPaint);
        canvas.drawText(infoBeanA.getStartGS()+"",cubicleRectFTop.left+(length+length0)*15+20,cubicleRectFTop.top + 30,gqtxtPaint);
        canvas.drawText(infoBeanA.getStartPort()+"",cubicleRectFTop.left+(length+length0+length2)*15+60,cubicleRectFTop.top + 30,gqtxtPaint);

        if (type == 1){
            canvas.drawText(infoBeanA.getGlgq()+"",cubicleRectFTop.left+10,cubicleRectFTop.top + 60,gqtxtPaint);
        }
        canvas.drawText("E",cubicleRectFTop.left+length*14,cubicleRectFTop.top + 60,gqtxtPaint);
        canvas.drawText(infoBeanA.getEndUnit()+"",cubicleRectFTop.left+length*14+20,cubicleRectFTop.top + 60,gqtxtPaint);
        canvas.drawText(infoBeanA.getEndGS()+"",cubicleRectFTop.left+(length+length0)*15+20,cubicleRectFTop.top + 60,gqtxtPaint);
        canvas.drawText(infoBeanA.getEndPort()+"",cubicleRectFTop.left+(length+length0+length2)*15+60,cubicleRectFTop.top + 60,gqtxtPaint);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        PointF curPointF = new PointF(event.getX(),event.getY());

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                /*eventX = (int) event.getX()-mWidth/2;
                eventY = (int) event.getY()-mHeight/2;*/
                eventX = (int) event.getX();
                eventY = (int) event.getY();
                Log.e("out for", "eventX,eventY="+eventX+" , "+eventY);
                try {
                    synchronized (event) {
                        /*for(ExtIedBean extIed : extIedBeans){
                            if(extIed.isOnTouch(eventX, eventY, totalOffX, totalOffY, mScale)){
                                //mPaint.setColor(Color.RED);
                                //双击触发 跳转操作
                                doubleClickChangeIED(extIed);
                                break;
                            }
                        }*/
                    }
                } catch (Exception e) {
                    Log.e(TAG, e.toString());
                }

                //当点击到插件上，双击弹出对话框
                doubleClickOnPlugSvg();

                break;

            default:
                break;
        }
        DargAndZoom(event);
        invalidate();
        return true;
        //return super.onTouchEvent(event);
    }


    //Dragging
    private static final int NONE = 0;
    private static final int DRAG = 1;
    private static final int ZOOM = 2;
    private int mode = NONE;

    private PointF startPoint = new PointF();
    private PointF middlePoint = new PointF();

    private float oriDis = 1f;//初始的俩指触点间距
    private float mScale = 1.0f;
    private float mPreScale = 1.0f;
    private float mMinScale = 0.2f;
    private float mMaxScale = 3.0f;

    private void DargAndZoom(MotionEvent event){
        //middlePoint = null;
        switch (event.getAction()&event.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
                startPoint.set(event.getX(), event.getY());
                mode = DRAG;
                break;
            case MotionEvent.ACTION_POINTER_DOWN:
                oriDis = DrawUtils.distance(event);
                if (oriDis > 10f) {
                    middlePoint = DrawUtils.mid(event,null);
                    mode = ZOOM;
                }

                break;
            case MotionEvent.ACTION_POINTER_UP:
                mPreScale = mScale;
                //if (event.getPointerCount()<2) mode = DRAG;
                break;

            case MotionEvent.ACTION_MOVE:

                if (mode == DRAG) {
                    onDrag(event);
                } else if((mode == ZOOM)){
                    onZoom(event);
                }
                if (mode == DRAG) {
                    startPoint.set(event.getX(), event.getY());
                }
                getParent().requestDisallowInterceptTouchEvent(true);
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                mode = NONE;
                break;
            default:
                break;
        }
    }

    private float totalOffX = 0;
    private float totalOffY = 0;
    float offX = 0;
    float offY = 0;
    private void onDrag(MotionEvent event) {
        //边界处理：(以中心IED的中心不超过View边界为参考)
        offX = event.getX() - startPoint.x;
        offY = event.getY() - startPoint.y;

        scrollBy(-(int)offX, -(int)offY);
        totalOffX += offX;
        totalOffY += offY;
        //mMatrix.setTranslate(totalOffX,totalOffY);
    }

    /**
     * 把View上的点的坐标转化为Canvas上的坐标点 for拾取
     * @return
     */
    //float newDist = 0;
    private void onZoom(MotionEvent event) {
        float newDist = DrawUtils.distance(event);
        if (newDist > 10f) {
            float scale = newDist/oriDis;

            mScale = scale * mPreScale;
            if (mScale > mMaxScale) {
                mScale = mMaxScale;
            }
            if (mScale < mMinScale) {
                mScale = mMinScale;
            }
        }
    }


    //存储时间的数组
    long[] mHitsPlugSvg = new long[2];
    private void doubleClickOnPlugSvg() {
        System.arraycopy(mHitsPlugSvg, 1, mHitsPlugSvg, 0, mHitsPlugSvg.length - 1);
        mHitsPlugSvg[mHitsPlugSvg.length - 1] = SystemClock.uptimeMillis();
        //双击事件的时间间隔500ms
        if (mHitsPlugSvg[0] >= (SystemClock.uptimeMillis() - Constants.CONSTANT_DOUBLE_CLICK_INTERVAL_MS)) {

        }
    }

    private Path getArrow(float vertexX, float vertexY, boolean isUp, float w, float h){
        Path path = new Path();
        path.moveTo(vertexX,vertexY);
        if (isUp){
            path.lineTo(vertexX+w/2f,vertexY+h);
            path.lineTo(vertexX-w/2f,vertexY+h);
        } else {
            path.lineTo(vertexX-w/2f,vertexY-h);
            path.lineTo(vertexX+w/2f,vertexY-h);
        }
        path.close();

        return path;
    }
}
