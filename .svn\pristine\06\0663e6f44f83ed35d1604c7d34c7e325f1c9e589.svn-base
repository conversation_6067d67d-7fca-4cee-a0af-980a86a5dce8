package com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool;

import android.app.Activity;
import android.content.Context;
import android.graphics.RectF;

import com.baseres.base.PubApp;
import com.j256.ormlite.stmt.QueryBuilder;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_REGION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_SUBSTATION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDao;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.vir_real_circuit.PortCtrlBlockListCanvas;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.vir_real_circuit.VR_CONTANTS;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.vir_real_circuit_v2.VR_CONTANTS_V2;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.XshlUtils;
import com.share.mycustomviewlib.bean.XshlBean;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *功能：虚实回路数据处理单例类
 *Created by RuanJian-GuoYong on 2019/9/5 9:09
 */
public class VirRealDataPool {
    private static final String TAG = "VirRealDataPool";
    private static VirRealDataPool sInstance = null;
    private Context mCtx = null;

    BaseDao<KM_SPCD_CABLE,Integer> cableDao = null;
    BaseDao<KM_SPCD_CORE,Integer> coreDao = null;
    BaseDaoImp<KM_SPCD_UNIT,Integer> unitDao = null;
    BaseDaoImp<KM_SPCD_BOARD,Integer> boardDao = null;
    BaseDaoImp<KM_SPCD_PORT,Integer> portDao = null;
    BaseDaoImp<KM_SPCD_CUBICLE,Integer> cubicleDao = null;
    BaseDaoImp<KM_SPCD_REGION,Integer> regionDao = null;
    BaseDaoImp<KM_SPCD_SUBSTATION,Integer> substationDao =null;
    BaseDaoImp<KM_SPCD_INTCORE,Integer> intCoreDao=null;

    public KM_SPCD_UNIT centIED = null;
    public List<KM_SPCD_UNIT> extIEDs = new ArrayList<>();
    public Map<String,List<Map<String,Object>>> mapsList = null;
    private float width;
    public List<RectF> extIedRectFs;
    public RectF cenIedRectF;
    float extIed_height_sum = 0;//所有右侧ied的高度总和（不含垂直间距）

    //真实数据
    public static final boolean USE_TEST_DATA = false;
    public XshlBean xshlBean;

    private VirRealDataPool() {
    }

    public synchronized static VirRealDataPool getInstance() {
        if (sInstance == null) {
            sInstance = new VirRealDataPool();
        }
        return sInstance;
    }

    //初始化数据--传入左侧中心IED-ID
    public void setCenIed(Context context, int cenIedId) throws SQLException {
        mCtx = context;
        initDaos(mCtx);
        centIED = unitDao.getById(cenIedId);
        extIEDs = unitDao.getDao().queryBuilder().where().le("id",3).query();

        mapsList = new HashMap<>();
        //测试数据 initMockedData();
        for (KM_SPCD_UNIT unit : extIEDs){
            List<Map<String,Object>> maps = new ArrayList<>();
            for (int i = 0; i < 3; i++) {
                Map<String,Object> map = new HashMap<>();
                map.put("leftBoardSlot","7");
                map.put("leftPortInfo","3-Tx");
                map.put("rightBoardSlot","3");
                map.put("rightPortInfo","5-Rx");
                map.put("isGs",false);
                map.put("appID","000"+i);
                map.put("direction", PortCtrlBlockListCanvas.Direction.valueOf("L2R"));
                maps.add(map);
            }
            mapsList.put(unit.getIed_name(), maps);
        }

        //真实数据
        if (USE_TEST_DATA) return;
        extIEDs.clear();
        mapsList.clear();
        String dbName = PubApp.getDbName();
        xshlBean = XshlUtils.getXshlData((Activity) context,dbName,cenIedId,1);
        if (xshlBean == null) {
            return;
        }
        centIED = unitDao.getById(xshlBean.getLeftUnit().getId());
        List<XshlBean.RightUnitConnect> ll = xshlBean.getList();
        for (XshlBean.RightUnitConnect rightUnitConnect : ll){
            //extIEDs.add(unitDao.getById(rightUnitConnect.getId()));

            List<Map<String,Object>> maps = new ArrayList<>();

            List<XshlBean.RightConnect> rightConnects = rightUnitConnect.getList();
            for (int i = 0; i < rightConnects.size(); i++) {
                XshlBean.RightConnect rightConnect = rightConnects.get(i);

                Map<String,Object> map = new HashMap<>();
                map.put("leftBoardSlot",rightConnect.getBoardA());
                map.put("leftPortInfo",rightConnect.getPortA());
                map.put("rightBoardSlot",rightConnect.getBoardB());
                map.put("rightPortInfo",rightConnect.getPortB());
                //map.put("isGs",rightConnect.getGoose().startsWith("G"));
                map.put("isGs",(rightConnect.getGoose()==null?"":rightConnect.getGoose()).startsWith("G"));
                map.put("appID",rightConnect.getAppId());
                String dir = "NONE";
                if (rightConnect.getDirection() ==1) dir = "L2R";
                else if (rightConnect.getDirection() ==1) dir = "R2L";
                map.put("direction", PortCtrlBlockListCanvas.Direction.valueOf(dir));
                maps.add(map);
            }
            mapsList.put(rightUnitConnect.getIedName(), maps);
        }

    }

    public void setCenIedPort(Context context, int cenIedPortId) throws SQLException {
        this.mCtx = context;
        initDaos(mCtx);

        QueryBuilder<KM_SPCD_UNIT, Integer> unitQB = unitDao.getDao().queryBuilder();
        QueryBuilder<KM_SPCD_BOARD, Integer> boardQB = boardDao.getDao().queryBuilder();
        QueryBuilder<KM_SPCD_PORT, Integer> portQB = portDao.getDao().queryBuilder();
        portQB.where().eq("id", cenIedPortId);
        // join with the order query
        KM_SPCD_UNIT result = unitQB
                .join("id","unit_id", boardQB
                        .join("id","board_id",portQB))
                .queryForFirst();

        centIED = result;
        extIEDs = unitDao.getDao().queryBuilder().where().eq("id",6).query();

        mapsList = new HashMap<>();
        //测试数据 initMockedData();
        for (KM_SPCD_UNIT unit : extIEDs){
            List<Map<String,Object>> maps = new ArrayList<>();
            for (int i = 0; i < 3; i++) {
                Map<String,Object> map = new HashMap<>();
                map.put("leftBoardSlot","7");
                map.put("leftPortInfo","3-Tx");
                map.put("rightBoardSlot","3");
                map.put("rightPortInfo","5-Rx");
                map.put("isGs",false);
                map.put("appID","000"+i);
                map.put("direction", PortCtrlBlockListCanvas.Direction.valueOf("L2R"));
                maps.add(map);
            }
            mapsList.put(unit.getIed_name(), maps);
        }

        //真实数据
        if (USE_TEST_DATA) return;
        mapsList.clear();
        extIEDs.clear();
        String dbName = PubApp.getDbName();
        xshlBean = XshlUtils.getXshlData((Activity) context,dbName,cenIedPortId,3);
        if (xshlBean == null){
            return;
        }
        centIED = unitDao.getById(xshlBean.getLeftUnit().getId());
        List<XshlBean.RightUnitConnect> ll = xshlBean.getList();
        for (XshlBean.RightUnitConnect rightUnitConnect : ll){
            extIEDs.add(unitDao.getById(rightUnitConnect.getId()));

            List<Map<String,Object>> maps = new ArrayList<>();

            List<XshlBean.RightConnect> rightConnects = rightUnitConnect.getList();
            for (int i = 0; i < rightConnects.size(); i++) {
                XshlBean.RightConnect rightConnect = rightConnects.get(i);

                Map<String,Object> map = new HashMap<>();
                map.put("leftBoardSlot",rightConnect.getBoardA());
                map.put("leftPortInfo",rightConnect.getPortA());
                map.put("rightBoardSlot",rightConnect.getBoardB());
                map.put("rightPortInfo",rightConnect.getPortB());
                if (rightConnect.getGoose() !=null){
                    map.put("isGs",rightConnect.getGoose().startsWith("G"));
                }else {
                    map.put("isGs",false);
                }
                map.put("appID",rightConnect.getAppId());
                String dir = "NONE";
                if (rightConnect.getDirection() ==1) dir = "L2R";
                else if (rightConnect.getDirection() ==1) dir = "R2L";
                map.put("direction", PortCtrlBlockListCanvas.Direction.valueOf(dir));
                maps.add(map);
            }
            mapsList.put(rightUnitConnect.getIedName(), maps);
        }
    }

    public void initRects(){
        extIed_height_sum = 0;
        //初始化 外侧ied的rect:
        extIedRectFs = getExtIedRectFs();
        cenIedRectF = getCenIedRectF();
    }

    public void setViewWidth(float width){
        this.width = width;
    }
    private void initDaos(Context mCtx) {
        String dbName = PubApp.getDbName();
        //String dbName = PrefReader.getInstance(mCtx).get(null);
        if(unitDao==null)unitDao = new BaseDaoImp(mCtx, KM_SPCD_UNIT.class, dbName);
        if(boardDao==null)boardDao = new BaseDaoImp(mCtx, KM_SPCD_BOARD.class, dbName);
        if(portDao==null)portDao = new BaseDaoImp(mCtx, KM_SPCD_PORT.class, dbName);
        if(cableDao==null)cableDao = new BaseDaoImp(mCtx, KM_SPCD_CABLE.class, dbName);
        if(coreDao==null)coreDao = new BaseDaoImp(mCtx, KM_SPCD_CORE.class, dbName);
        if(cubicleDao==null)cubicleDao = new BaseDaoImp(mCtx, KM_SPCD_CUBICLE.class, dbName);
        if(regionDao==null)regionDao = new BaseDaoImp(mCtx, KM_SPCD_REGION.class, dbName);
        if(substationDao==null)substationDao = new BaseDaoImp(mCtx, KM_SPCD_SUBSTATION.class, dbName);
        if(intCoreDao==null)intCoreDao = new BaseDaoImp(mCtx, KM_SPCD_INTCORE.class, dbName);
    }

    //获取右侧N个extIED 的rect
    public List<RectF> getExtIedRectFs(){
        List<RectF> list = new ArrayList<>();
        float left = 0;
        float top = 0;
        float right = 0;
        float bottom = 0;

        right = width - VR_CONTANTS_V2.IED_PADDING_RIGHT;
        left = right - VR_CONTANTS_V2.IED_WIDTH;

        for (int i = 0; i < (USE_TEST_DATA?extIEDs.size():xshlBean.getList().size()); i++) {
            /*int cb_num_test = mapsList.get(extIEDs.get(i).getIed_name()).size();
            int cb_num_real = mapsList.get(xshlBean.getList().get(i).getIedName()).size();*/
            int cb_num = USE_TEST_DATA ?
                    mapsList.get(extIEDs.get(i).getIed_name()).size():
                    mapsList.get(xshlBean.getList().get(i).getIedName()).size();
            float height_i = VR_CONTANTS_V2.IED_TYPE_HEIGHT
                    + VR_CONTANTS_V2.BOARD_SLOT_PADING_TOP
                    + VR_CONTANTS_V2.ITEM_HEIGHT * cb_num
                    + VR_CONTANTS_V2.BOARD_SLOT_PADING_BOTTOM;
            if (height_i < VR_CONTANTS_V2.IED_MIN_HEIGHT)
                height_i = VR_CONTANTS_V2.IED_MIN_HEIGHT;

            if (i == 0){
                top = VR_CONTANTS_V2.IED_PADDING_TOP;
                bottom = top + height_i;
            } else {
                top = bottom + VR_CONTANTS_V2.IED_SPACING_VERTICAL;
                bottom = top + height_i;
            }
            extIed_height_sum += height_i;
            RectF rectF = new RectF(left,top,right,bottom);
            list.add(rectF);
        }
        //获取第N个IED中的控制块数量
        return list;
    }

    RectF getCenIedRectF(){
        int size = USE_TEST_DATA?extIEDs.size():xshlBean.getList().size();
        if (size == 0){
            return new RectF(
                    VR_CONTANTS_V2.IED_PADDING_RIGHT,
                    VR_CONTANTS_V2.IED_PADDING_TOP,
                    VR_CONTANTS_V2.IED_PADDING_RIGHT+VR_CONTANTS_V2.IED_WIDTH,
                    VR_CONTANTS_V2.IED_PADDING_TOP+VR_CONTANTS_V2.IED_MIN_HEIGHT);
        }
        return new RectF(
                VR_CONTANTS_V2.IED_PADDING_RIGHT,
                VR_CONTANTS_V2.IED_PADDING_TOP,
                VR_CONTANTS_V2.IED_PADDING_RIGHT+VR_CONTANTS_V2.IED_WIDTH,
                VR_CONTANTS_V2.IED_PADDING_TOP+extIed_height_sum+(size-1)*VR_CONTANTS_V2.IED_SPACING_VERTICAL);
    }

    //获取右侧第N个extIED 连接到中心centreIED的RectF
    public RectF getNthExt2CentreIedRectF(int N){
        RectF rectF = new RectF(1,1,1,1);
        //获取第N个IED中的控制块数量
        return rectF;
    }
}
