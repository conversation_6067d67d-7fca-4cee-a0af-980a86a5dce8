package com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.optical_fiber_tag;

import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.util.Log;

import com.baseres.base.PubApp;
import com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.DeviceBackPanelFragmentNew;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDao;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class GetTagInfoThread extends Thread {
    private static final String TAG = "GetTagInfoThread";
    Context mCtx;

    int[] portIds;
    public List<BaseTagInfo> tags = new ArrayList<>();
    boolean isRunning = false;

    Handler mHandler;

    //others
    BaseDao<KM_SPCD_PORT, Integer> portDao;
    BaseDao<KM_SPCD_BOARD, Integer> boardDao;
    BaseDao<KM_SPCD_UNIT, Integer> unitDao;

    /***
     *
     * @param mCtx
     * @param mHandler
     * @param portIds 可以传入ied的一对端口，也可以传odf的打单个端口。
     */
    public GetTagInfoThread(Context mCtx, Handler mHandler,int[] portIds) {
        this.mCtx = mCtx;
        this.mHandler = mHandler;

        this.portIds = portIds;

        StringBuffer strbf_ports = new StringBuffer("GetTagInfosThread");
        for (int portId : portIds){
            strbf_ports.append(portId);
        }
        this.setName(strbf_ports.toString());

        initDao();
    }

    private void initDao() {
        String dbName = PubApp.getDbName();
        portDao = new BaseDaoImp(mCtx, KM_SPCD_PORT.class, dbName);
        boardDao = new BaseDaoImp(mCtx, KM_SPCD_BOARD.class, dbName);
        unitDao = new BaseDaoImp(mCtx, KM_SPCD_UNIT.class, dbName);
    }

    @Override
    public void run() {
        mHandler.sendEmptyMessage(DeviceBackPanelFragmentNew.MSG_PORT_DATA_HANDLING);
        isRunning = true;
        try {
            sleep(300);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        long t0 = System.currentTimeMillis();
        int USER_TYPE = PubApp.sp.getInt("tag_type",FiberTagsAdapter.USER_TYPE_GB);
        try {
            tags.clear();
            //for debug
            String debugs = "";
            for (int i:portIds){
                debugs += i+",";
            }
            Log.e(TAG, "mockQrCodeInfo: portIds="+ debugs);
            if (portIds.length<1) {
                mHandler.sendEmptyMessage(DeviceBackPanelFragmentNew.MSG_PORT_DATA_HANDLE_CANCEL);
                isRunning = false;
                Log.e(TAG, "run: 传入的端口数组为空，取消找标签数据！" );
                return;
            }

            KM_SPCD_UNIT unit_beyondTO = unitDao.getById(boardDao.getById(portDao.getById(portIds[0]).getBoard_id()).getUnit_id());

            if (USER_TYPE==FiberTagsAdapter.USER_TYPE_SHAANXI){
                for (int portId:portIds){
                    if (unit_beyondTO.getDev_class().equals("ODF")){
                        List<TagInfoShaanxi> tagInfoShaanxis = TagInfoUtils.mockQrCodeInfos(mCtx, portId);
                        if (tagInfoShaanxis == null) {
                            continue;
                        }
                        tags.addAll(tagInfoShaanxis);
                    } else {
                        TagInfoShaanxi tagInfoShaanxi = TagInfoUtils.mockQrCodeInfo(mCtx, portId);
                        if (tagInfoShaanxi==null) continue;
                        tags.add(tagInfoShaanxi);
                    }
                }
            }
            if (USER_TYPE==FiberTagsAdapter.USER_TYPE_GB){
                for (int portId:portIds){
                    if (unit_beyondTO.getDev_class().equals("ODF")){
                        List<TagInfoGB> tagInfoGBs = TagInfoUtils.mockQrCodeInfosGB(mCtx, portId);
                        if (tagInfoGBs == null) {
                            continue;
                        }
                        tags.addAll(tagInfoGBs);
                    } else {
                        TagInfoGB tagInfoShaanxi = TagInfoUtils.mockQrCodeInfoGB(mCtx, portId);
                        if (tagInfoShaanxi==null) continue;
                        tags.add(tagInfoShaanxi);
                    }
                }
            }

            if (tags == null||tags.isEmpty()){
                mHandler.sendEmptyMessage(DeviceBackPanelFragmentNew.MSG_PORT_DATA_HANDLE_CANCEL);
            }
            mHandler.sendEmptyMessage(DeviceBackPanelFragmentNew.MSG_PORT_DATA_HANDLED);
            Message msg = new Message();
            msg.what = DeviceBackPanelFragmentNew.MSG_PORT_DATA_HANDLED;
            msg.obj = tags;
            mHandler.sendMessage(msg);
        } catch (SQLException e) {
            e.printStackTrace();
            mHandler.sendEmptyMessage(DeviceBackPanelFragmentNew.MSG_PORT_DATA_HANDLE_CANCEL);
        }
        isRunning = false;
        long t1 = System.currentTimeMillis();
        Log.d(TAG, String.format("It takes time : <%s><耗时%dms>","标签信息数据处理",t1-t0));
    }
}
