package com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ExpandableListView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.baseres.base.PubApp;
import com.kemov.parsescl.KIEDModel;
import com.kemov.sclaata.common.app.PubUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.CommonAdapterHelper.CommonAdapter;
import com.kemov.visual.spcd.spcdvisualandroidapp.CommonAdapterHelper.ViewHolder;
import com.kemov.visual.spcd.spcdvisualandroidapp.R;
import com.kemov.visual.spcd.spcdvisualandroidapp.activity.ied_whole_circuit.HandleForWholeCircuitsFix3;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.GoosePortBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.UnitConnectBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.WholeCircuitBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Utils;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.XshlUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.visualview.DeviceBackPanelView;
import com.share.mycustomviewlib.bean.DataExchangeCachePool;
import com.share.mycustomviewlib.bean.VirExtRefBean;
import com.share.mycustomviewlib.bean.VirFCDABean;
import com.share.mycustomviewlib.bean.XshlBean;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

//装置背板图的界面
@Deprecated
public class DeviceBackPanelFragment extends Fragment implements View.OnClickListener{
    private static final String TAG = "DeviceBackPanelFragment";

    public final static int PORT_UNIT_TYPE_IED = 1;
    public final static int PORT_UNIT_TYPE_ODF = 2;
    public int PORT_UNIT_TYPE = PORT_UNIT_TYPE_ODF;
    public final static int MSG_PORT_DATA_HANDLING = 1;
    public final static int MSG_PORT_DATA_HANDLED = 2;
    public final static int MSG_PORT_DATA_HANDLE_CANCEL = 3;

    int unitId = -1;
    int highLightPortId = -1;
    protected Context mCtx;
    View rootView;
    DeviceBackPanelView device_back_view = null;
    private ImageView mapBiggerView0, mapSmallerView0, mapOriginalView0, mapOriginalViewFilled0;//缩放适配

    RelativeLayout device_back;
    private TextView WhiteSpaceText;
    LinearLayout port_info_list;
    ExpandableListView expandableListView;
    ListView listView;

    PortChangedBroadcastReceiver portChangedBroadcastReceiver = null;

    private Handler handler;
    ProgressDialog progressDialog;


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mCtx = getActivity();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        rootView = inflater.inflate(R.layout.device_backpanel_layout, container, false);

        //注册广播 监听装置背板图双击的端口id
        IntentFilter intentFilter = new IntentFilter("ACTION_PORT_ID_CHANGE");
        portChangedBroadcastReceiver = new PortChangedBroadcastReceiver();
        this.getActivity().registerReceiver(portChangedBroadcastReceiver, intentFilter);

        handler = new Handler(){
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                switch(msg.what){
                    case MSG_PORT_DATA_HANDLING:
                        //progressDialog.setTitle("获取端口数据中...");
                        progressDialog.setIcon(R.mipmap.ic_launcher_round);
                        progressDialog.setMessage("获取端口数据中...");
                        progressDialog.setCancelable(false);
                        progressDialog.setProgressStyle(ProgressDialog.STYLE_SPINNER);
                        progressDialog.show();
                        break;
                    case MSG_PORT_DATA_HANDLED:
                        timer.cancel();
                        progressDialog.dismiss();
                        if (PORT_UNIT_TYPE == PORT_UNIT_TYPE_IED){
                            if (portGoose != null && portGoose.getGooseBeans()!=null){
                                portAboutDataChangeNotice();//更新列表数据
                                //有数据才打开分屏界面
                                if (!isBottomViewShow()) showBottomView(true);
                                //rootView.requestLayout();
                            }else {
                                if (isBottomViewShow()) showBottomView(false);
                                Utils.showLongToast(mCtx, "未找到控制块");
                            }
                        }
                        if (PORT_UNIT_TYPE == PORT_UNIT_TYPE_ODF){
                            if (odfLinks != null){
                                portAboutDataChangeNotice();//更新列表数据
                                //有数据才打开分屏界面
                                if (!isBottomViewShow()) showBottomView(true);
                                //rootView.requestLayout();
                            }else {
                                if (isBottomViewShow()) showBottomView(false);
                                Utils.showLongToast(mCtx, "未找到ODF链接信息");
                            }
                        }

                        break;
                    case MSG_PORT_DATA_HANDLE_CANCEL:
                        if (thread_getCbVrLinks.getState()!= Thread.State.TERMINATED){
                            thread_getCbVrLinks.interrupt();
                            timer.cancel();
                            showBottomView(false);
                        } else {
                            showBottomView(true);
                        }
                        progressDialog.dismiss();
                        break;
                    default:
                        break;
                }
            }
        };
        return rootView;
    }

    private void showBottomView(boolean isShow){
        if (isShow){
            WhiteSpaceText.setVisibility(View.VISIBLE);
            port_info_list.setVisibility(View.VISIBLE);
            setViewGroupHeight(device_back,640);
        } else {
            WhiteSpaceText.setVisibility(View.GONE);
            port_info_list.setVisibility(View.GONE);
        }
    }
    private boolean isBottomViewShow(){
        return WhiteSpaceText.getVisibility() == View.VISIBLE;
    }

    private void portAboutDataChangeNotice(){
        expandableListAdapter = new MyExpandableListAdapter(mCtx,portGoose.getGooseBeans());
        expandableListView.setAdapter(expandableListAdapter);
        odf_listAdapter = getOdfDataLvAdapter(mCtx,odfLinks);
        listView.setAdapter(odf_listAdapter);

        /*expandableListAdapter.notifyDataSetChanged();
        odf_listAdapter.notifyDataSetChanged();*/
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        device_back_view = rootView.findViewById(R.id.device_back_view);
        device_back_view.setUnitId(unitId);
        if (highLightPortId>0) device_back_view.setHightLightPort(highLightPortId);

        mapBiggerView0 = (ImageView) rootView.findViewById(R.id.mapBigger0);
        mapSmallerView0 = (ImageView) rootView.findViewById(R.id.mapSmaller0);
        mapOriginalView0 = (ImageView) rootView.findViewById(R.id.mapOriginal0);
        mapOriginalViewFilled0 = (ImageView) rootView.findViewById(R.id.mapOriginalFilled);
        mapBiggerView0.setOnClickListener(this);
        mapSmallerView0.setOnClickListener(this);
        mapOriginalView0.setOnClickListener(this);
        mapOriginalViewFilled0.setOnClickListener(this);

        //分屏界面
        initDraggableView();

        progressDialog = new ProgressDialog(mCtx);
    }

    private int wholeHeight = 0;
    private float DownX  = 0.0f;
    private float DownY = 0.0f;
    MyExpandableListAdapter expandableListAdapter;
    CommonAdapter odf_listAdapter;
    private void initDraggableView() {
        device_back = rootView.findViewById(R.id.device_back);
        WhiteSpaceText = (TextView) rootView.findViewById(R.id.draggableTV);
        //ied+odf
        port_info_list = rootView.findViewById(R.id.port_info_list);
        expandableListView = port_info_list.findViewById(R.id.iedCbListView);
        listView = port_info_list.findViewById(R.id.odfListView);

        if (portGoose == null) portGoose = new GoosePortBean();
        List<GoosePortBean.GooseBean> gooseBeans = portGoose.getGooseBeans();
        if (gooseBeans == null )gooseBeans = new ArrayList<>();
        expandableListAdapter = new MyExpandableListAdapter(mCtx,portGoose.getGooseBeans());
        expandableListView.setAdapter(expandableListAdapter);
        odf_listAdapter = getOdfDataLvAdapter(mCtx,odfLinks);
        listView.setAdapter(odf_listAdapter);

        if (PORT_UNIT_TYPE == PORT_UNIT_TYPE_IED){
            port_info_list.setVisibility(View.VISIBLE);
            expandableListView.setVisibility(View.VISIBLE);
            listView.setVisibility(View.GONE);
        } else if (PORT_UNIT_TYPE == PORT_UNIT_TYPE_ODF){
            port_info_list.setVisibility(View.VISIBLE);
            expandableListView.setVisibility(View.GONE);
            listView.setVisibility(View.VISIBLE);
        } else {
            WhiteSpaceText.setVisibility(View.GONE);
            port_info_list.setVisibility(View.GONE);
        }

        WhiteSpaceText.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {

                int action = event.getAction();

                switch(action) {
                    case MotionEvent.ACTION_DOWN:
                        WhiteSpaceText.setBackgroundColor(0xffff0000);
                        int deviceView = device_back.getHeight();
                        int listHeight = port_info_list.getHeight();
                        wholeHeight = deviceView + listHeight;

                        DownX = event.getX();//float DownX
                        DownY = event.getY();//float DownY
                        break;
                    case MotionEvent.ACTION_MOVE:
                        float moveX = event.getX() - DownX;//X轴距离
                        float moveY = event.getY() - DownY;//y轴距离
                        int nHeight = 	device_back.getHeight();

                        nHeight = (int)(nHeight + moveY);
                        if(nHeight <  0) {
                            nHeight = 0;
                        } else if(nHeight > wholeHeight) {
                            nHeight = wholeHeight;
                        }

                        setViewGroupHeight(device_back,nHeight);
                        setViewGroupHeight(port_info_list,wholeHeight -nHeight);
                        break;
                    case MotionEvent.ACTION_UP:
                        WhiteSpaceText.setBackgroundColor(0xffc0c0c0);
                        break;
                }
                return true;
            }
        });
    }

    //修改LinearLayout的高度
    public static void setViewGroupHeight(View view, int height){
        ViewGroup.LayoutParams lp = view.getLayoutParams();
        if (lp.height != height) {
            lp.height = height;
            view.setLayoutParams(lp);
        }
    }

    private CommonAdapter getOdfDataLvAdapter(Context context, final List<UnitConnectBean.UnitData> datas){
        CommonAdapter adapter = new CommonAdapter(context, datas,R.layout.port_odf_link_item) {
            @Override
            public void convert(final ViewHolder viewHolder, Object itemBean) {
                final int pos = viewHolder.getPosition();
                if (datas.isEmpty()) return;
                viewHolder.setText(R.id.tv_index, (pos+1)+"");
                String port_str = datas.get(pos).getBoardPort()+"-"+datas.get(pos).getPort();
                //viewHolder.setText(R.id.tv_port1, port_str);
                viewHolder.setText(R.id.tv_unitName, datas.get(pos).getKm_spcd_unit().getDescription());
                viewHolder.setText(R.id.tv_port2, port_str);
                viewHolder.setText(R.id.tv_opline, datas.get(pos).getGxGL());

                View.OnClickListener toDetailListener = new View.OnClickListener(){
                    @Override
                    public void onClick(View v) {
                    }
                };
                viewHolder.setOnItemClickListener(toDetailListener);
            }
        };
        return adapter;
    }

    public void setUnitIdPortId(int unitId,int portId){
        this.unitId = unitId;
        this.highLightPortId = portId;

        if (device_back_view == null) return;
        device_back_view.setUnitId(unitId);
        if (portId>0) device_back_view.setHightLightPort(portId);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.mapBigger0) {
            device_back_view.SetScale(1.5f);
        } else if (id == R.id.mapSmaller0) {
            device_back_view.SetScale(0.5f);
        } else if (id == R.id.mapOriginal0) {
            device_back_view.RequestLayout_FillWidth();
        } else if (id == R.id.mapOriginalFilled) {//全局显示
            device_back_view.RequestLayout_FillWidthHeight();
        }


    }

    //控制块虚端子信息
    GoosePortBean portGoose = new GoosePortBean();
    Thread thread_getCbVrLinks;
    Timer timer;
    //ODF链路信息
    List<UnitConnectBean.UnitData> odfLinks = new ArrayList<>();
    public class PortChangedBroadcastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if("ACTION_PORT_ID_CHANGE".equals(action)){

                final int portId = intent.getIntExtra("portId", -1);
                handler.sendEmptyMessage(MSG_PORT_DATA_HANDLING);
                //点击端口出来goose控制块和虚端子连接
                thread_getCbVrLinks = new Thread(){
                    @Override
                    public void run() {
                        if(PORT_UNIT_TYPE==PORT_UNIT_TYPE_IED){
                            portGoose = getPortGoose(portId);
                        }
                        if(PORT_UNIT_TYPE==PORT_UNIT_TYPE_ODF){
                            odfLinks = getODFDatas(portId);
                        }

                        handler.sendEmptyMessage(MSG_PORT_DATA_HANDLED);
                    }
                };
                thread_getCbVrLinks.start();

                if (thread_getCbVrLinks.getState() == Thread.State.TERMINATED){

                }
                timer = new Timer();
                timer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        handler.sendEmptyMessage(MSG_PORT_DATA_HANDLE_CANCEL);
                    }
                },10*1000);
            }
        }
    }

    private GoosePortBean getPortGoose(int portId) {
        BaseDaoImp unitDao = null;
        BaseDaoImp boardDao = null;
        BaseDaoImp portDao = null;

        GoosePortBean goosePortBean = new GoosePortBean();
        List<GoosePortBean.GooseBean> gooseBeanList = new ArrayList<>();
        goosePortBean.setGooseBeans(gooseBeanList);
        try {
            XshlBean.ConnectBean connectBeanF = null;
            String iedName = null;
            String dbName = PubApp.getDbName();
            unitDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_UNIT.class, dbName);
            boardDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_BOARD.class, dbName);
            portDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_PORT.class, dbName);

            KM_SPCD_PORT km_spcd_port = (KM_SPCD_PORT) portDao.getFirstForEq("id", portId);
            KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) boardDao.getFirstForEq("id", km_spcd_port.getBoard_id());
            KM_SPCD_UNIT km_spcd_unit = (KM_SPCD_UNIT) unitDao.getFirstForEq("id", km_spcd_board.getUnit_id());
            String portA = km_spcd_board.getSlot()+"-"+km_spcd_port.getNo()+"-"+km_spcd_port.getDirection();
            XshlBean xshlBean = XshlUtils.getXshlData((Activity) mCtx, dbName, km_spcd_unit.getId(), 1);
            List<XshlBean.RightUnitConnect> list = xshlBean.getList();
            if (list != null && list.size()!=0){
                for (XshlBean.RightUnitConnect rightUnitConnect:list){
                    List<XshlBean.ConnectBean> connectBeans = rightUnitConnect.getConnectBean();
                    if (connectBeans!=null && connectBeans.size()!=0){
                        for (XshlBean.ConnectBean connectBean:connectBeans){
                            if (portA.equals(connectBean.getBoardA()+"-"+connectBean.getPortA())){
                                connectBeanF = connectBean;
                                iedName = rightUnitConnect.getIedName();
                                break;
                            }
                        }
                    }
                }
            }
            if (connectBeanF != null){
                DataExchangeCachePool.getInstance().setKIED(PubUtils.getPubUtils().GetKIEDModel(xshlBean.getLeftUnit().getIedName()));//同时通知更新标题！
                Integer direction = connectBeanF.getDirection();//方向
                List<XshlBean.GsooseBean> gsooseBeans = connectBeanF.getGsooseBeans();//goose控制块
                if (gsooseBeans!=null && gsooseBeans.size()!=0){
                    for (XshlBean.GsooseBean gsoose:gsooseBeans){
                        GoosePortBean.GooseBean gooseBean = new GoosePortBean.GooseBean();
                        List<GoosePortBean.PortsBean> list1 = new ArrayList<>();
                        gooseBean.setDirection(direction);
                        gooseBean.setGoose(gsoose.getGoose());
                        //根据goose控制块去找虚端子连接
                        List<VirFCDABean> mFcdaList = null;
                        if (direction == 1){
                            if (gsoose.getGoose().startsWith("GO")){
                                mFcdaList = DataExchangeCachePool.getInstance().getFcdaList(iedName, gsoose.getAppId(), KIEDModel.GSE_SEND);
                            }else if (gsoose.getGoose().startsWith("SV")){
                                mFcdaList = DataExchangeCachePool.getInstance().getFcdaList(iedName, gsoose.getAppId(), KIEDModel.SMV_SEND);
                            }
                        }else if (direction == 2){
                            if (gsoose.getGoose().startsWith("GO")){
                                mFcdaList = DataExchangeCachePool.getInstance().getFcdaList(iedName, gsoose.getAppId(), KIEDModel.GSE_RECV);
                            }else if (gsoose.getGoose().startsWith("SV")){
                                mFcdaList = DataExchangeCachePool.getInstance().getFcdaList(iedName, gsoose.getAppId(), KIEDModel.SMV_RECV);
                            }
                        }
                        if (mFcdaList !=null){
                            for (VirFCDABean virFCDABean:mFcdaList){
                                List<VirExtRefBean> extRefList = virFCDABean.getExtRefList();
                                if (extRefList!=null && extRefList.size()!=0){
                                    for (VirExtRefBean virExtRefBean:extRefList){
                                        GoosePortBean.PortsBean portsBean = new GoosePortBean.PortsBean();
                                        portsBean.setPortA(virFCDABean.getFcdaName());
                                        portsBean.setPortB(virExtRefBean.getName());
                                        list1.add(portsBean);
                                    }
                                }
                            }
                        }
                        gooseBean.setPorts(list1);
                        gooseBeanList.add(gooseBean);
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return null;
        } catch (Exception e){
            e.printStackTrace();
            return null;
        }
        return goosePortBean;
    }

    private List<UnitConnectBean.UnitData> getODFDatas(int portId){
        try{
            String dbName = PubApp.getDbName();
            HandleForWholeCircuitsFix3 handleForWholeCircuitsFix3 = new HandleForWholeCircuitsFix3((Activity) mCtx, dbName, portId,3);
            WholeCircuitBean data = handleForWholeCircuitsFix3.wholeCircuitBean0;
            //整理数据
            UnitConnectBean unitConnectBean = DataProcessTools.trimData(data);
            return unitConnectBean.getUnits();
        }catch (Exception e){
            return new ArrayList<>();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.getActivity().unregisterReceiver(portChangedBroadcastReceiver);
    }
}
