package com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.optical_fiber_tag;

import android.graphics.Bitmap;

import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.QRCode;

import java.util.HashMap;
import java.util.Map;

/**
 *
 */
public class BaseTagInfo {
    KM_SPCD_PORT belongTo;//所扫描的光纤端口或者光纤连接图所选中的端口【】
    String qr_code = "";//光纤端口的信息，即二维码所对应的字符串
    Bitmap bitmap_qr_code;//二维码图片

    Map<String,Object> tag_view_info = new HashMap<>();//标签图信息

    public Map<String, Object> getTag_view_info() {
        return tag_view_info;
    }

    public void setTag_view_info(Map<String, Object> tag_view_info) {
        this.tag_view_info = tag_view_info;
        tag_view_info.put("useDlgTheme",true);
    }

    public KM_SPCD_PORT getBelongTo() {
        return belongTo;
    }

    public void setBelongTo(KM_SPCD_PORT belongTo) {
        this.belongTo = belongTo;
        //给端口赋值之后再给
    }

    public Bitmap getBitmap_qr_code() {
        return bitmap_qr_code;
    }

    public void setBitmap_qr_code(Bitmap bitmap_qr_code) {
        this.bitmap_qr_code = bitmap_qr_code;
    }

    public String getQr_code() {
        return qr_code;
    }

    public void setQr_code(String qr_code) {
        this.qr_code = qr_code;
        this.setBitmap_qr_code(QRCode.createQRCode(qr_code,128));
    }
}
