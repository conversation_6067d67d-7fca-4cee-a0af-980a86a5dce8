package com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.Parser.model;

import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.SPCDConstant;
import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.check.CheckResult;
import org.xmlpull.v1.XmlPullParser;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public class Cubicle extends  SPCDItemBaseEx  {
    private List<Unit> units;
    private List<IntCore> intCores;
    private List<Cable> cables;//连接到此屏柜的Cable
    public Cubicle(XmlPullParser parser,Object parent) throws Exception{
        super(parent);
        this.parser(parser);
        this.startParserChild(parser, SPCDConstant.SECTIONTAG_CUBICLE);
    }
    public Port findPortByCorePort(String corePortPath)
    {
        CorePort corePort=new CorePort(corePortPath);
        return findPortByCorePort(corePort);
    }
    public Port findPortByCorePort(CorePort corePort)
    {
        Unit findUnit=findUnit(corePort.getUnitName());
        if(findUnit==null)
            return null;
        return findUnit.findPortByCorePort(corePort);
    }
    /**
     * 通过corePort查找其RxTx相对应的另外一个端口
     * @param corePort
     * @return
     */
    public Port findPairPortByCorePort(CorePort corePort){

        Unit findUnit=findUnit(corePort.getUnitName());
        if(findUnit==null)
            return null;
        return findUnit.findPairPortByCorePort(corePort);
    }
    public Unit findUnit(String unitName){
        if((units==null)||(unitName==null)||unitName.isEmpty())
            return null;
        for(Unit unitItm:units){
            if(unitName.equals(unitItm.getName()))
                return unitItm;
        }
        return null;
    }
    public List<Unit> getUnits() {
        return units==null?new LinkedList<>():units;
    }
    public void addCableConnected(Cable cable){
        if(cable==null)
            return ;
        if(!this.equals(cable.getCubicleAObject())&&!this.equals(cable.getCubicleBObject()))
            return;
        if(cables==null)
            cables=new LinkedList<>();
        cables.add(cable);
    }
    public List<IntCore> getIntCores() {
        return intCores==null?new LinkedList<>():intCores;
    }

    private void doParserFileEnd(boolean bPrepared) throws Exception{
        if(units!=null){
            for(Unit unitItm:units){
                if(bPrepared)
                    unitItm.parseFileEndPrepare();
                else
                    unitItm.parseFileEnd();
            }
        }
        if(intCores!=null){
            for(IntCore intCoreItm:intCores){
                if(bPrepared)
                    intCoreItm.parseFileEndPrepare();
                else
                    intCoreItm.parseFileEnd();
            }
        }
    }
    @Override
    public void parseFileEndPrepare() throws Exception {
        doParserFileEnd(true);
    }

    @Override
    public void parseFileEnd() throws Exception {
        doParserFileEnd(false);
    }

    @Override
    public void parserEventChildStartTag(XmlPullParser parser, String childrenTagName) throws Exception {
        switch(childrenTagName){
            case SPCDConstant.SECTIONTAG_UNIT:
                if(this.units==null)
                    this.units=new LinkedList<>();
                this.units.add(new Unit(parser,this));
                break;
            case SPCDConstant.SECTIONTAG_INTCORE:
                if(this.intCores==null)
                    this.intCores=new LinkedList<>();
                this.intCores.add(new IntCore(parser,this));
                break;
            default:
                break;
        }
    }

    @Override
    public List<CheckResult> check() {
        List<CheckResult>ret=new ArrayList<>();
        for(IntCore intCoreItm:getIntCores())
        {
            ret.addAll(intCoreItm.check());
        }
        for(Unit unitItm:getUnits()){
            ret.addAll(unitItm.check());
        }
        return ret;
    }

    @Override
    public String toString() {
        return "Cubicle[" +getName()+"("+getDesc()+")]";
    }
}
