package com.kemov.visual.spcd.spcdvisualandroidapp.bean;

import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;

import java.util.List;

public class OdfViewDataBean {

    private static OdfViewDataBean instance;

    public synchronized static OdfViewDataBean getInstance() {
        if (instance == null) {
            instance = new OdfViewDataBean();
        }
        return instance;
    }

    List<KM_SPCD_PORT> ports;
    List<CableBean> cableBeans;//左侧连接线数据
    List<CableBean> cableRightBeans;//右侧连接线数据

    public List<CableBean> getCableRightBeans() {
        return cableRightBeans;
    }

    public void setCableRightBeans(List<CableBean> cableRightBeans) {
        this.cableRightBeans = cableRightBeans;
    }

    public List<CableBean> getCableBeans() {
        return cableBeans;
    }

    public void setCableBeans(List<CableBean> cableBeans) {
        this.cableBeans = cableBeans;
    }

    public List<KM_SPCD_PORT> getPorts() {
        return ports;
    }

    public void setPorts(List<KM_SPCD_PORT> ports) {
        this.ports = ports;
    }


    public static class CableBean {
        KM_SPCD_CABLE km_spcd_cable;
        List<Integer> cableCoreIds;
        int lineColor;

        public int getLineColor() {
            return lineColor;
        }

        public void setLineColor(int lineColor) {
            this.lineColor = lineColor;
        }

        public List<Integer> getCableCoreIds() {
            return cableCoreIds;
        }

        public void setCableCoreIds(List<Integer> cableCoreIds) {
            this.cableCoreIds = cableCoreIds;
        }

        public KM_SPCD_CABLE getKm_spcd_cable() {
            return km_spcd_cable;
        }

        public void setKm_spcd_cable(KM_SPCD_CABLE km_spcd_cable) {
            this.km_spcd_cable = km_spcd_cable;
        }
    }

    public static void clear(){
        if (instance != null) {
            instance = null;
        }
    }
}
