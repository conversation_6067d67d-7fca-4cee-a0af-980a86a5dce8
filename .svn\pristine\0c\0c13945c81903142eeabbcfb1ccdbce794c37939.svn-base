package com.kemov.visual.spcd.spcdvisualandroidapp.activity.ied_whole_circuit;

import android.app.Activity;
import android.text.TextUtils;
import android.util.Log;

import com.kemov.sclaata.common.app.PubUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.PortConnectBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.WholeCircuitBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_REGION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_SUBSTATION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDao;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.SpcdUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Utils;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import static com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool.CubicleLocation.DOUBLE_FROM;
import static com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool.CubicleLocation.DOUBLE_TO;
import static com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool.CubicleLocation.FROM;
import static com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool.CubicleLocation.PASS;
import static com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool.CubicleLocation.SINGLE;
import static com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool.CubicleLocation.TO;
import static java.util.Arrays.asList;

public class HandleForWholeCircuitsFix {
    private static final String TAG = "HandleForWholeCircuits";

    public static final String UNIT_TYPE_IED = "IED";
    public static final String UNIT_TYPE_SWITCH = "SWITCH";
    public static final String UNIT_TYPE_ODF = "ODF";

    Activity mCtx;
    Integer mType;

    private String dbName;
    public PubUtils mPubUtils = null;

    BaseDao cableDao = null;
    BaseDao coreDao = null;
    BaseDaoImp unitDao = null;
    BaseDaoImp boardDao = null;
    BaseDaoImp portDao = null;
    BaseDaoImp cubicleDao = null;
    BaseDaoImp regionDao = null;
    BaseDaoImp substationDao =null;
    BaseDaoImp intcoreDao=null;
    private KM_SPCD_UNIT km_spcd_unit;
    private KM_SPCD_CUBICLE km_spcd_cubicle;
    private KM_SPCD_SUBSTATION km_spcd_substation;
    private KM_SPCD_REGION km_spcd_region;
    List<KM_SPCD_INTCORE> bIntcores = new ArrayList<>();//portB="4n.1.A-Rx"
    List<KM_SPCD_INTCORE> aIntcores = new ArrayList<>();//portA="4n.1.C-Tx"
    List<PortConnectBean.IedConnectBean> iedList  = new ArrayList<>();
    List<KM_SPCD_INTCORE> switchIntCores  = new ArrayList<>();
    PortConnectBean portConnectBean;
    public static IEDsWholeCircuitDataPool instance;

    List<IEDsWholeCircuitDataPool.CubicleGroupBaseBean> cubicles = new ArrayList<>();
    List<String> glNames = new ArrayList<>();
    List<String> txNames = new ArrayList<>();//跳纤(ied-odf)
    List<String> ied2ied_TxName = new ArrayList<>();//跳纤（1个屏柜中的两个装置直接相连）
    List<String> ied2ied_WLName = new ArrayList<>();//尾缆（两个屏柜中的两个装置直接相连）
    KM_SPCD_PORT port = null;//全回路被点击的端口号
    KM_SPCD_BOARD board = null;//被点击端口的板卡
    String port0 = null;//被点击端口的拼接端口，如：portA="1n.1.A-Tx"
    List<IEDsWholeCircuitDataPool.IED> ieds1 = null;//装第一个屏柜的ied
    IEDsWholeCircuitDataPool.IED ied1 = null;//第一个拼柜的ied
    IEDsWholeCircuitDataPool.CubicleGroupBaseBean cubicleGroupBaseBean1;//1.第一个屏柜的数据

    public WholeCircuitBean wholeCircuitBean;
    List<WholeCircuitBean.CubicleWholeBean> list = new ArrayList<>();
    //第一个屏柜，第一个屏柜的装置
    WholeCircuitBean.CubicleWholeBean cubicleWholeBean1;
    WholeCircuitBean.UnitWholeBean unitWholeBean1;
    List<WholeCircuitBean.UnitWholeBean> unitWholeBeanList1;

    public HandleForWholeCircuitsFix(Activity mCtx, String dbName, Integer portId,Integer type) {
        this.mCtx = mCtx;
        this.dbName = dbName;
        //查询虚实回路端口数据
        getPortData(portId,type);
    }

    public WholeCircuitBean getPortData(Integer id,Integer type) {
        mType = type;
        wholeCircuitBean = new WholeCircuitBean();

        aIntcores.clear();
        bIntcores.clear();
        iedList.clear();
        switchIntCores.clear();
        //改造数据
        list.clear();
        wholeCircuitBean.clear();

        unitDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_UNIT.class, dbName);
        boardDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_BOARD.class, dbName);
        portDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_PORT.class, dbName);
        cableDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_CABLE.class, dbName);
        coreDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_CORE.class, dbName);
        cubicleDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_CUBICLE.class, dbName);
        regionDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_REGION.class, dbName);
        substationDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_SUBSTATION.class, dbName);
        intcoreDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_INTCORE.class, dbName);

        portConnectBean = new PortConnectBean();
        instance = IEDsWholeCircuitDataPool.getInstance();

        KM_SPCD_INTCORE intcore = null;
        if (type == 1){//传进来的是装置unitid
            km_spcd_unit = (KM_SPCD_UNIT)unitDao.getById(id);//装置
            portConnectBean.setType(1);
        }else if (type == 2){//传进来的是装置IntCoreId
            intcore = (KM_SPCD_INTCORE) intcoreDao.getById(id);
            String port_a = intcore.getPort_a();
            km_spcd_unit = SpcdUtils.getUnitByIntcoreId(mCtx, dbName, intcore);
            portConnectBean.setType(2);

            String[] split = port_a.split("\\.");
            try {
                board = (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unit.getId(), "slot", split[1]);
                String no = getNoDes(port_a, 1);
                String des = getNoDes(port_a, 2);
                port = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", board.getId(), "no", no, "direction", des);
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }else if (type == 3 || type == 4){//传进来的是装置PortId
            port = (KM_SPCD_PORT) portDao.getById(id);
            if (port == null){
                return null;
            }
            board = (KM_SPCD_BOARD)boardDao.getById(port.getBoard_id());
            km_spcd_unit = SpcdUtils.getUnitByPortId(mCtx, dbName, port);
            portConnectBean.setType(3);
        }

        if (km_spcd_unit == null){
            return null;
        }

        List<KM_SPCD_BOARD> boards = boardDao.queryLike("unit_id", km_spcd_unit.getId());//板卡
        km_spcd_cubicle = (KM_SPCD_CUBICLE)cubicleDao.getById(km_spcd_unit.getCubicle_id());//屏柜
        km_spcd_region = (KM_SPCD_REGION)regionDao.getById(km_spcd_cubicle.getRegion_id());//小室
        km_spcd_substation = (KM_SPCD_SUBSTATION)substationDao.getById(km_spcd_cubicle.getSubstation_id());//厂站

        //设置本ied
        portConnectBean.setKm_spcd_unit(km_spcd_unit);
        //改造数据,设置第一个屏柜，屏柜里面的第一个ied装置
        cubicleWholeBean1 = new WholeCircuitBean.CubicleWholeBean();
        unitWholeBeanList1 = new ArrayList<>();
        unitWholeBean1 = new WholeCircuitBean.UnitWholeBean();
        unitWholeBeanList1.add(unitWholeBean1);
        list.add(cubicleWholeBean1);

        cubicleWholeBean1.setUnitWholeBeans(unitWholeBeanList1);
        wholeCircuitBean.setCubicleWholeBeans(list);

        cubicleWholeBean1.setKm_spcd_cubicle(km_spcd_cubicle);
        unitWholeBean1.setKm_spcd_unit(km_spcd_unit);

        //1.查询IntCore中，portB、portA分别是该装置的时候
        List<KM_SPCD_INTCORE> km_spcd_intcores  = (List<KM_SPCD_INTCORE>) intcoreDao.getListForEq("cubicle_id", km_spcd_cubicle.getId());
        if (type == 1){
            if (km_spcd_intcores!=null && km_spcd_intcores.size()!=0){
                for (KM_SPCD_INTCORE km_spcd_intcore : km_spcd_intcores){
                    if (km_spcd_intcore.getPort_b().startsWith(km_spcd_unit.getName())){
                        bIntcores.add(km_spcd_intcore);
                    }
                    if (km_spcd_intcore.getPort_a().startsWith(km_spcd_unit.getName())){
                        aIntcores.add(km_spcd_intcore);
                    }
                }
            }
        }else if (type == 2){
            //如果是光纤或者odf跳进来；只有单边的连线
            wholeCircuitBean.setType(2);
            if (intcore!=null){
                aIntcores.add(intcore);
                //改造数据
                if (port!=null && board!=null){
                    unitWholeBean1.setBoardPort(board.getSlot()+"-"+port.getNo());
                    Utils.setBoardPort(unitWholeBean1,board.getSlot(),port.getNo());
                    unitWholeBean1.setPortADirection(port.getDirection());
//                    unitWholeBean1.setKm_spcd_portA(port);
                    String portAB1 = km_spcd_unit.getName() +"." + board .getSlot() + "." + port.getNo() +"-" + port.getDirection();//如：1n.1.A-Tx
                    if (km_spcd_intcores!=null && km_spcd_intcores.size()!=0){
                        for (KM_SPCD_INTCORE km_spcd_intcore : km_spcd_intcores){
                            if (km_spcd_intcore.getPort_b().equals(portAB1)){
                                unitWholeBean1.setKm_spcd_intcoreA(km_spcd_intcore);
                            }
                            if (km_spcd_intcore.getPort_a().equals(portAB1)){
                                unitWholeBean1.setKm_spcd_intcoreA(km_spcd_intcore);
                            }
                        }
                    }
                }
            }
        }else if (type == 3 || type == 4){
            wholeCircuitBean.setType(type);
            KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD)boardDao.getById(port.getBoard_id());

            if (type == 3){//4，如果是odf，不设置BoardPort
                unitWholeBean1.setBoardPort(board.getSlot()+"-"+port.getNo());
                Utils.setBoardPort(unitWholeBean1,board.getSlot(),port.getNo());
            }
            //查询另外一个端口
            List<KM_SPCD_PORT> ports = portDao.getListForEq("board_id", board.getId(), "no", port.getNo());
            String portAB1 = null;
            String portAB2 = null;
            if (ports.size() == 1){
                unitWholeBean1.setPortADirection(port.getDirection());
//                unitWholeBean1.setKm_spcd_portA(port);
                portAB1 = km_spcd_unit.getName() +"." + km_spcd_board .getSlot() + "." + port.getNo() +"-" + port.getDirection();//如：1n.1.A-Tx
                if (type == 4){
                    unitWholeBean1.setPortADesc(board.getSlot()+ "-" + port.getNo());
                }
            }else if (ports.size() == 2){
                for (KM_SPCD_PORT km_spcd_port:ports){
                    if (km_spcd_port.getDirection().contains("T")){
                        unitWholeBean1.setPortADirection(km_spcd_port.getDirection());
//                        unitWholeBean1.setKm_spcd_portA(port);
                        portAB1 = km_spcd_unit.getName() +"." + km_spcd_board .getSlot() + "." + km_spcd_port.getNo() +"-" + km_spcd_port.getDirection();//如：1n.1.A-Tx
                        if (type == 4){
                            unitWholeBean1.setPortADesc(board.getSlot()+ "-" + port.getNo());
                        }
                    }else {
                        if (type != 1){
                            unitWholeBean1.setPortBDirection(km_spcd_port.getDirection());
//                            unitWholeBean1.setKm_spcd_portB(port);
                            portAB2 = km_spcd_unit.getName() +"." + km_spcd_board .getSlot() + "." + km_spcd_port.getNo() +"-" + km_spcd_port.getDirection();//如：1n.1.A-Tx
                            if (type == 4){
                                unitWholeBean1.setPortBDesc(board.getSlot()+ "-" + port.getNo());
                            }
                        }
                    }
                }
            }

            if (km_spcd_intcores!=null && km_spcd_intcores.size()!=0){
                for (KM_SPCD_INTCORE km_spcd_intcore : km_spcd_intcores){
                    if (km_spcd_intcore.getPort_b().equals(portAB1)){
                        bIntcores.add(km_spcd_intcore);
                        unitWholeBean1.setKm_spcd_intcoreA(km_spcd_intcore);
                    }
                    if (km_spcd_intcore.getPort_a().equals(portAB1)){
                        aIntcores.add(km_spcd_intcore);
                        unitWholeBean1.setKm_spcd_intcoreA(km_spcd_intcore);
                    }
                    if (type != 1){
                        if (km_spcd_intcore.getPort_b().equals(portAB2)){
                            unitWholeBean1.setKm_spcd_intcoreB(km_spcd_intcore);
                        }
                        if (km_spcd_intcore.getPort_a().equals(portAB2)){
                            unitWholeBean1.setKm_spcd_intcoreB(km_spcd_intcore);
                        }
                    }
                }
            }
        }

        //清除上一个数据集
        instance.clear();
        //1.拼装该端口
        port0 = km_spcd_unit.getName()+"."+board.getSlot()+"."+port.getNo()+"-"+port.getDirection();
        //2.设置第一个屏柜的数据
        cubicleGroupBaseBean1 = new IEDsWholeCircuitDataPool.CubicleGroupBaseBean();

        ieds1 = new ArrayList<>();
        ied1 = new IEDsWholeCircuitDataPool.IED();
        String dev_class = km_spcd_unit.getDev_class();
        if ("ODF".equals(dev_class)){
            //如果是odf，在下面代码中设置第一个屏柜该odf与之相连的装置
        }else {
            ied1.setIedName(buildUnitNameDesc(km_spcd_unit));
            ied1.setIed_portNo(port.getNo()+"-"+port.getDirection());
            ieds1.add(ied1);
            cubicleGroupBaseBean1.setCubicleName(buildCubiculeNameDesc(km_spcd_cubicle));
            cubicleGroupBaseBean1.setIeds(ieds1);
        }

        //查询本屏柜尾缆的连接
        setWLconnect();

        if (instance.getLink_type() != IEDsWholeCircuitDataPool.TYPE_1IED_IN_1CUBICULE_STRAIT_THR){//不同屏柜直连
            //查询本屏柜连接的装置
            if (aIntcores!=null && aIntcores.size()!=0){
                setConnectBean(aIntcores,1);
            }
            if (bIntcores!=null && bIntcores.size()!=0){
                setConnectBean(bIntcores,2);
            }
        }
        instance.setGlNames(glNames);
        instance.setCubicles(cubicles);
//        return instance;
        return wholeCircuitBean;
    }

    private void setConnectBean(List<KM_SPCD_INTCORE> intcores,int type) {
        if (intcores!=null && intcores.size()!=0){
            for (KM_SPCD_INTCORE km_spcd_intcore:intcores){
                String dev_class = km_spcd_unit.getDev_class();
                if ("ODF".equals(dev_class)){//如果是点击的odf端口进来的
                    String port_a = km_spcd_intcore.getPort_a();
                    String port_b = km_spcd_intcore.getPort_b();
                    //unitWholeBean1.setType(2);
                    if (port_a.startsWith(km_spcd_unit.getName())){
                        //查找odf连接的本侧屏柜装置
                        setCubicleGroupBaseBean1(port_b);

                        String[] split = port_a.split("\\.");
                        String unitName = split[0];//unit的name，如：4n
                        //改造
                        setOtherUnit(km_spcd_intcore,unitName,null,2,km_spcd_cubicle, null,cubicleWholeBean1,unitWholeBeanList1,null);
                    }else if (port_b.startsWith(km_spcd_unit.getName())){
                        //查找odf连接的本侧屏柜装置
                        setCubicleGroupBaseBean1(port_a);

                        String[] split = port_b.split("\\.");
                        String unitName = split[0];//unit的name，如：4n
                        setOtherUnit(km_spcd_intcore,unitName,null,1,km_spcd_cubicle, null,cubicleWholeBean1,unitWholeBeanList1,null);
                    }
                }else{
                    //unitWholeBean1.setType(1);
                    if (type == 1){
                        String port_b = km_spcd_intcore.getPort_b();
                        if (!TextUtils.isEmpty(port_b) && port_b.contains("-")){
                            String[] split = port_b.split("\\.");
                            String unitName = split[0];//unit的name，如：4n
                            //改造，查找与之相匹配的另一个intcore
                            String port_a = km_spcd_intcore.getPort_a();
                            KM_SPCD_INTCORE otherIntcore = getOtherIntcore(port_a,1);
                            //根据unitName查询该装置
                            setOtherUnit(km_spcd_intcore,unitName,null,type,km_spcd_cubicle, null,cubicleWholeBean1,unitWholeBeanList1,otherIntcore);
                        }
                    }else if (type == 2){
                        String port_a = km_spcd_intcore.getPort_a();
                        if (!TextUtils.isEmpty(port_a) && port_a.contains("-")){
                            String[] split = port_a.split("\\.");
                            String unitName = split[0];//unit的name，如：4n
                            //改造，查找与之相匹配的另一个intcore
                            String port_b = km_spcd_intcore.getPort_a();
                            KM_SPCD_INTCORE otherIntcore = getOtherIntcore(port_b,2);
                            //根据unitName查询该装置
                            setOtherUnit(km_spcd_intcore,unitName,null,type,km_spcd_cubicle, null,cubicleWholeBean1,unitWholeBeanList1,otherIntcore);
                        }
                    }
                }
            }
            portConnectBean.setIedList(iedList);
        }
        Log.e("打印出来的端口连接关系",portConnectBean.toString());
    }

    public KM_SPCD_INTCORE getOtherIntcore(String port,Integer type){
        String[] split1 = port.split("\\.");
        List<KM_SPCD_BOARD> boards = (List<KM_SPCD_BOARD>) boardDao.getListForEq("unit_id", km_spcd_unit.getId(), "slot", split1[1]);
        if (boards!=null && boards.size()!=0){
            KM_SPCD_BOARD km_spcd_board = boards.get(0);
            String no = getNoDes(port, 1);
            String des = getNoDes(port, 2);
            List<KM_SPCD_PORT> ports = portDao.getListForEq("board_id", km_spcd_board.getId(), "no", no);
            if (ports!=null && ports.size()!=0){
                for (KM_SPCD_PORT km_spcd_port:ports){
                    if (!km_spcd_port.getDirection().equals(des)){
                        String port1 = km_spcd_unit.getName() + "." + km_spcd_board.getSlot() + "." + km_spcd_port.getNo()+"-"+km_spcd_port.getDirection();
                        List<KM_SPCD_INTCORE> intcoress = null;
                        if (type == 1){
                            intcoress = (List<KM_SPCD_INTCORE>) intcoreDao.getListForEq("cubicle_id", km_spcd_cubicle.getId(), "port_a", port1);
                        }else if (type == 2){
                            intcoress = (List<KM_SPCD_INTCORE>) intcoreDao.getListForEq("cubicle_id", km_spcd_cubicle.getId(), "port_b", port1);
                        }
                        if (intcoress!=null && intcoress.size()!=0){
                            KM_SPCD_INTCORE km_spcd_intcore1 = intcoress.get(0);
                            return km_spcd_intcore1;
                        }
                    }
                }
            }
        }
        return null;
    }

    public String getNoDes(String port,Integer type){//type=1返回no，type=2返回端口如Tx
        if (!TextUtils.isEmpty(port)){
            String[] split1 = port.split("\\.");
            String noDes = split1[2];
            if (!TextUtils.isEmpty(noDes) && noDes.contains("-")){
                String[] split2 = noDes.split("-");
                if (type == 1){
                    return split2[0];
                }else if (type == 2){
                    return split2[1];
                }else {
                    return null;
                }
            }
        }
        return null;
    }

    public void setCubicleGroupBaseBean1(String port_b){
        //查找odf连接的本侧屏柜装置
        String[] split0 = port_b.split("\\.");
        String unitName0 = split0[0];//unit的name，如：4n
        try {
            KM_SPCD_UNIT km_spcd_unit0 = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", unitName0);

            ied1.setIedName(buildUnitNameDesc(km_spcd_unit0));
            ied1.setIed_portNo(split0[2]);
            ieds1.add(ied1);
            cubicleGroupBaseBean1.setCubicleName(buildCubiculeNameDesc(km_spcd_cubicle));
            cubicleGroupBaseBean1.setIeds(ieds1);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private void setOtherUnit(KM_SPCD_INTCORE km_spcd_intcore, String unitName, KM_SPCD_INTCORE km_spcd_intcore0, int type, KM_SPCD_CUBICLE cubicle, IEDsWholeCircuitDataPool.CubicleGroupBaseBean secondWholeCubicle,
                              WholeCircuitBean.CubicleWholeBean cubicleWholeBean, List<WholeCircuitBean.UnitWholeBean> unitWholeBeanList, KM_SPCD_INTCORE km_spcd_intcore1) {
        //参数：1.第一次循环的原始intcore   2.对侧unitName,如：4n   3.多次循环传进来的   4.
        try {
            //本侧portA
            String portA = null;

            KM_SPCD_UNIT km_spcd_unit = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", cubicle.getId(), "name", unitName);
            String dev_class = km_spcd_unit.getDev_class();//获取连接装置的类型，如：ied,odf,switch
            String port_1 = null;//对测端口
            if (km_spcd_intcore0 == null){
                if (type == 1){
                    portA = km_spcd_intcore.getPort_a();
                    port_1 = km_spcd_intcore.getPort_b();
                }else if (type == 2){
                    portA = km_spcd_intcore.getPort_b();
                    port_1 = km_spcd_intcore.getPort_a();
                }
            }else {
                if (type == 1){
                    portA = km_spcd_intcore0.getPort_a();
                    port_1 = km_spcd_intcore0.getPort_b();
                }else if (type == 2){
                    portA = km_spcd_intcore0.getPort_b();
                    port_1 = km_spcd_intcore0.getPort_a();
                }
            }
            String[] split2 = port_1.split("\\.");
            String  noDir = split2[2];
            if ("IED".equals(dev_class)){//已经找到装置，结束
                //改造
                WholeCircuitBean.UnitWholeBean unitWholeBean = new WholeCircuitBean.UnitWholeBean();//收尾装置，同一屏柜
                //unitWholeBean.setType(1);
                unitWholeBean.setKm_spcd_unit(km_spcd_unit);
                if (!TextUtils.isEmpty(noDir) && noDir.contains("-")){
                    String[] split3 = noDir.split("-");
                    unitWholeBean.setBoardPort(split2[1]+"-"+split3[0]);
                    Utils.setBoardPort(unitWholeBean,split2[1],split3[0]);
                    unitWholeBean.setPortADirection(split3[1]);
                    //查找与这个port并列的intcore
                    if (mType != 1){
                        KM_SPCD_UNIT km_spcd_unitA = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", cubicle.getId(), "name", split2[0]);
                        KM_SPCD_BOARD km_spcd_boardA = (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unitA.getId(), "slot", split2[1]);
                        if (km_spcd_boardA!=null){
                            List<KM_SPCD_PORT> ports = (List<KM_SPCD_PORT>) portDao.getListForEq("board_id", km_spcd_boardA.getId(), "no", split3[0]);
                            if (ports!=null && ports.size()!=0){
                                for (KM_SPCD_PORT km_spcd_port:ports){
                                    if (!km_spcd_port.getDirection().equals(split3[1])){
                                        unitWholeBean.setPortBDirection(km_spcd_port.getDirection());
                                    }
                                }
                            }
                        }
                        unitWholeBean.setKm_spcd_intcoreB(km_spcd_intcore1);
                    }
                }
                unitWholeBeanList.add(unitWholeBean);

                IEDsWholeCircuitDataPool.IED ied2 = new IEDsWholeCircuitDataPool.IED();
                ied2.setIedName(buildUnitNameDesc(km_spcd_unit));
                ied2.setIed_portNo(split2[2]);
                if(km_spcd_intcore0 == null){//一个循环就找到装置
                    //设置对侧装置
                    ieds1.add(ied2);
                    cubicleGroupBaseBean1.setIeds(ieds1);
                    //设置类型
                    cubicleGroupBaseBean1.setLocationOf(SINGLE);
                    //设置连接类型
                    instance.setLink_type(IEDsWholeCircuitDataPool.TYPE_2IED_IN_1CUBICULE);
                    //3.添加第一个屏柜
                    cubicles.add(cubicleGroupBaseBean1);

                    unitWholeBean.setKm_spcd_intcoreA(km_spcd_intcore);
                }else {
                    List<IEDsWholeCircuitDataPool.IED> ieds2 = new ArrayList<>();
                    ieds2.add(ied2);
                    secondWholeCubicle.setIeds(ieds2);
                    cubicles.add(secondWholeCubicle);

                    unitWholeBean.setKm_spcd_intcoreA(km_spcd_intcore0);
                }
            }else if ("SWITCH".equals(dev_class)){//连接的是交换机，接着找装置
                IEDsWholeCircuitDataPool.IED ied2 = new IEDsWholeCircuitDataPool.IED();
                ied2.setIedName(buildUnitNameDesc(km_spcd_unit));
                ied2.setIed_portNo(split2[2]);

                //改造
                WholeCircuitBean.UnitWholeBean unitWholeBean = new WholeCircuitBean.UnitWholeBean();//收尾装置，同一屏柜
                //unitWholeBean.setType(1);
                unitWholeBean.setKm_spcd_unit(km_spcd_unit);
                if (!TextUtils.isEmpty(noDir) && noDir.contains("-")){
                    String[] split3 = noDir.split("-");
                    unitWholeBean.setBoardPort(split2[1]+"-"+split3[0]);
                    Utils.setBoardPort(unitWholeBean,split2[1],split3[0]);
                    unitWholeBean.setPortADirection(split3[1]);
                }
                String[] split4 = portA.split("\\.");
                String s1 = split4[2];
                //查找与这个port并列的intcore
                if (mType != 1){
                    KM_SPCD_UNIT km_spcd_unitA = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", cubicle.getId(), "name", split4[0]);
                    KM_SPCD_BOARD km_spcd_boardA = (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unitA.getId(), "slot", split4[1]);
                    if (!TextUtils.isEmpty(s1) && s1.contains("-")){
                        String[] split5 = s1.split("-");
                        if (km_spcd_boardA!=null){
                            List<KM_SPCD_PORT> ports = (List<KM_SPCD_PORT>) portDao.getListForEq("board_id", km_spcd_boardA.getId(), "no", split5[0]);
                            if (ports!=null && ports.size()!=0){
                                for (KM_SPCD_PORT km_spcd_port:ports){
                                    if (!km_spcd_port.getDirection().equals(split5[1])){
                                        unitWholeBean.setPortBDirection(km_spcd_port.getDirection());
                                    }
                                }
                            }
                        }
                    }
                    unitWholeBean.setKm_spcd_intcoreB(km_spcd_intcore1);
                }
                unitWholeBeanList.add(unitWholeBean);

                if(km_spcd_intcore0 == null) {//一个循环就找到交换机
                    ieds1.add(ied2);
                    cubicleGroupBaseBean1.setIeds(ieds1);
                    cubicleGroupBaseBean1.setLocationOf(SINGLE);
                    instance.setLink_type(IEDsWholeCircuitDataPool.TYPE_2IED_IN_1CUBICULE);
                    //3.添加第一个屏柜
                    cubicles.add(cubicleGroupBaseBean1);
                }else {
                    List<IEDsWholeCircuitDataPool.IED> ieds2 = new ArrayList<>();
                    ieds2.add(ied2);
                    secondWholeCubicle.setIeds(ieds2);
                    cubicles.add(secondWholeCubicle);
                }
            }else if ("ODF".equals(dev_class)){//连接的是odf,接着找对侧屏柜的装置
                String port_a = null;
                String port_b = null;
                if (km_spcd_intcore0 == null){
                    port_a = km_spcd_intcore.getPort_a();
                    port_b = km_spcd_intcore.getPort_b();
                    //给第一个屏柜设置odf数据(unit.name-unit.desc)
                    StringBuffer stringBuffer = new StringBuffer(" ");
                    stringBuffer.append(km_spcd_unit.getName())
                            .append("-")
                            .append(km_spcd_unit.getDescription());
                    cubicleGroupBaseBean1.setOdf_name(stringBuffer.toString());
                    cubicleGroupBaseBean1.setOdf_portFrNo(split2[2]);
                    cubicleGroupBaseBean1.setLocationOf(FROM);
                    instance.setLink_type(IEDsWholeCircuitDataPool.TYPE_1IED_IN_1CUBICULE_BY_ODF);
                    cubicles.add(cubicleGroupBaseBean1);
                }else {
                    port_a = km_spcd_intcore0.getPort_a();
                    port_b = km_spcd_intcore0.getPort_b();
                }
                //继续查找
                String portOdf = null;
                if (type == 2 && port_a.startsWith(unitName)){//port_a 是odf
                    portOdf = port_a;
                }else if (type == 1 && port_b.startsWith(unitName)){//port_b 是odf
                    portOdf = port_b;
                }

                //改造如果是odf ----可先在本屏柜找是否有多个odf（待做）
                WholeCircuitBean.UnitWholeBean unitWholeBean = new WholeCircuitBean.UnitWholeBean();
                unitWholeBean.setKm_spcd_unit(km_spcd_unit);
                String otherOdf = null;
                if (noDir!=null && noDir.contains("-")){
                    String[] noDirSp = noDir.split("-");
                    unitWholeBean.setPortADirection(noDirSp[1]);
                    //查询该odf板卡
                    KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) boardDao.getFirstForEq("unit_id", km_spcd_unit.getId(), "slot", split2[1]);
                    List<KM_SPCD_PORT> ports = portDao.getListForEq("board_id", km_spcd_board.getId(), "no", noDirSp[0]);
                    unitWholeBean.setPortADesc(km_spcd_board.getDescription()+"-"+noDirSp[0]);
                    if (ports!=null && ports.size()!=0 && mType != 1){
                        for (KM_SPCD_PORT km_spcd_port:ports){
                            if (!km_spcd_port.getDirection().equals(noDirSp[1])){
                                unitWholeBean.setPortBDirection(km_spcd_port.getDirection());
                                //unitWholeBean.setPortBdsc(km_spcd_board.getDescription()+km_spcd_port.getNo());
                                unitWholeBean.setPortBDesc(km_spcd_board.getDescription()+"-"+km_spcd_port.getNo());//gy add
                                //同一根跳纤的另外一只odf
                                otherOdf = km_spcd_unit.getName()+"."+km_spcd_board.getSlot()+"."+km_spcd_port.getNo()+"-"+km_spcd_port.getDirection();
                            }
                        }
                    }
                }
                unitWholeBeanList.add(unitWholeBean);

                //根据odf去查找Cable连接
                String cubicleC = km_spcd_region.getName() +"." + cubicle.getName();//如：cubicleA="R66.XLKZ1A"
                //查找与portOdf相连的屏柜
                List<String> keys = new ArrayList<String>(asList("station_id", "cubicleA"));
                List<Object> value = new ArrayList<Object>(asList(km_spcd_substation.getId(),cubicleC));
                List<KM_SPCD_CABLE> cablesA = (List<KM_SPCD_CABLE>) cableDao.getListForEq(keys, value);//cubicleA是当前屏柜

                List<String> keys0 = new ArrayList<String>(asList("station_id", "cubicleB"));
                List<KM_SPCD_CABLE> cablesB = (List<KM_SPCD_CABLE>) cableDao.getListForEq(keys0, value);//cubicleA是当前屏柜

                analysisCables(cablesA,portOdf,km_spcd_intcore,1,otherOdf);
                analysisCables(cablesB,portOdf, km_spcd_intcore, 2,otherOdf);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private void analysisCables(List<KM_SPCD_CABLE> cables, String portOdf, KM_SPCD_INTCORE km_spcd_intcore, int type,String otherOdf) {
        try {
            if (cables!=null && cables.size()!=0){
                List<Integer> ids = new ArrayList<>();
                for (KM_SPCD_CABLE km_spcd_cable:cables){
//                    if ("GL".equals(km_spcd_cable.getType())){
                        ids.add(km_spcd_cable.getId());
//                    }
                }
                List<KM_SPCD_CORE> km_spcd_cores = coreDao.getListForIn("cable_id", ids);//找到与当前屏柜cubicleA相连的KM_SPCD_CORE集合
                if (km_spcd_cores!=null && km_spcd_cores.size()!=0){
                    for (KM_SPCD_CORE km_spcd_core:km_spcd_cores){
                        //cubicleA是相连的odf
                        String port_b1 = null;
                        String port_b22 = null;
                        String cubicleB = null;
                        KM_SPCD_CORE km_spcd_core1 = null;
                        KM_SPCD_CORE km_spcd_core2 = null;

                        if (type == 1 && km_spcd_core.getPort_a().equals(portOdf)){
                            port_b1 = km_spcd_core.getPort_b();//与之相连的对侧屏柜的odf
                            Integer cable_id = km_spcd_core.getCable_id();
                            KM_SPCD_CABLE km_spcd_cable = (KM_SPCD_CABLE)cableDao.getById(cable_id);
                            cubicleB = km_spcd_cable.getCubicleB();//对侧屏柜信息，如：R66.XLP1A
                            glNames.add(km_spcd_cable.getName());
                            km_spcd_core1 = km_spcd_core;
                        }else if (type == 2 && km_spcd_core.getPort_b().equals(portOdf)){
                            port_b1 = km_spcd_core.getPort_a();//与之相连的对侧屏柜的odf
                            Integer cable_id = km_spcd_core.getCable_id();
                            KM_SPCD_CABLE km_spcd_cable = (KM_SPCD_CABLE)cableDao.getById(cable_id);
                            cubicleB = km_spcd_cable.getCubicleA();//对侧屏柜信息，如：R66.XLP1A
                            glNames.add(km_spcd_cable.getName());
                            km_spcd_core1 = km_spcd_core;
                        }
                        if (type == 1 && km_spcd_core.getPort_a().equals(otherOdf)){
                            km_spcd_core2 = km_spcd_core;
                            port_b22 = km_spcd_core.getPort_b();//与之相连的对侧屏柜的odf
                        }else if (type == 2 && km_spcd_core.getPort_b().equals(otherOdf)){
                            km_spcd_core2 = km_spcd_core;
                            port_b22 = km_spcd_core.getPort_a();//与之相连的对侧屏柜的odf
                        }

                        if (!TextUtils.isEmpty(cubicleB) && cubicleB.contains(".")){
                            String[] split = cubicleB.split("\\.");
                            String region = split[0];//小室，如：R66
                            String cubicle  = split[1];//屏柜，如：XLP1A
                            KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("substation_id", km_spcd_substation.getId(),"name",region);
                            KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE)cubicleDao.getFirstForEq
                                    ("region_id", km_spcd_region.getId(),"substation_id", km_spcd_substation.getId(), "name", cubicle);
                            if (km_spcd_cubicle!=null){
                                //改造
                                WholeCircuitBean.CubicleWholeBean cubicleWholeBean = new WholeCircuitBean.CubicleWholeBean();
                                List<WholeCircuitBean.UnitWholeBean> unitWholeBeanList = new ArrayList<>();
                                WholeCircuitBean.UnitWholeBean unitWholeBean = new WholeCircuitBean.UnitWholeBean();
                                unitWholeBeanList.add(unitWholeBean);
                                //unitWholeBean.setType(2);
                                cubicleWholeBean.setUnitWholeBeans(unitWholeBeanList);
                                list.add(cubicleWholeBean);
                                cubicleWholeBean.setKm_spcd_cubicle(km_spcd_cubicle);

                                //查出对侧odf装置
                                String[] split2 = port_b1.split("\\.");
                                KM_SPCD_UNIT odfUnit = (KM_SPCD_UNIT) unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", split2[0]);
                                //设置中间屏柜
                                IEDsWholeCircuitDataPool.CubicleGroupBaseBean cubicleGroupBaseBeanCenter = new IEDsWholeCircuitDataPool.CubicleGroupBaseBean();
                                cubicleGroupBaseBeanCenter.setCubicleName(buildCubiculeNameDesc(km_spcd_cubicle));
                                //改造
                                String noDes = split2[2];
                                String[] noDesSp = null;
                                if (!TextUtils.isEmpty(noDes) && noDes.contains("-")){
                                    noDesSp = noDes.split("-");
                                    unitWholeBean.setPortADirection(noDesSp[1]);
                                }
                                unitWholeBean.setKm_spcd_unit(odfUnit);
                                unitWholeBean.setKm_spcd_coreA(km_spcd_core1);
                                if (mType != 1){
                                    unitWholeBean.setKm_spcd_coreB(km_spcd_core2);
                                    if (!TextUtils.isEmpty(port_b22)){
                                        String[] split22 = port_b22.split("\\.");
                                        String noDes1 = split22[2];
                                        String[] noDesSp1 = null;
                                        if (!TextUtils.isEmpty(noDes1) && noDes1.contains("-")){
                                            noDesSp1 = noDes1.split("-");
                                            unitWholeBean.setPortBDirection(noDesSp1[1]);
                                        }
                                        KM_SPCD_UNIT odfUnit1 = (KM_SPCD_UNIT) unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", split22[0]);
                                        if (odfUnit1!=null){
                                            KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) boardDao.getFirstForEq("unit_id", odfUnit1.getId(), "slot", split22[1]);
                                            if (km_spcd_board!=null){
                                                //unitWholeBean.setPortBdsc(km_spcd_board.getDescription()+noDesSp1[0]);
                                                unitWholeBean.setPortBDesc(km_spcd_board.getDescription()+"-"+noDesSp1[0]);//gy add
                                            }
                                        }
                                    }
                                }

                                if (odfUnit!=null){
                                    //设置屏柜的odf名称(unit.name-unit.desc)
                                    StringBuffer stringBuffer = new StringBuffer(" ");
                                    stringBuffer.append(odfUnit.getName())
                                            .append("-")
                                            .append(odfUnit.getDescription());
                                    cubicleGroupBaseBeanCenter.setOdf_name(stringBuffer.toString());
                                    cubicleGroupBaseBeanCenter.setOdf_portToNo(split2[2]);

                                    //改造
                                    KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) boardDao.getFirstForEq("unit_id", odfUnit.getId(), "slot", split2[1]);
                                    if (km_spcd_board!=null){
                                        unitWholeBean.setPortADesc(km_spcd_board.getDescription()+"-"+noDesSp[0]);
                                    }
                                }

                                List<KM_SPCD_INTCORE> km_spcd_intcores0 = new ArrayList<>();
                                //查出port_a是odf的
                                List<String> keys1 = new ArrayList<String>(asList("cubicle_id", "port_a"));
                                List<Object> value1 = new ArrayList<Object>(asList(km_spcd_cubicle.getId(),port_b1));
                                List<KM_SPCD_INTCORE> km_spcd_intcores1 = intcoreDao.getListForEq(keys1, value1);
                                //查出port_b是odf的
                                List<String> keys2 = new ArrayList<String>(asList("cubicle_id", "port_b"));
                                List<KM_SPCD_INTCORE> km_spcd_intcores2 = intcoreDao.getListForEq(keys2, value1);
                                if (km_spcd_intcores1!=null && km_spcd_intcores1.size()!=0){
                                    km_spcd_intcores0.addAll(km_spcd_intcores1);
                                }
                                if (km_spcd_intcores2!=null && km_spcd_intcores2.size()!=0){
                                    km_spcd_intcores0.addAll(km_spcd_intcores2);
                                }

                                //改造
                                KM_SPCD_INTCORE km_spcd_intcore11 = null;
                                if (!TextUtils.isEmpty(port_b22)){
                                    List<KM_SPCD_INTCORE> km_spcd_intcores00 = new ArrayList<>();
                                    List<Object> value22 = new ArrayList<Object>(asList(km_spcd_cubicle.getId(),port_b1));
                                    List<KM_SPCD_INTCORE> km_spcd_intcores22 = intcoreDao.getListForEq(keys1, value22);
                                    List<KM_SPCD_INTCORE> km_spcd_intcores33 = intcoreDao.getListForEq(keys2, value22);
                                    if (km_spcd_intcores22!=null && km_spcd_intcores22.size()!=0){
                                        km_spcd_intcores00.addAll(km_spcd_intcores22);
                                    }
                                    if (km_spcd_intcores33!=null && km_spcd_intcores33.size()!=0){
                                        km_spcd_intcores00.addAll(km_spcd_intcores33);
                                    }
                                    if (km_spcd_intcores00!=null && km_spcd_intcores00.size()!=0){
                                        km_spcd_intcore11 = km_spcd_intcores00.get(0);
                                    }
                                }


                                if (km_spcd_intcores0!=null && km_spcd_intcores0.size()!=0){
                                    KM_SPCD_INTCORE km_spcd_intcore1 = km_spcd_intcores0.get(0);
                                    String port_a2 = km_spcd_intcore1.getPort_a();
                                    String port_b2 = km_spcd_intcore1.getPort_b();
                                    String[] split1 = null;
                                    int thisType = 0;
                                    if (port_a2.equals(port_b1)){
                                        thisType = 1;
                                        split1 = port_b2.split("\\.");
                                    }
                                    if (port_b2.equals(port_b1)){
                                        thisType = 2;
                                        split1 = port_a2.split("\\.");
                                    }
                                    if (split1!=null){
                                        String unitName1 = split1[0];//如：4n
                                        //查出与odfUnit相连的装置是否是odf，是与否都要设置值
                                        KM_SPCD_UNIT odfConnectUnit = (KM_SPCD_UNIT) unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", unitName1);
                                        String dev_class = odfConnectUnit.getDev_class();
                                        if ("ODF".equals(dev_class)){
                                            //设置屏柜的odf名称(unit.name-unit.desc)
                                            StringBuffer stringBuffer = new StringBuffer(" ");
                                            stringBuffer.append(km_spcd_unit.getName())
                                                    .append("-")
                                                    .append(km_spcd_unit.getDescription());
                                            cubicleGroupBaseBeanCenter.setOdf_name(stringBuffer.toString());
                                            cubicleGroupBaseBeanCenter.setLocationOf(PASS);
                                            cubicleGroupBaseBeanCenter.setOdf_portFrNo(split1[2]);
                                            cubicles.add(cubicleGroupBaseBeanCenter);//屏柜添加进集合
                                        }else {
                                            cubicleGroupBaseBeanCenter.setLocationOf(TO);
                                        }
                                        setOtherUnit(km_spcd_intcore,unitName1,km_spcd_intcore1,thisType,km_spcd_cubicle,cubicleGroupBaseBeanCenter,cubicleWholeBean,unitWholeBeanList,km_spcd_intcore11);
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
            }
        }catch (SQLException e){
            e.printStackTrace();
        }
    }

    private PortConnectBean.IntcoreBean setSwitchIedFinal(KM_SPCD_INTCORE km_spcd_intcore, String unitName, KM_SPCD_INTCORE km_spcd_intcore0, int type){
        PortConnectBean.IntcoreBean intcoreBean = new PortConnectBean.IntcoreBean();

        String port_a1 = km_spcd_intcore.getPort_a();
        String port_b1 = km_spcd_intcore.getPort_b();
        String[] splitA1 = port_a1.split("\\.");
        String[] splitB1 = port_b1.split("\\.");

        if (km_spcd_intcore0 == null){
            if (type == 1){
                intcoreBean.setBoardA(splitA1[1]);
                intcoreBean.setPortA(splitA1[2]);
                intcoreBean.setBoardB(splitB1[1]);
                intcoreBean.setPortB(splitB1[2]);
            }else if (type == 2){
                intcoreBean.setBoardA(splitB1[1]);
                intcoreBean.setPortA(splitB1[2]);
                intcoreBean.setBoardB(splitA1[1]);
                intcoreBean.setPortB(splitA1[2]);
            }
        }else{
            String port_a = km_spcd_intcore0.getPort_a();
            String port_b = km_spcd_intcore0.getPort_b();
            String[] splitA0 = port_a.split("\\.");
            String[] splitB0 = port_b.split("\\.");

            if (type == 1){
                intcoreBean.setBoardA(splitA1[1]);
                intcoreBean.setPortA(splitA1[2]);
                if (port_a.startsWith(unitName)){//port_a是对侧ied----注意：name=TX01未改值
                    intcoreBean.setBoardB(splitA0[1]);
                    intcoreBean.setPortB(splitA0[2]);
                }else if (port_b.startsWith(unitName)){//port_b是对侧ied----注意：name=TX01未改值
                    intcoreBean.setBoardB(splitB0[1]);
                    intcoreBean.setPortB(splitB0[2]);
                }
            }else if (type == 2){
                intcoreBean.setBoardA(splitB1[1]);
                intcoreBean.setPortA(splitB1[2]);
                if (port_a.startsWith(unitName)){//port_a是对侧ied----注意：name=TX01未改值
                    intcoreBean.setBoardB(splitA0[1]);
                    intcoreBean.setPortB(splitA0[2]);
                }else if (port_b.startsWith(unitName)){//port_b是对侧ied----注意：name=TX01未改值
                    intcoreBean.setBoardB(splitB0[1]);
                    intcoreBean.setPortB(splitB0[2]);
                }
            }
        }
        return intcoreBean;
    }


    private KM_SPCD_INTCORE setSwitchIed(KM_SPCD_INTCORE km_spcd_intcore, String unitName, KM_SPCD_INTCORE km_spcd_intcore0, int type){
        if (type == 1){
            String port_a = km_spcd_intcore0.getPort_a();
            String port_b = km_spcd_intcore0.getPort_b();
            if (port_a.startsWith(unitName)){//port_a是对侧ied----注意：name=TX01未改值
                km_spcd_intcore.setPort_b(km_spcd_intcore0.getPort_a());
            }else if (port_b.startsWith(unitName)){//port_b是对侧ied----注意：name=TX01未改值
                km_spcd_intcore.setPort_b(km_spcd_intcore0.getPort_b());
            }
        }else if (type == 2){
            String port_a = km_spcd_intcore0.getPort_a();
            String port_b = km_spcd_intcore0.getPort_b();
            if (port_a.startsWith(unitName)){//port_a是对侧ied----注意：name=TX01未改值
                km_spcd_intcore.setPort_a(km_spcd_intcore0.getPort_a());
            }else if (port_b.startsWith(unitName)){//port_b是对侧ied----注意：name=TX01未改值
                km_spcd_intcore.setPort_a(km_spcd_intcore0.getPort_b());
            }
        }
        return km_spcd_intcore;
    }


    private PortConnectBean.IedConnectBean getIedConnectBean(KM_SPCD_UNIT km_spcd_unit) {
        PortConnectBean.IedConnectBean  iedConnect= null;
        if (iedList!=null && iedList.size()!=0){
            for (PortConnectBean.IedConnectBean iedConnectBean:iedList){
                KM_SPCD_UNIT otherUnit = iedConnectBean.getOtherUnit();
                if (otherUnit.getId().intValue() == km_spcd_unit.getId().intValue()){
                    iedConnect = iedConnectBean;
                    break;
                }
            }
        }
        return iedConnect;
    }

    private void setWLconnect() {
        //拼装当前装置的尾缆cubicleA="R66.XLKZ1A"
        String cubicleC= km_spcd_region.getName()+"."+km_spcd_cubicle.getName();
        List<String> keys1 = new ArrayList<String>(asList("station_id", "cubicleA","type"));
        List<String> keys2 = new ArrayList<String>(asList("station_id", "cubicleB","type"));
        List<Object> value = new ArrayList<Object>(asList(km_spcd_substation.getId(),cubicleC,"WL"));
        try {
            List<KM_SPCD_CABLE> cables = new ArrayList<>();
            List<KM_SPCD_CABLE> cables1 = (List<KM_SPCD_CABLE>)cableDao.getListForEq(keys1, value);
            List<KM_SPCD_CABLE> cables2 = (List<KM_SPCD_CABLE>)cableDao.getListForEq(keys2, value);
            if (cables1!=null && cables1.size()!=0){
                cables.addAll(cables1);
            }
            if (cables2!=null && cables2.size()!=0){
                cables.addAll(cables2);
            }
            if (cables.size()!=0){
                int isGo = -1;
                for (KM_SPCD_CABLE km_spcd_cable:cables){
                    if (isGo == 1){
                        break;
                    }
                    String cubicleA = km_spcd_cable.getCubicleA();
                    String cubicleB = km_spcd_cable.getCubicleB();
                    List<KM_SPCD_CORE> cores = coreDao.getListForEq("cable_id", km_spcd_cable.getId());

                    WholeCircuitBean.CubicleWholeBean cubicleWholeBean = new WholeCircuitBean.CubicleWholeBean();
                    List<WholeCircuitBean.UnitWholeBean> unitWholeBeanList = new ArrayList<>();
                    WholeCircuitBean.UnitWholeBean unitWholeBean = new WholeCircuitBean.UnitWholeBean();
                    unitWholeBeanList.add(unitWholeBean);
                    cubicleWholeBean.setUnitWholeBeans(unitWholeBeanList);

                    if (cubicleA.equals(cubicleC)){
//                        coresWl(km_spcd_cable,cubicleB,1);
                        if (cores!=null && cores.size()!=0){
                            String port = null;
                            KM_SPCD_CUBICLE cubicle = null;
                            for (KM_SPCD_CORE km_spcd_core:cores){
                                String port_a = km_spcd_core.getPort_a();
                                String port_b = km_spcd_core.getPort_b();
                                if (port0.equals(port_a)){//尾缆找到对侧屏柜连接，停止循环
                                    String[] split2 = port_b.split("\\.");
                                    //找对侧屏柜信息
                                    KM_SPCD_CUBICLE cubicle2 = getcubicle(km_spcd_cubicle.getId(), cubicleB);
                                    if (cubicle2 == null){
                                        break;
                                    }
                                    //找对侧装置
                                    KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicleB, port_b);
                                    //改造
                                    cubicleWholeBean.setKm_spcd_cubicle(cubicle2);
                                    //unitWholeBean.setType(1);
                                    unitWholeBean.setKm_spcd_unit(unit);
                                    unitWholeBean.setBoardPort(getBoardPort(port_b));
                                    Utils.setBoardPort1(unitWholeBean,port_b);
                                    unitWholeBean.setPortADirection(getNoDes(port_b,2));
//                                    setBoardPort(cubicle2,port_b,unitWholeBean);
                                    if (mType != 1){
                                        unitWholeBean.setPortBDirection(getOtherPort(cubicle2,port_b,1));
                                    }
                                    unitWholeBean.setKm_spcd_coreA(km_spcd_core);
                                    list.add(cubicleWholeBean);
                                    //设置对侧装置
                                    setOtherCubicle(unit,split2,cubicle2);
                                    isGo = 1;//找到之后结束外面的循环标志
                                    port = port_a;
                                    cubicle = cubicle2;
                                    break;
                                }
                            }
                            if (!TextUtils.isEmpty(port) && mType != 1){
                                for (KM_SPCD_CORE km_spcd_core1:cores){
                                    if (getOtherPort(cubicle,port,2).equals(km_spcd_core1.getPort_a())){
                                        unitWholeBean.setKm_spcd_coreB(km_spcd_core1);
                                        break;
                                    }
                                }
                            }
                        }
                    }else if (cubicleB.equals(cubicleC)){
//                        coresWl(km_spcd_cable,cubicleA,2);
                        if (cores!=null && cores.size()!=0){
                            String port = null;
                            KM_SPCD_CUBICLE cubicle = null;
                            for (KM_SPCD_CORE km_spcd_core:cores){
                                String port_a = km_spcd_core.getPort_a();
                                String port_b = km_spcd_core.getPort_b();
                                if (port0.equals(port_b)){//尾缆找到对侧屏柜连接，停止循环
                                    String[] split2 = port_a.split("\\.");
                                    //找对侧屏柜信息
                                    KM_SPCD_CUBICLE cubicle2 = getcubicle(km_spcd_cubicle.getId(), cubicleA);
                                    if (cubicle2 == null){
                                        break;
                                    }
                                    //找对侧装置
                                    KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicleA, port_a);
                                    //改造
                                    cubicleWholeBean.setKm_spcd_cubicle(cubicle2);
                                    //unitWholeBean.setType(1);
                                    unitWholeBean.setKm_spcd_unit(unit);
                                    unitWholeBean.setBoardPort(getBoardPort(port_a));
                                    Utils.setBoardPort1(unitWholeBean,port_a);
                                    unitWholeBean.setPortADirection(getNoDes(port_a,2));
                                    if (mType != 1){
                                        unitWholeBean.setPortBDirection(getOtherPort(cubicle2,port_a,1));
                                    }
                                    unitWholeBean.setKm_spcd_coreA(km_spcd_core);
                                    list.add(cubicleWholeBean);
                                    //设置对侧装置
                                    setOtherCubicle(unit,split2,cubicle2);
                                    isGo = 1;//找到之后结束外面的循环标志
                                    port = port_b;
                                    cubicle = cubicle2;
                                    break;
                                }
                            }
                            if (!TextUtils.isEmpty(port) && mType != 1){
                                for (KM_SPCD_CORE km_spcd_core1:cores){
                                    if (km_spcd_core1.getPort_b().equals(getOtherPort(cubicle,port,2))){
                                        unitWholeBean.setKm_spcd_coreB(km_spcd_core1);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private void setBoardPort(KM_SPCD_CUBICLE cubicle, String port, WholeCircuitBean.UnitWholeBean unitWholeBean) {
        if (!TextUtils.isEmpty(port)){
            String[] split = port.split("\\.");
            KM_SPCD_UNIT km_spcd_unitA = null;
            try {
                km_spcd_unitA = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", cubicle.getId(), "name", split[0]);
                KM_SPCD_BOARD km_spcd_boardA = (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unitA.getId(), "slot", split[1]);
                unitWholeBean.setKm_spcd_boardA(km_spcd_boardA);
                if (!TextUtils.isEmpty(split[2]) && split[2].contains("-")){
                    String[] split5 = split[2].split("-");
                    List<KM_SPCD_PORT> ports = (List<KM_SPCD_PORT>) portDao.getListForEq("board_id", km_spcd_boardA.getId(), "no", split5[0]);
                    if (ports!=null && ports.size()!=0){
                        for (KM_SPCD_PORT km_spcd_port:ports){
                            if (!km_spcd_port.getDirection().equals(split5[1])){
                                unitWholeBean.setKm_spcd_portA(km_spcd_port);
                            }
                        }
                    }
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    private String getOtherPort(KM_SPCD_CUBICLE cubicle,String unit,Integer type){
        String[] split4 = unit.split("\\.");
        String s1 = split4[2];
        KM_SPCD_UNIT km_spcd_unitA = null;
        try {
            km_spcd_unitA = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", cubicle.getId(), "name", split4[0]);
            KM_SPCD_BOARD km_spcd_boardA = (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unitA.getId(), "slot", split4[1]);
            if (!TextUtils.isEmpty(s1) && s1.contains("-")){
                String[] split5 = s1.split("-");
                if (km_spcd_boardA!=null){
                    List<KM_SPCD_PORT> ports = (List<KM_SPCD_PORT>) portDao.getListForEq("board_id", km_spcd_boardA.getId(), "no", split5[0]);
                    if (ports!=null && ports.size()!=0){
                        for (KM_SPCD_PORT km_spcd_port:ports){
                            if (!km_spcd_port.getDirection().equals(split5[1])){
                                if (type == 1){
                                    return km_spcd_port.getDirection();
                                }else if (type == 2){
                                    return km_spcd_unitA.getName()+"."+km_spcd_boardA.getSlot()+"."+km_spcd_port.getNo()+"-"+km_spcd_port.getDirection();
                                }
                            }
                        }
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    private String getBoardPort(String port_a) {
        String[] split = port_a.split("\\.");
        String s = split[2];
        String boardPort = null;
        if (!TextUtils.isEmpty(s) && s.contains("-")){
            String[] split1 = s.split("-");
            boardPort = split[1] + "-" + split1[0];
            return boardPort;
        }
        return null;
    }

    private void setOtherCubicle(KM_SPCD_UNIT unit, String[] split2, KM_SPCD_CUBICLE cubicle2){
        //设置对侧装置
        List<IEDsWholeCircuitDataPool.IED> ieds2 = new ArrayList<>();
        IEDsWholeCircuitDataPool.IED ied2 = new IEDsWholeCircuitDataPool.IED();
        ied2.setIedName(buildUnitNameDesc(unit));
        ied2.setIed_portNo(split2[2]);
        ieds2.add(ied2);

        IEDsWholeCircuitDataPool.CubicleGroupBaseBean cubicleGroupBaseBean2 = new IEDsWholeCircuitDataPool.CubicleGroupBaseBean();
        cubicleGroupBaseBean2.setCubicleName(buildCubiculeNameDesc(cubicle2));
        cubicleGroupBaseBean2.setIeds(ieds2);
        //设置类型
        cubicleGroupBaseBean2.setLocationOf(DOUBLE_TO);
        cubicleGroupBaseBean1.setLocationOf(DOUBLE_FROM);
        //设置连接类型
        instance.setLink_type(IEDsWholeCircuitDataPool.TYPE_1IED_IN_1CUBICULE_STRAIT_THR);

        //3.添加第一个屏柜
        cubicles.add(cubicleGroupBaseBean1);
        cubicles.add(cubicleGroupBaseBean2);
    }

    private void coresWl(KM_SPCD_CABLE km_spcd_cable, String cubicle0, int type){
        List<KM_SPCD_CORE> cores = coreDao.getListForEq("cable_id", km_spcd_cable.getId());
        if (cores!=null && cores.size()!=0){
            for (KM_SPCD_CORE km_spcd_core:cores){
                String port_a = km_spcd_core.getPort_a();
                String port_b = km_spcd_core.getPort_b();
                PortConnectBean.IedConnectBean iedConnectBean = null;
                if (type == 1){//port_a是本屏柜装置，port_b是对侧屏柜装置
                    KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicle0, port_b);
                    if (unit!=null){
                        KM_SPCD_INTCORE km_spcd_intcore = new KM_SPCD_INTCORE();
                        km_spcd_intcore.setPort_a(port_a);
                        km_spcd_intcore.setPort_b(port_b);
                        setIedConnectBean(unit,km_spcd_intcore,1);
                    }
                }else if (type == 2){
                    KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicle0, port_a);
                    if (unit!=null){
                        KM_SPCD_INTCORE km_spcd_intcore = new KM_SPCD_INTCORE();
                        km_spcd_intcore.setPort_a(port_a);
                        km_spcd_intcore.setPort_b(port_b);
                        setIedConnectBean(unit,km_spcd_intcore,2);
                    }
                }
            }
        }
    }

    private void setIedConnectBean(KM_SPCD_UNIT unit,KM_SPCD_INTCORE km_spcd_intcore,Integer type){
        PortConnectBean.IedConnectBean iedConnectBean = getIedConnectBean(unit);
        List<KM_SPCD_INTCORE> intcores;
        List<PortConnectBean.IntcoreBean> intcoress;
        if (iedConnectBean == null){
            iedConnectBean = new PortConnectBean.IedConnectBean();
            intcores = new ArrayList<>();
            intcoress = new ArrayList<>();
            iedConnectBean.setOtherUnit(unit);
            PortConnectBean.IntcoreBean intcoreBean = setSwitchIedFinal(km_spcd_intcore, null, null, type);
            intcores.add(km_spcd_intcore);
            intcoress.add(intcoreBean);

            iedConnectBean.setIntcores(intcores);
            iedConnectBean.setIntcoress(intcoress);
            iedList.add(iedConnectBean);
        }else {
            intcores = iedConnectBean.getIntcores();
            intcoress = iedConnectBean.getIntcoress();
            PortConnectBean.IntcoreBean intcoreBean = setSwitchIedFinal(km_spcd_intcore, null, null, type);
            intcores.add(km_spcd_intcore);
            intcoress.add(intcoreBean);
            iedConnectBean.setIntcores(intcores);
            iedConnectBean.setIntcoress(intcoress);
        }
    }

    //通过core里面的portA="1n.1.A-Tx"，如1n去找到该装置
    private KM_SPCD_UNIT getUnit(Integer stationId, String cubicleA, String port_a) {
        if (!TextUtils.isEmpty(cubicleA) && !TextUtils.isEmpty(port_a)){
            String[] split = cubicleA.split("\\.");
            String region = split[0];//小室
            String cubicle = split[1];//屏柜
            String[] split1 = port_a.split("\\.");
            String unitName = split1[0];//unitName
            //查询小室id
            try {
                KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("substation_id", stationId,"name",region);
                KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getFirstForEq("region_id", km_spcd_region.getId(), "substation_id", stationId,"name",cubicle);
                if (km_spcd_cubicle!=null){
                    KM_SPCD_UNIT km_spcd_unit = (KM_SPCD_UNIT) unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", unitName);
                    return km_spcd_unit;
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    //通过core里面的portA="1n.1.A-Tx"，如1n去找到该装置
    private KM_SPCD_CUBICLE getcubicle(Integer stationId, String cubicleA) {
        if (!TextUtils.isEmpty(cubicleA)){
            String[] split = cubicleA.split("\\.");
            String region = split[0];//小室
            String cubicle = split[1];//屏柜
            //查询小室id
            try {
                KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("substation_id", stationId,"name",region);
                if (km_spcd_region == null){
                    return null;
                }
                KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getFirstForEq("region_id", km_spcd_region.getId(), "substation_id", stationId,"name",cubicle);
                if (km_spcd_cubicle!=null){
                    return km_spcd_cubicle;
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static String buildCubiculeNameDesc(KM_SPCD_CUBICLE cubicle){
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(cubicle.getName())
                .append("-")
                .append(cubicle.getDescription());
        return stringBuffer.toString();
    }

    public static String buildUnitNameDesc(KM_SPCD_UNIT unit){
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(unit.getName())
                .append("-")
                .append(unit.getDescription());
        return stringBuffer.toString();
    }
}
