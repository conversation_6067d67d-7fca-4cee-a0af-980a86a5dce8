package com.kemov.visual.spcd.spcdvisualandroidapp.utils;

import android.content.Context;
import android.content.SharedPreferences;

public class PrefReader {

    private static PrefReader mInstance = null;
    private SharedPreferences pref;

    private PrefReader(Context context) {
        pref = context.getSharedPreferences(Config.PREF_KEY, Context.MODE_PRIVATE);
    }

    public synchronized static PrefReader getInstance(Context context) {
        if(mInstance == null) {
            mInstance = new PrefReader(context);
        }
        return mInstance;
    }


    public String get(String key, String defaultValue) {
        return pref.getString(key, defaultValue);
    }

    public String get(String defaultValue) {
        return pref.getString("dbName", defaultValue);
    }

    public Integer getInt(String key,Integer defaultValue) {
        return pref.getInt(key, defaultValue);
    }
}
