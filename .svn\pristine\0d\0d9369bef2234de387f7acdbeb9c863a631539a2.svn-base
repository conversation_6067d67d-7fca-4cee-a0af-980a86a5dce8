package com.kemov.visual.spcd.spcdvisualandroidapp.visualview;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.RectF;
import android.os.Handler;
import android.os.Message;
import android.support.annotation.Nullable;
import android.text.Layout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Toast;

import com.baseres.base.PubApp;
import com.kemov.parsescl.KIEDModel;
import com.kemov.sclaata.common.app.PubUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.DataProcessTools;
import com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.IEDVisualActivityNew;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.GoosePortBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.DeviceBackPanelDataPool;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.cubicle_back_panel.BoardBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.cubicle_back_panel.CubicleBackPanelConstants;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.cubicle_back_panel.IedBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.cubicle_back_panel.PlugBaseSvgBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.cubicle_back_panel.PlugIedSvgBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.cubicle_back_panel.PlugSwitchSvgBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.cubicle_back_panel.SwitchBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.cubicle_back_panel.UnitBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.utils.DrawUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Constants;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Utils;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.XshlUtils;
import com.share.mycustomviewlib.bean.DataExchangeCachePool;
import com.share.mycustomviewlib.bean.VirExtRefBean;
import com.share.mycustomviewlib.bean.VirFCDABean;
import com.share.mycustomviewlib.bean.XshlBean;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.kemov.visual.spcd.spcdvisualandroidapp.visualview.CubicleBackPanelView.NA;
import com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.*;


//绘制装置的 背面板图
public class DeviceBackPanelView extends View {
    private static final String TAG = "DeviceBackPanelView";
    Context  mCtx;
    private static final boolean SHOW_DRAWING_HELPER = false;//开启辅助线

    //定义广播action
    public static final String ACTION_PORT_ID_CHANGE_ONSIDE = "ACTION_PORT_ID_CHANGE_ONSIDE";
    public static final String ACTION_PORT_ID_CHANGE_OFFSIDE = "ACTION_PORT_ID_CHANGE_OFFSIDE";

    private static final int MSG_DLG_SHOW = 0x0001;
    private static final int MSG_DLG_DISMISS = 0x0002;

    private float eventX, eventY;
    TextPaint txtPaint = null;
    TextPaint leftTxtPaint = null;
    TextPaint rightTxtPaint = null;
    Paint mPaint = null;

    private float mWidth = 0;
    private float mHeight = 0;
    private float ratio = 1080.0f/680;//自定义控件宽高比

    DeviceBackPanelDataPool cacheData = null;
    UnitBean unitBean = null;
    int portId = -1;

    MyProgressDialog dialog = null;
    Handler handler = new Handler(){
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what){
                case MSG_DLG_SHOW:
                    dialog.show();
                    break;
                case MSG_DLG_DISMISS:
                    requestLayout();
                    dialog.dismiss();
                    break;
            }
        }
    };

    public DeviceBackPanelView(Context context) {
        this(context,null);
    }

    public DeviceBackPanelView(Context context, @Nullable AttributeSet attrs) {
        this(context,attrs,0);
    }

    public DeviceBackPanelView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context,attrs,defStyleAttr,0);
    }

    public DeviceBackPanelView(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initPaint();

        mCtx = context;
        cacheData = DeviceBackPanelDataPool.getInstance();
        unitBean = new UnitBean();
        dialog = new MyProgressDialog(mCtx);
    }


    //Activity赋值传参数！
    public void setUnitId(final int unitId){
        // if (cacheData!=null&&cacheData.getUnit()!=null&&cacheData.getUnit().getId()==unitId) return;
        handler.sendEmptyMessage(MSG_DLG_SHOW);
        new Thread(){
            @Override
            public void run() {
                cacheData.setDevice(mCtx,unitId);
                dataProcessBeforeDraw();
                handler.sendEmptyMessage(MSG_DLG_DISMISS);
            }
        }.start();

    }
    public void setHightLightPort(int portId) {
        this.portId = portId;
    }
    public int getHightLightPort()
    {
        return this.portId;
    }
    private void initPaint() {
        txtPaint = new TextPaint();
        txtPaint.setAntiAlias(true);
        //txtPaint.setStrokeWidth(DisplayUtil.dip2sp(mCtx,2));
        txtPaint.setTextAlign(Paint.Align.CENTER);
        //txtPaint.setTextSize(DisplayUtil.dip2sp(mCtx,24));

        leftTxtPaint = new TextPaint(txtPaint);
        leftTxtPaint.setTextAlign(Paint.Align.LEFT);
        rightTxtPaint = new TextPaint(txtPaint);
        rightTxtPaint.setTextAlign(Paint.Align.RIGHT);

        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        //mPaint.setColor(0x7aff0000);
        mPaint.setColor(0xff000000);//0x7a008080 IED_BG_COLOR
        mPaint.setStrokeWidth(3);
        //mPaint.setStyle(Paint.Style.FILL);
        mPaint.setStyle(Paint.Style.STROKE);

    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        setMeasuredDimension((int)mWidth, (int)mHeight);
        //测量+赋值各个矩形框的参数。
        int widthSpecMode = MeasureSpec.getMode(widthMeasureSpec);
        int widthSpecSize = MeasureSpec.getSize(widthMeasureSpec);
        int heightSpecMode = MeasureSpec.getMode(heightMeasureSpec);
        int heightSpecSize = MeasureSpec.getSize(heightMeasureSpec);
        if (widthSpecMode == MeasureSpec.AT_MOST
                && heightSpecMode == MeasureSpec.AT_MOST) {
            setMeasuredDimension(Utils.dipToPixel(getContext(), 150), Utils.dipToPixel(getContext(), 150));// dip2px(150), dip2px(200));
        } else if (widthSpecMode == MeasureSpec.AT_MOST) {
            setMeasuredDimension((int) (heightSpecSize * 0.75f), heightSpecSize);
        } else if (heightSpecMode == MeasureSpec.AT_MOST) {
            setMeasuredDimension(widthSpecSize, (int) (widthSpecSize / 0.75f));
        }
        /***/
        if (widthSpecMode == MeasureSpec.EXACTLY
                && heightSpecMode != MeasureSpec.EXACTLY && ratio != 0.0f) {
            // 判断条件为，宽度模式为Exactly，也就是填充父窗体或者是指定宽度；
            // 且高度模式不是Exaclty，代表设置的既不是fill_parent也不是具体的值，于是需要具体测量
            // 且图片的宽高比已经赋值完毕，不再是0.0f
            // 表示宽度确定，要测量高度
            heightSpecSize = (int) (widthSpecSize / ratio + 0.5f);
            heightMeasureSpec = MeasureSpec.makeMeasureSpec(heightSpecSize,
                    MeasureSpec.EXACTLY);
        } else if (widthSpecMode != MeasureSpec.EXACTLY
                && heightSpecMode == MeasureSpec.EXACTLY && ratio != 0.0f) {
            // 判断条件跟上面的相反，宽度方向和高度方向的条件互换
            // 表示高度确定，要测量宽度
            widthSpecSize = (int) (heightSpecSize * ratio + 0.5f);

            widthMeasureSpec = MeasureSpec.makeMeasureSpec(widthSpecSize,
                    MeasureSpec.EXACTLY);
        }
    }

    private void dataProcessBeforeDraw(){
        //计算整个屏柜的【宽】【高】！
        Map<String,Float> cubicle_w_h_map = cacheData.getCubWidthHeightForCubBackPanel();
        float cubicle_width = cubicle_w_h_map.get("width");
        float cubicle_height = cubicle_w_h_map.get("height");

        KM_SPCD_UNIT unit = cacheData.getUnit();
        if (unit == null) return;
        int i = 0;
        if (unit.getDev_class().equals("IED")){
            IedBean iedBean = new IedBean();

            iedBean.setName(unit.getDescription()+"["+unit.getName()+"]");//第一排：desc[name]
            iedBean.setIdentifier(unit.getDev_type()+"/"+unit.getIed_name()) ;//第二排：type
            iedBean.setRectF(cacheData.getIedRectForCubBackPanel(i));

            List<BoardBean> boards = new ArrayList<>();
            for (int j = 0; j < unit.boards.size(); j++) {
                KM_SPCD_BOARD board = unit.boards.get(j);
                BoardBean boardBean = new BoardBean();
                boardBean.setName(board.getSlot());//type 标准附录有例子type为型号
                boardBean.setDesc(board.getType());
                //统一ied内部所有的board的高度都为最高的那个板卡的高度
                RectF boardRect = new RectF(cacheData.getBoardRectForCubBackPanel(i,j));
                boardRect = new RectF(boardRect.left,boardRect.top,boardRect.right,
                        boardRect.top+ cacheData.getBoardHeightByIEDHeight(iedBean.getRectF().height()));
                boardBean.setRectF(boardRect);

                //board_Port_Bean(组装数据)
                List<PlugIedSvgBean> plugIedSvgBeans  = new ArrayList<>();
                //需要对该ports进行处理，找出成对的cp_ports
                List<KM_SPCD_PORT> ports = unit.boards.get(j).ports;
                //调试代码
                /*StringBuffer sb = new StringBuffer("[");
                for (KM_SPCD_PORT pp:ports){
                    sb.append(pp.getId()+",");
                }
                sb.append("]");
                Log.d(TAG, "gg-high_light: 所有portIds="+sb.toString());
                Log.d(TAG, "gg-high_light: 高亮portId="+portId );*/

                Map<String,List<KM_SPCD_PORT>> map = getPortPortsByNo(board, ports);

                //port绘制
                int index_plugin_temp = 0;
                for (String no:  map.keySet()){
                    List<KM_SPCD_PORT> cp_ports = map.get(no);//PORT:plug表示接口类型，枚举值为LC、ST、SC、FC、RJ45

                    PlugIedSvgBean plugIedSvgBean = new PlugIedSvgBean(mCtx);

                    PlugBaseSvgBean.SvgType svgType = PlugIedSvgBean.getSvgTypeByPlug(cp_ports.size(),cp_ports.get(0).getPlug(),cp_ports.get(0).getDirection());
                    Log.d(TAG, "onLayout: cp_ports.get(0).getPlug()="+cp_ports.get(0).getPlug());
                    Log.d(TAG, "onLayout: svgType="+svgType.toString());
                    plugIedSvgBean.setSvgType(svgType);
                    plugIedSvgBean.setRectF(cacheData.getPluginRectForCubBackPanel(i,j,index_plugin_temp));
                    //TODO:计算：求关联！
                    plugIedSvgBean.setOppositeUnitDesc("对侧屏柜名称");
                    plugIedSvgBean.setBoard_type(board.getType());
                    plugIedSvgBean.setPort_no(no);
                    plugIedSvgBean.setSingle_port_id(cp_ports.get(0).getId());//传入单个portID
                    plugIedSvgBean.setPorts(cp_ports);

                    {//配置光纤的指定端口的另端信息
                        Map<String,String> the_other_side_port_info_map = null;
                        String cubicle_desc = NA;
                        String unit_name_desc = NA;
                        String board_type = NA;
                        String gsxh = null;
                        try {
                            the_other_side_port_info_map = cacheData.getPortToInfoV2(cp_ports.get(0).getId());
                        } catch (SQLException e) {
                            e.printStackTrace();
                        }
                        if (the_other_side_port_info_map != null) {
                            cubicle_desc = the_other_side_port_info_map.get("cubicle_desc");
                            unit_name_desc = the_other_side_port_info_map.get("unit_name_desc");
                            board_type = the_other_side_port_info_map.get("board_type");
                            gsxh = the_other_side_port_info_map.get("gsxh");
                        }
                        plugIedSvgBean.setOppositeUnitDesc(unit_name_desc);//张炳旺要求不显示屏柜，而是显示装置信息
                        //信号类型 TODO：先用假数据，解决性能问题
                        if (!TextUtils.isEmpty(gsxh)){
                            board_type = gsxh;
                        }else {
                            board_type = "GS";
                        }
                        board_type = DataProcessTools.getSignalTypeWithPriCfg(mCtx,cp_ports.get(0).getId());
                        plugIedSvgBean.setBoard_type(board_type);//修改显示-信号类型
                    }

                    plugIedSvgBeans.add(plugIedSvgBean);
                    index_plugin_temp++;
                }

                boardBean.setPlugSvgBeanList(plugIedSvgBeans);

                boards.add(boardBean);
            }
            iedBean.setBoards(boards);
            unitBean = iedBean;
            //ides.add(unitBean);
        } else if (unit.getDev_class().equals("SWITCH")){
            SwitchBean switchBean = new SwitchBean();
            switchBean.setName(unit.getIed_name()+"["+unit.getName()+"]");
            switchBean.setIdentifier(unit.getDev_type()) ;//第二排：type
            switchBean.setRectF(cacheData.getIedRectForCubBackPanel(i));
            //交换机只有一个board
            //设置交换机的绘制方式，通过其板卡下的所有的port来断定是否需要按照9800的绘制方式来绘制
            int switch_board_size = unit.boards.size();
            if (switch_board_size==0) return;
            KM_SPCD_BOARD switch_board = unit.boards.get(0);
            RectF upsideRect = new RectF(cacheData.getBoardRectForCubBackPanel(i,0));
            RectF downsideRect = new RectF(cacheData.getBoardRectForCubBackPanel(i,1));
            switchBean.setUpsideRect(upsideRect);//上层
            switchBean.setDownsideRect(downsideRect);//下层

            //需要对该ports进行处理，找出成对的cp_ports
            Map<String,List<KM_SPCD_PORT>> mapNoPorts = getPortPortsByNo(switch_board, switch_board.ports);
            switchBean.setPriCfg(cacheData.isUsePriCfgForCubBackPanel(i,0));
            switchBean.initUpDownSides(mapNoPorts);//将端口放到交换机的上下两侧。

            //port绘制
            //List<PlugSwitchSvgBean> plugIedSvgBeans  = new ArrayList<>();
            //initSwitchPlugins(i, switchBean, plugIedSvgBeans);
            switchBean.setUpPlugSwitchSvgBeans(getValuedSwitchPlugins(i,switchBean.getMapNoPorts_Upside(),true));
            switchBean.setDownPlugSwitchSvgBeans(getValuedSwitchPlugins(i,switchBean.getMapNoPorts_Downside(),false));
            unitBean = switchBean;
            //ides.add(switchBean);
        } else {
            throw new RuntimeException("error!");
        }
        //cubicleBackPanelBean.setIdes(ides);
        this.cubicle_width = cubicle_width;
        this.cubicle_height = cubicle_height;
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        if (cacheData == null) return;

        //横向图形显示完整
        RequestLayout_FillWidth();
    }

    private List<PlugSwitchSvgBean> getValuedSwitchPlugins(int i, Map<String, List<KM_SPCD_PORT>> map, boolean isUp) {
        List<PlugSwitchSvgBean> plugIedSvgBeans = new ArrayList<>();
        int index_plugin_temp = 0;
        for (String no:  map.keySet()){
            List<KM_SPCD_PORT> cp_ports = map.get(no);//PORT:plug表示接口类型，枚举值为LC、ST、SC、FC、RJ45

            PlugSwitchSvgBean plugSwitchSvgBean = new PlugSwitchSvgBean(mCtx);
            plugSwitchSvgBean.setUpside(isUp);
            PlugBaseSvgBean.SvgType svgType = PlugSwitchSvgBean.getSvgTypeByPlug(cp_ports.size(),cp_ports.get(0).getPlug(),cp_ports.get(0).getDirection());
            Log.d(TAG, "onLayout: cp_ports.get(0).getPlug()="+cp_ports.get(0).getPlug());
            Log.d(TAG, "onLayout: svgType="+svgType.toString());
            plugSwitchSvgBean.setSvgType(svgType);
            plugSwitchSvgBean.setRectF(cacheData.getSwitchPluginRectForCubBackPanel(i,isUp,index_plugin_temp));
            //TODO:计算：求关联！
            plugSwitchSvgBean.setOppositeUnitDesc("对侧屏柜名称");
            plugSwitchSvgBean.setPort_no(no);
            plugSwitchSvgBean.setPorts(cp_ports);
            plugSwitchSvgBean.setSingle_port_id(cp_ports.get(0).getId());//传入单个portID

            {//配置光纤的指定端口的另端信息
                Map<String,String> the_other_side_port_info_map = null;
                String cubicle_desc = NA;
                String unit_name_desc = NA;
                String board_type = NA;
                try {
                    the_other_side_port_info_map = cacheData.getPortToInfoV2(cp_ports.get(0).getId());
                } catch (SQLException e) {
                    e.printStackTrace();
                }
                if (the_other_side_port_info_map != null) {
                    cubicle_desc = the_other_side_port_info_map.get("cubicle_desc");
                    unit_name_desc = the_other_side_port_info_map.get("unit_name_desc");
                    board_type = the_other_side_port_info_map.get("board_type");
                }
                plugSwitchSvgBean.setOppositeUnitDesc(unit_name_desc);//张炳旺要求不显示屏柜，而是显示装置信息

                //TODO求信号类型
                board_type = DataProcessTools.getSignalTypeWithPriCfg(mCtx,cp_ports.get(0).getId());
                plugSwitchSvgBean.setBoard_type(board_type);
            }

            plugIedSvgBeans.add(plugSwitchSvgBean);
            index_plugin_temp++;
        }
        return plugIedSvgBeans;
    }

    private static Map<String,List<KM_SPCD_PORT>> getPortPortsByNo(KM_SPCD_BOARD board, List<KM_SPCD_PORT> ports) {
        Map<String,List<KM_SPCD_PORT>> map = new LinkedHashMap<>();
        //求出所有的no
        List<String> noList = new ArrayList<>();
        for (KM_SPCD_PORT port:ports){
            String no = port.getNo();
            if (!noList.contains(port.getNo())){
                noList.add(no);
            }
        }
        //通过no找出成对的port
        for (String e:noList){
            List<KM_SPCD_PORT> cp_ports = new ArrayList<>();
            for (KM_SPCD_PORT port:ports){
                if (port.getNo().equals(e)){
                    cp_ports.add(port);
                }
            }
            if (cp_ports.size() > 2){
                Log.e(TAG, "onLayout: ", new RuntimeException(
                        "SPCD文件错误！\n"+
                                "板卡："+board.getDescription()+"下no="+e+"的port的个数="+cp_ports.size()+",应该<=2"));
                cp_ports = cp_ports.subList(0,1);
            }
            map.put(e,cp_ports);
        }
        return map;
    }

    private float cubicle_width = 0f;
    private float cubicle_height = 0f;
    //横向图形显示完整
    public void RequestLayout_FillWidth(){
        scrollTo(0,0);
        mScale = mWidth/(cubicle_width+CubicleBackPanelConstants.CUBICULE_LEFT_PADDING*2);
        mPreScale = mScale;
        this.totalOffX = 0;
        this.totalOffY = 0;
        invalidate();
    }
    //所有图形显示全局
    public void RequestLayout_FillWidthHeight(){
        scrollTo(0,0);
        float mScale_w = mWidth/(cubicle_width+CubicleBackPanelConstants.CUBICULE_LEFT_PADDING*2);
        float mScale_h = mHeight/(cubicle_height+CubicleBackPanelConstants.CUBICULE_TOP_PADDING*2);
        mScale = mScale_w<mScale_h ? mScale_w : mScale_h;
        mPreScale = mScale;
        this.totalOffX = 0;
        this.totalOffY = 0;
        invalidate();
    }
    //缩放
    int maxscaleCount = 0;
    int minscaleCount = 0;
    public void SetScale(float scale){
        this.mScale *= scale;
        if (mScale > mMaxScale) {
            mScale = mMaxScale;
            maxscaleCount++;
        }else {
            maxscaleCount = 0;
        }
        if (mScale < mMinScale) {
            mScale = mMinScale;
            minscaleCount++;
        }else {
            minscaleCount = 0;
        }
        mPreScale = mScale;

        //当缩小到固定的最小倍率或者放大到最大倍率超过一次就不请求重绘
        if (maxscaleCount>1 || minscaleCount>1) {
            return;
        }else {
            invalidate();
        }

    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        long t0 = System.currentTimeMillis();
        canvas.scale(mScale, mScale);

        if (cacheData == null) return;
        if (cacheData.getUnit() == null){
            DrawUtils.textCenter("装置信息",txtPaint,canvas,new RectF(0,0,mWidth,mHeight),
                    Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);
            invalidate();
            requestLayout();
            return;
        }

        txtPaint.setColor(0xFF000000);
        mPaint.setColor(0xFF000000);
        //绘制屏柜内部-IED设备

        if (unitBean instanceof IedBean){
            canvas.drawRect(unitBean.getRectF(),mPaint);
            canvas.drawRect(unitBean.getNameRectF(),mPaint);
            txtPaint.setTextSize(CubicleBackPanelConstants.TEXT_SIZE_UNIT);
            txtPaint.setFakeBoldText(true);
            DrawUtils.textCenter(unitBean.getName(),txtPaint,canvas, unitBean.getNameRectF(),
                    Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);

            canvas.drawRect(unitBean.getIdentRectF(),mPaint);
            DrawUtils.textCenter(unitBean.getIdentifier(),txtPaint,canvas, unitBean.getIdentRectF(),
                    Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);

            //绘制IED中的板卡
            for (int j = 0; j < unitBean.getBoards().size(); j++) {
                txtPaint.setTextSize(CubicleBackPanelConstants.TEXT_SIZE_BOARD);
                txtPaint.setFakeBoldText(true);
                txtPaint.setColor(0xFF000000);
                mPaint.setColor(0xFF000000);
                BoardBean boardBean = unitBean.getBoards().get(j);
                canvas.drawRect(boardBean.getRectF(),mPaint);
                canvas.drawRect(boardBean.getNameRectF(),mPaint);
                DrawUtils.textCenter(boardBean.getName(),txtPaint,canvas,boardBean.getNameRectF(),
                        Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);
                canvas.drawRect(boardBean.getDescRectF(),mPaint);
                DrawUtils.textCenter(boardBean.getDesc(),txtPaint,canvas,boardBean.getDescRectF(),
                        Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);

                //绘制板卡中的插板
                txtPaint.setTextSize(CubicleBackPanelConstants.TEXT_SIZE_DEFAULT);
                txtPaint.setFakeBoldText(false);
                for (int k = 0; k < boardBean.getPlugSvgBeanList().size(); k++) {
                    PlugIedSvgBean plugIedSvgBean = (PlugIedSvgBean)boardBean.getPlugSvgBeanList().get(k);
                    drawHighlightPort(plugIedSvgBean);
                    plugIedSvgBean.draw(canvas,mScale,mPaint,txtPaint);

                    if (SHOW_DRAWING_HELPER){
                        mPaint.setStrokeWidth(1);
                        mPaint.setPathEffect(new DashPathEffect(new float[] { 3, 1 }, 0));
                        canvas.drawRect(plugIedSvgBean.getRectF(),mPaint);
                        mPaint.setStrokeWidth(3);
                        mPaint.setPathEffect(null);
                    }
                }
            }
        }
        if (unitBean instanceof SwitchBean){
            SwitchBean switchBean = (SwitchBean) unitBean;
            canvas.drawRect(switchBean.getFrameRect(),mPaint);
            //绘制中间的文字
            canvas.drawRect(switchBean.getMiddleRectF(),mPaint);
            DrawUtils.textCenterDefault(switchBean.getName(),txtPaint,canvas,switchBean.getNameRectF());
            DrawUtils.textCenterDefault(switchBean.getIdentifier(),txtPaint,canvas,switchBean.getIdentRectF());
            //绘制Switch中的上下两个东西
            if (SHOW_DRAWING_HELPER){
                mPaint.setStrokeWidth(1);
                mPaint.setPathEffect(new DashPathEffect(new float[] { 3, 1 }, 0));
                mPaint.setColor(0x7fff0000);
                canvas.drawRect(switchBean.getRectF(),mPaint);//
                canvas.drawRect(switchBean.getUpsideRect(),mPaint);
                canvas.drawRect(switchBean.getDownsideRect(),mPaint);
                mPaint.setStrokeWidth(3);
                mPaint.setPathEffect(null);
                mPaint.setColor(0xff000000);
            }
            for (PlugSwitchSvgBean upPlugin : switchBean.getUpPlugSwitchSvgBeans()){
                if (SHOW_DRAWING_HELPER){
                    mPaint.setStrokeWidth(1);
                    mPaint.setPathEffect(new DashPathEffect(new float[] { 3, 1 }, 0));
                    canvas.drawRect(upPlugin.getRectF(),mPaint);
                    mPaint.setStrokeWidth(3);
                    mPaint.setPathEffect(null);
                }
                drawHighlightPort(upPlugin);
                upPlugin.draw(canvas,mScale,mPaint,txtPaint);
            }
            for (PlugSwitchSvgBean downPlugin : switchBean.getDownPlugSwitchSvgBeans()){
                if (SHOW_DRAWING_HELPER){
                    mPaint.setStrokeWidth(1);
                    mPaint.setPathEffect(new DashPathEffect(new float[] { 3, 1 }, 0));
                    canvas.drawRect(downPlugin.getRectF(),mPaint);
                    mPaint.setStrokeWidth(3);
                    mPaint.setPathEffect(null);
                }
                drawHighlightPort(downPlugin);
                downPlugin.draw(canvas,mScale,mPaint,txtPaint);
            }
        }
        //IEDVisualActivityNew.closeProgressDialog();
        long t1 = System.currentTimeMillis();
        Log.d(TAG, String.format("It takes time : <%s><耗时%dms>","装置的背板图view onDraw",t1-t0));
    }

    private void drawHighlightPort(PlugBaseSvgBean plugBaseSvgBean) {
        if (portId != -1){
            if (plugBaseSvgBean.containsPort(portId)){
                mPaint.setColor(0xFFFF0000);
                txtPaint.setColor(0xFFFF0000);
            } else {
                mPaint.setColor(0xFF000000);
                txtPaint.setColor(0xFF000000);
            }
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        mWidth = w;
        mHeight = h;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                eventX = (int) event.getX();
                eventY = (int) event.getY();
                break;
        }
        //short_longClickOnPlugSvg(event);
        DargAndZoom(event);

        //处理单击和双击的事件：
        SingleAndDoubleClick(event);

        invalidate();
        return true;
        //return super.onTouchEvent(event);
    }


    // 统计300ms内的点击次数
    TouchEventCountThread touchEventCountThread = new TouchEventCountThread();
    // 根据TouchEventCountThread统计到的点击次数, perform单击还是双击事件
    TouchEventHandler mTouchEventHandler = new TouchEventHandler();
    PointF clickDownPoint;
    private void SingleAndDoubleClick(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                clickDownPoint = new PointF(event.getX(),event.getY());
                touchEventCountThread.isCancel = false;
                Log.e(TAG, "touchEventCountThread.touchCount = "+touchEventCountThread.touchCount);
                if (touchEventCountThread.touchCount>2) touchEventCountThread.touchCount = -1;
                if (0 == touchEventCountThread.touchCount) // 第一次按下时,开始统计
                    postDelayed(touchEventCountThread, Constants.CONSTANT_DOUBLE_CLICK_INTERVAL_MS);
                break;
            case MotionEvent.ACTION_UP:
                // 一次点击事件要有按下和抬起, 有抬起必有按下, 所以只需要在ACTION_UP中处理
                if(!touchEventCountThread.isLongClick) {
                    touchEventCountThread.touchCount++;
                }
                // 如果是长按操作, 则Handler的消息,不能将touchCount置0, 需要特殊处理
                if(touchEventCountThread.isLongClick) {
                    touchEventCountThread.touchCount = 0;
                    touchEventCountThread.isLongClick = false;
                    touchEventCountThread.isCancel = true;
                }
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        touchEventCountThread.touchCount = 0;
                    }
                },Constants.CONSTANT_DOUBLE_CLICK_INTERVAL_MS);
                break;
            case MotionEvent.ACTION_MOVE:
                boolean isMoved = Math.sqrt(Math.pow((clickDownPoint.x-event.getX()),2)+Math.pow((clickDownPoint.y-event.getY()),2))>10;
                if(isMoved) {
                    touchEventCountThread.isCancel = true;
                    touchEventCountThread.isLongClick = false;
                }
                break;
            case MotionEvent.ACTION_CANCEL:
                break;
            default:
                break;
        }
    }
    public class TouchEventCountThread implements Runnable {
        public int touchCount = 0;
        public boolean isLongClick = false;
        public boolean isCancel = false;

        @Override
        public void run() {
            Message msg = new Message();
            if(0 == touchCount){ // long click
                isLongClick = true;
                msg.arg1 = touchCount;
            } else {
                isLongClick = false;
                msg.arg1 = touchCount;
                //touchCount = 0;
            }
            if (!isCancel){
                Log.e(TAG, "run: 未取消 msg="+msg.arg1 );
                mTouchEventHandler.sendMessage(msg);
                touchCount = 0;
                isLongClick = false;
            } else {
                Log.e(TAG, "run: 已取消 msg="+msg.arg1 );
                touchCount = 0;
                isLongClick = false;
                isCancel = true;
            }
        }
    }

    /**
     * touch-time = 0 表示长按；
     * toutch-time >0表示在指定时间（500ms）内所点击的次数。
     */
    public class TouchEventHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            //Toast.makeText(mCtx, "touch " + msg.arg1 + " time.", Toast.LENGTH_SHORT).show();
            if (unitBean instanceof IedBean){
                for (BoardBean boardBean : unitBean.getBoards()) {
                    for (PlugBaseSvgBean plugBaseSvg : boardBean.getPlugSvgBeanList()) {
                        boolean isOnTouch = plugBaseSvg.isOnTouch(eventX, eventY, totalOffX, totalOffY, mScale);
                        if (isOnTouch){
                            if (SHOW_DRAWING_HELPER){
                                Utils.showShortToast(mCtx, " be clicked:" +String.format("%s,%s,%s",
                                        unitBean.getName(),boardBean.getName(),plugBaseSvg.getPort_no()));
                            }

                            if (NA.equals(plugBaseSvg.getOppositeUnitDesc())) {
                                Toast.makeText(mCtx, "备用端口无连接!", Toast.LENGTH_SHORT).show();
                                return;
                            }

                            somClicksListener(msg, plugBaseSvg);
                            touchEventCountThread.touchCount = 0;
                            return;
                        }
                    }
                }
            }
            if (unitBean instanceof SwitchBean){
                SwitchBean switchBean = (SwitchBean) unitBean;
                List<PlugSwitchSvgBean> plugSwitchSvgBeans = new ArrayList<>();
                plugSwitchSvgBeans.addAll(switchBean.getUpPlugSwitchSvgBeans());
                plugSwitchSvgBeans.addAll(switchBean.getDownPlugSwitchSvgBeans());

                for (PlugBaseSvgBean plugBaseSvg : plugSwitchSvgBeans) {
                    boolean isOnTouch = plugBaseSvg.isOnTouch(eventX, eventY, totalOffX, totalOffY, mScale);
                    if (isOnTouch){
                        if (SHOW_DRAWING_HELPER){
                            Utils.showShortToast(mCtx, " be clicked:" +String.format("%s,%s",
                                    unitBean.getName(),plugBaseSvg.getPort_no()));
                        }

                        if (NA.equals(plugBaseSvg.getOppositeUnitDesc())) {
                            Toast.makeText(mCtx, "备用端口无连接!", Toast.LENGTH_SHORT).show();
                            return;
                        }

                        somClicksListener(msg, plugBaseSvg);
                        touchEventCountThread.touchCount = 0;
                        return;
                    }
                }
            }
        }

        private void somClicksListener(Message msg, PlugBaseSvgBean plugBaseSvg) {
            long t0 = System.currentTimeMillis();
            switch (msg.arg1) {
                case 1://单击
                    //单击端口高亮：
                    setHightLightPort(plugBaseSvg.getPortIds()[0]);
                    postInvalidate();
                    //发送显示光纤标签的广播
                    sendBroadcast(plugBaseSvg.getPortIds(), ACTION_PORT_ID_CHANGE_ONSIDE, 0, 0);
                    break;
                case 0://长按
                case 2://双击
                    sendBroadcast(plugBaseSvg.getPortIds(), ACTION_PORT_ID_CHANGE_OFFSIDE, (int) eventX, (int) eventY);
                    break;
                default:
                    Toast.makeText(mCtx, "请不要快速频繁点击！", Toast.LENGTH_SHORT).show();
                    break;
            }
            long t1 = System.currentTimeMillis();
            Log.d(TAG, String.format("It takes time : <%s><耗时%dms>","装置的背板图view 点击端口响应(单/双击端口后400ms开始统计)",t1-t0));
        }
    }

    //Dragging
    private static final int NONE = 0;
    private static final int DRAG = 1;
    private static final int ZOOM = 2;
    private int mode = NONE;

    private PointF startPoint = new PointF();
    private PointF middlePoint = new PointF();

    private float oriDis = 1f;//初始的俩指触点间距
    private float mScale = 1.0f;
    private float mPreScale = 1.0f;
    private float mMinScale = 0.2f;
    private float mMaxScale = 3.0f;

    private void DargAndZoom(MotionEvent event){
        //middlePoint = null;
        switch (event.getAction()&event.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
                startPoint.set(event.getX(), event.getY());
                mode = DRAG;
                break;
            case MotionEvent.ACTION_POINTER_DOWN:
                oriDis = DrawUtils.distance(event);
                if (oriDis > 10f) {
                    middlePoint = DrawUtils.mid(event,null);
                    mode = ZOOM;
                }

                break;
            case MotionEvent.ACTION_POINTER_UP:
                mPreScale = mScale;
                //if (event.getPointerCount()<2) mode = DRAG;
                break;

            case MotionEvent.ACTION_MOVE:

                if (mode == DRAG) {
                    onDrag(event);
                } else if((mode == ZOOM)){
                    onZoom(event);
                }
                if (mode == DRAG) {
                    startPoint.set(event.getX(), event.getY());
                }
                getParent().requestDisallowInterceptTouchEvent(true);
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                mode = NONE;
                break;
            default:
                break;
        }
    }

    private float totalOffX = 0;
    private float totalOffY = 0;
    float offX = 0;
    float offY = 0;
    private void onDrag(MotionEvent event) {
        //边界处理：(以中心IED的中心不超过View边界为参考)
        offX = event.getX() - startPoint.x;
        offY = event.getY() - startPoint.y;

        scrollBy(-(int)offX, -(int)offY);
        totalOffX += offX;
        totalOffY += offY;
        //mMatrix.setTranslate(totalOffX,totalOffY);
    }

    /**
     * 把View上的点的坐标转化为Canvas上的坐标点 for拾取
     * @return
     */
    //float newDist = 0;
    private void onZoom(MotionEvent event) {
        float newDist = DrawUtils.distance(event);
        if (newDist > 10f) {
            float scale = newDist/oriDis;

            mScale = scale * mPreScale;
            if (mScale > mMaxScale) {
                mScale = mMaxScale;
            }
            if (mScale < mMinScale) {
                mScale = mMinScale;
            }
        }
    }

    public void sendBroadcast(int[] portIds, String ACTION_PORT_ID_CHANGE_TYPE,int x,int y){
        Intent intent = new Intent();
        intent.setAction(ACTION_PORT_ID_CHANGE_TYPE);
        intent.putExtra("portIds", portIds);
        intent.putExtra("x", x);
        intent.putExtra("y", y);
        mCtx.sendBroadcast(intent);
    }

    private GoosePortBean getPortGoose(int portId) {
        BaseDaoImp unitDao = null;
        BaseDaoImp boardDao = null;
        BaseDaoImp portDao = null;

        GoosePortBean goosePortBean = new GoosePortBean();
        List<GoosePortBean.GooseBean> gooseBeanList = new ArrayList<>();
        goosePortBean.setGooseBeans(gooseBeanList);
        try {
            XshlBean.ConnectBean connectBeanF = null;
            String iedName = null;
            String dbName = PubApp.getDbName();
            unitDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_UNIT.class, dbName);
            boardDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_BOARD.class, dbName);
            portDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_PORT.class, dbName);

            KM_SPCD_PORT km_spcd_port = (KM_SPCD_PORT) portDao.getFirstForEq("id", portId);
            KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) boardDao.getFirstForEq("id", km_spcd_port.getBoard_id());
            KM_SPCD_UNIT km_spcd_unit = (KM_SPCD_UNIT) unitDao.getFirstForEq("id", km_spcd_board.getUnit_id());
            String portA = km_spcd_board.getSlot()+"-"+km_spcd_port.getNo()+"-"+km_spcd_port.getDirection();
            XshlBean xshlBean = XshlUtils.getXshlData((Activity) mCtx, dbName, km_spcd_unit.getId(), 1);
            List<XshlBean.RightUnitConnect> list = xshlBean.getList();
            if (list != null && list.size()!=0){
                for (XshlBean.RightUnitConnect rightUnitConnect:list){
                    List<XshlBean.ConnectBean> connectBeans = rightUnitConnect.getConnectBean();
                    if (connectBeans!=null && connectBeans.size()!=0){
                        for (XshlBean.ConnectBean connectBean:connectBeans){
                            if (portA.equals(connectBean.getBoardA()+"-"+connectBean.getPortA())){
                                connectBeanF = connectBean;
                                iedName = rightUnitConnect.getIedName();
                                break;
                            }
                        }
                    }
                }
            }
            if (connectBeanF != null){
                DataExchangeCachePool.getInstance().setKIED(PubUtils.getPubUtils().GetKIEDModel(xshlBean.getLeftUnit().getIedName()));//同时通知更新标题！
                Integer direction = connectBeanF.getDirection();//方向
                List<XshlBean.GsooseBean> gsooseBeans = connectBeanF.getGsooseBeans();//goose控制块
                if (gsooseBeans!=null && gsooseBeans.size()!=0){
                    for (XshlBean.GsooseBean gsoose:gsooseBeans){
                        GoosePortBean.GooseBean gooseBean = new GoosePortBean.GooseBean();
                        List<GoosePortBean.PortsBean> list1 = new ArrayList<>();
                        gooseBean.setDirection(direction);
                        gooseBean.setGoose(gsoose.getGoose());
                        //根据goose控制块去找虚端子连接
                        List<VirFCDABean> mFcdaList = null;
                        if (direction == 1){
                            if (gsoose.getGoose().startsWith("GO")){
                                mFcdaList = DataExchangeCachePool.getInstance().getFcdaList(iedName, gsoose.getAppId(), KIEDModel.GSE_SEND);
                            }else if (gsoose.getGoose().startsWith("SV")){
                                mFcdaList = DataExchangeCachePool.getInstance().getFcdaList(iedName, gsoose.getAppId(), KIEDModel.SMV_SEND);
                            }
                        }else if (direction == 2){
                            if (gsoose.getGoose().startsWith("GO")){
                                mFcdaList = DataExchangeCachePool.getInstance().getFcdaList(iedName, gsoose.getAppId(), KIEDModel.GSE_RECV);
                            }else if (gsoose.getGoose().startsWith("SV")){
                                mFcdaList = DataExchangeCachePool.getInstance().getFcdaList(iedName, gsoose.getAppId(), KIEDModel.SMV_RECV);
                            }
                        }
                        if (mFcdaList !=null){
                            for (VirFCDABean virFCDABean:mFcdaList){
                                List<VirExtRefBean> extRefList = virFCDABean.getExtRefList();
                                if (extRefList!=null && extRefList.size()!=0){
                                    for (VirExtRefBean virExtRefBean:extRefList){
                                        GoosePortBean.PortsBean portsBean = new GoosePortBean.PortsBean();
                                        portsBean.setPortA(virFCDABean.getFcdaName());
                                        portsBean.setPortB(virExtRefBean.getName());
                                        list1.add(portsBean);
                                    }
                                }
                            }
                        }
                        gooseBean.setPorts(list1);
                        gooseBeanList.add(gooseBean);
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return null;
        } catch (Exception e){
            e.printStackTrace();
            return null;
        }
        return goosePortBean;
    }

}