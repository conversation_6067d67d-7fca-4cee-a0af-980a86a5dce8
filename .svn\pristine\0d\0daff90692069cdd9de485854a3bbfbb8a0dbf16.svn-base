package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PointF;
import android.text.TextPaint;

import java.util.List;

public class FoldLine {
    public Paint mPaint = null;
    public Paint txtPaint = null;
    public enum LineType{
        IED_TO_ODF,//屏柜内光纤
        ODF1_TO_ODF2,//屏柜外光缆
        IED1_TO_IED2_IN,//屏柜内光纤
        ODF_SELF,//屏柜内部自连（屏柜个数>2时，才会存在）
        IED1_TO_IED2_OUT,//屏柜外光纤
    };

    LineType lineType;

    PointF portA;
    PointF portB;
    String desc = "";

    PointF centrePoint = null;

    List<PointF> brokenPoints;

    public FoldLine() {
        mPaint = new Paint();
        mPaint.setStrokeWidth(2);

        txtPaint = new TextPaint();
        txtPaint.setStrokeWidth(3);
        txtPaint.setTextAlign(Paint.Align.CENTER);
        txtPaint.setAntiAlias(true);
    }

    public LineType getLineType() {
        return lineType;
    }

    public void setLineType(LineType lineType) {
        this.lineType = lineType;
    }

    public void setPorts(PointF portA, PointF portB){
        this.portA = portA;
        this.portB = portB;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void draw(Canvas canvas){
        switch (lineType){
            case IED_TO_ODF:
                //取中间中点处折断，产生两个水平断点
                PointF tmA1,tmA2;
                tmA1= new PointF(portA.x,(portA.y+portB.y)/2);
                tmA2= new PointF(portB.x,(portA.y+portB.y)/2);
                drawLines(canvas,portA,tmA1,tmA2,portB);
                break;
            case ODF1_TO_ODF2:
                PointF tmB1,tmB2;
                tmB1= new PointF(portA.x,portA.y+CircuitConstants.ODF_LINE_UP_DOWN);
                tmB2= new PointF(portB.x,portB.y+CircuitConstants.ODF_LINE_UP_DOWN);
                drawLines(canvas,portA,tmB1,tmB2,portB);
                canvas.drawText(desc,(portA.x+portB.x)/2,tmB1.y+CircuitConstants.TEXT_DONW_GL_WL,txtPaint);
                break;
            case ODF_SELF:
                PointF tmC1,tmC2;
                tmC1= new PointF(portA.x,portA.y-CircuitConstants.ODF_LINE_UP_DOWN);
                tmC2= new PointF(portB.x,portB.y-CircuitConstants.ODF_LINE_UP_DOWN);
                drawLines(canvas,portA,tmC1,tmC2,portB);
                canvas.drawText(desc,(portA.x+portB.x)/2,tmC1.y+CircuitConstants.TEXT_DONW_GL_WL,txtPaint);
                break;
            case IED1_TO_IED2_IN:
                drawLines(canvas,portA,portB);
                break;
            case IED1_TO_IED2_OUT:
                PointF tmD1,tmD2;
                tmD1= new PointF(portA.x,portA.y+CircuitConstants.IED_LINE_DOWN);
                tmD2= new PointF(portB.x,portB.y+CircuitConstants.IED_LINE_DOWN);
                drawLines(canvas,portA,tmD1,tmD2,portB);
                canvas.drawText(desc,(portA.x+portB.x)/2,tmD1.y+CircuitConstants.TEXT_DONW_GL_WL,txtPaint);
                break;
            default:break;
        }

        //canvas.drawLines();
    }

    private void drawLines(Canvas canvas, PointF... pointFs){
        if (pointFs.length<2) return;

        float [] floats = new float[(pointFs.length-1)*4];
        for (int i = 0; i < pointFs.length-1; i++) {
            PointF p1 = pointFs[i];
            PointF p2 = pointFs[i+1];
            floats[i*4] = p1.x;
            floats[i*4+1] = p1.y;
            floats[i*4+2] = p2.x;
            floats[i*4+3] = p2.y;
        }
        canvas.drawLines(floats,mPaint);
    }

}
