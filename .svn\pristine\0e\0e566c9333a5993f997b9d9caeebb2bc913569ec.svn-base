package com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.check;

import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.Parser.model.SPCDItemBase;

import java.util.ArrayList;
import java.util.List;

public class CheckResult {
    private CheckResultLevel checkLevel;
    private String message;
    private List<SPCDItemBase> objects;//相关的对象
    public CheckResult(CheckResultLevel level){
        checkLevel=level;
    }
    public CheckResult(CheckResultLevel level,String message){
        this.checkLevel=level;
        this.message=message;
    }
    public CheckResult(CheckResultLevel level,String message,SPCDItemBase object){
        this.checkLevel=level;
        this.message=message;
        addObject(object);
    }
    public List<SPCDItemBase> getObjects() {
        return objects==null?new ArrayList<>():objects;
    }

    public void addObject(SPCDItemBase object) {
        if(objects==null)
            objects=new ArrayList<>();
        this.objects.add(object);
    }
    public CheckResultLevel getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(CheckResultLevel checkLevel) {
        this.checkLevel = checkLevel;
    }
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        StringBuilder builder=new StringBuilder();
        builder.append("[").append(checkLevel).append("]:");
        if(objects!=null) {
            builder.append("[");
            for( SPCDItemBase itm:objects){
                builder.append("{line:").append(itm.getLineBegin()).append(" ");
                builder.append(itm.toString()).append("} ");
            }
            builder.append("]");
        }
        builder.append(message);
        return builder.toString();
    }
}
