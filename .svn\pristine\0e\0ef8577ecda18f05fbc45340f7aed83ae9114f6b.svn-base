package com.kemov.visual.spcd.spcdvisualandroidapp.activity;

import android.content.Intent;
import android.os.Bundle;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageButton;
import android.widget.TextView;

import com.kemov.visual.spcd.spcdvisualandroidapp.CommonAdapterHelper.GlwlgqAdapter;
import com.kemov.visual.spcd.spcdvisualandroidapp.R;
import com.kemov.visual.spcd.spcdvisualandroidapp.activity.optical_fiber_visual.OpticalFiberVisualActivity;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.WholeCircuitBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_REGION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_SUBSTATION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDao;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.PrefReader;

import java.io.Serializable;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Arrays.asList;

public class GLWLGQActivity extends BaseActivity implements View.OnClickListener {

    BaseDao cableDao = null;
    BaseDao coreDao = null;
    BaseDao substationDao = null;
    BaseDao cubicleDao = null;
    BaseDao regionDao = null;
    BaseDao unitDao = null;
    BaseDaoImp boardDao;
    BaseDaoImp portDao;
    BaseDaoImp intcoreDao;

    private String dbName;
    private int cableId;
    private ImageButton left_back;
    private TextView title;
    private RecyclerView recyclerView;
    List<KM_SPCD_CORE> cores;
    List<KM_SPCD_CORE> coress;
    private String glName;
    KM_SPCD_CABLE km_spcd_cable;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_gq);
        initView();
    }

    @Override
    public void initView() {
        findViewWithId();
        getDataFromIntent();
        initDao();
        getData();
    }

    private void initDao() {
        unitDao = new BaseDaoImp(getApplicationContext(), KM_SPCD_UNIT.class, dbName);
        boardDao = new BaseDaoImp(getApplicationContext(), KM_SPCD_BOARD.class, dbName);
        portDao = new BaseDaoImp(getApplicationContext(), KM_SPCD_PORT.class, dbName);
        cableDao = new BaseDaoImp(getApplicationContext(), KM_SPCD_CABLE.class, dbName);
        coreDao = new BaseDaoImp(getApplicationContext(), KM_SPCD_CORE.class, dbName);
        cubicleDao = new BaseDaoImp(getApplicationContext(), KM_SPCD_CUBICLE.class, dbName);
        regionDao = new BaseDaoImp(getApplicationContext(), KM_SPCD_REGION.class, dbName);
        substationDao = new BaseDaoImp(getApplicationContext(), KM_SPCD_SUBSTATION.class, dbName);
        intcoreDao = new BaseDaoImp(getApplicationContext(), KM_SPCD_INTCORE.class, dbName);
    }

    private void getData() {
        if (cableId == 0){
            return;
        }
        cores = (List<KM_SPCD_CORE>)coreDao.getListForEq("cable_id", cableId);
        coress = new ArrayList<>();
        if (cores!=null && cores.size()!=0){
            for (KM_SPCD_CORE km_spcd_core:cores){
                if (!TextUtils.isEmpty(km_spcd_core.getPort_a()) || !TextUtils.isEmpty(km_spcd_core.getPort_b())){
                    coress.add(km_spcd_core);
                }
            }
        }
        setRecyclerView();
    }

    private void setRecyclerView() {
        //设置LayoutManager为LinearLayoutManager
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        //设置Adapter
        GlwlgqAdapter gqAdapter = new GlwlgqAdapter(this, coress);
        recyclerView.setAdapter(gqAdapter);
//        gqAdapter.setOnItemClick(new GlwlgqAdapter.OnItemClick() {
//            @Override
//            public void OnItem(KM_SPCD_CORE km_spcd_core) {
//                if (TextUtils.isEmpty(km_spcd_core.getPort_a()) || TextUtils.isEmpty(km_spcd_core.getPort_b())){
//                    showToast("未找到数据");
//                    return;
//                }
//
//                KM_SPCD_PORT port = null;
//                KM_SPCD_UNIT km_spcd_unit = null;
//                Integer cable_id = km_spcd_core.getCable_id();
//                KM_SPCD_CABLE km_spcd_cable = (KM_SPCD_CABLE)cableDao.getById(cable_id);
//                String cubicleA = km_spcd_cable.getCubicleA();
//                KM_SPCD_PORT portByPortA = getPortByPortA(cubicleA, km_spcd_core.getPort_a());
//                //获取odf连接的装置id
//                HandleForWholeCircuitsFix2 handleForWholeCircuitsFix2 = new HandleForWholeCircuitsFix2(GLWLGQActivity.this, dbName, portByPortA.getId(),3);
//                WholeCircuitBean data = handleForWholeCircuitsFix2.wholeCircuitBean1;
//                if (data!=null){
//                    List<WholeCircuitBean.CubicleWholeBean> cubicleWholeBeans = data.getCubicleWholeBeans();
//                    if (cubicleWholeBeans!=null && cubicleWholeBeans.size()!=0){
//                        WholeCircuitBean.CubicleWholeBean cubicleWholeBean1 = cubicleWholeBeans.get(cubicleWholeBeans.size()-1);
//                        List<WholeCircuitBean.UnitWholeBean> unitWholeBeans1 = cubicleWholeBean1.getUnitWholeBeans();
//
//                        if (unitWholeBeans1!=null && unitWholeBeans1.size()!=0){
//                            WholeCircuitBean.UnitWholeBean unitWholeBean = unitWholeBeans1.get(unitWholeBeans1.size() - 1);
//                            km_spcd_unit = unitWholeBean.getKm_spcd_unit();
//                            port = getPort(unitWholeBean);
//                        }
//                    }
//                }
//
//                if (km_spcd_unit.getDev_class().equals("ODF")){
//                    showToast("未找到数据");
//                }else {
//                    if (port != null){
//                        //跳转到全回路图
//                        Intent intent = new Intent();
//                        intent.setClass(GLWLGQActivity.this, OpticalFiberVisualActivity.class);
//                        intent.putExtra("portID", port.getId());
//                        intent.putExtra("type",4);//跳纤也传4
//                        startActivity(intent);
//                    }
//                }
//            }
//        });
//        gqAdapter.setOnArrowClick(new GlwlgqAdapter.OnArrowClick() {
//            @Override
//            public void OnArrow(KM_SPCD_CORE km_spcd_core) {
//                KM_SPCD_INTCORE intcore = getIntcore(km_spcd_core);
//                if (intcore == null){
//                    showToast("未找到数据");
//                    return;
//                }
//                //跳转到全回路图
//                Intent intent = new Intent();
//                intent.setClass(GLWLGQActivity.this, GlwlLabelActivity.class);
//                Map<String, Object> map = new HashMap<>();
//                map.put("intCore",intcore.getId());
//                map.put("desc",intcore.getPort_a()+"——"+intcore.getPort_b());
//                map.put("name",intcore.getName());
//                intent.putExtra("datas", (Serializable) map);
//                intent.putExtra("type",2);
//                startActivity(intent);
//            }
//        });
        gqAdapter.setOnItemClick(new GlwlgqAdapter.OnItemClick() {
            @Override
            public void OnItem(KM_SPCD_CORE km_spcd_core) {
                //通过core去找连接的intcore
                KM_SPCD_INTCORE intcore = getIntcore(km_spcd_core);
                if (intcore == null){
                    showToast("未找到数据");
                    return;
                }
                //跳转到全回路图
                Intent intent = new Intent();
                intent.setClass(GLWLGQActivity.this, OpticalFiberVisualActivity.class);
                intent.putExtra("intCoreID", intcore.getId());
                intent.putExtra("type",4);//跳纤也传4
                startActivity(intent);
            }
        });
        gqAdapter.setOnArrowClick(new GlwlgqAdapter.OnArrowClick() {
            @Override
            public void OnArrow(KM_SPCD_CORE km_spcd_core) {
                KM_SPCD_INTCORE intcore = getIntcore(km_spcd_core);
                if (intcore == null){
                    showToast("未找到数据");
                    return;
                }
                //跳转到全回路图
                Intent intent = new Intent();
                intent.setClass(GLWLGQActivity.this, GlwlLabelActivity.class);
                Map<String, Object> map = new HashMap<>();
                map.put("intCore",intcore.getId());
                map.put("desc",intcore.getPort_a()+"——"+intcore.getPort_b());
                map.put("name",intcore.getName());
                intent.putExtra("datas", (Serializable) map);
                intent.putExtra("type",2);
                startActivity(intent);
            }
        });
    }

    private KM_SPCD_INTCORE getIntcore(KM_SPCD_CORE km_spcd_core) {
        Integer cable_id = km_spcd_core.getCable_id();
        if(km_spcd_core.getPort_a().isEmpty()||km_spcd_core.getPort_b().isEmpty())
            return null;
        try {
            km_spcd_cable = (KM_SPCD_CABLE) cableDao.getFirstForEq("id", cable_id);
            if (km_spcd_cable == null || TextUtils.isEmpty(km_spcd_cable.getCubicleA())){
                showToast("未找到屏柜");
                return null;
            }
            KM_SPCD_INTCORE intcore1=getIntCore(km_spcd_cable.getCubicleA(),km_spcd_core.getPort_a());
            if(intcore1!=null)
                return intcore1;
            else {
                KM_SPCD_INTCORE intcore2 = getIntCore(km_spcd_cable.getCubicleB(), km_spcd_core.getPort_b());
                if (intcore2 != null)
                    return intcore2;
                else {
                    //通过odf光缆直连的找法
                    //                    getCoreFromGlwl(km_spcd_region,km_spcd_cubicle,km_spcd_core);
                    KM_SPCD_REGION km_spcd_region = null;
                    KM_SPCD_CUBICLE km_spcd_cubicle = null;
                    if (!TextUtils.isEmpty(km_spcd_cable.getCubicleA()) && km_spcd_cable.getCubicleA().contains(".")) {
                        String[] split = km_spcd_cable.getCubicleA().split("\\.");
                        km_spcd_region = (KM_SPCD_REGION) regionDao.getFirstForEq("substation_id", 1, "name", split[0]);
                        if(km_spcd_region==null)
                            return null;
                        km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getFirstForEq
                                ("region_id", km_spcd_region.getId(), "substation_id", 1, "name", split[1]);
                        if (km_spcd_cubicle == null) {
                            return null;
                        }
                    }
                    KM_SPCD_INTCORE coreFromGlwlNew = getCoreFromGlwlNew(km_spcd_region, km_spcd_cubicle, km_spcd_core, km_spcd_cable);
                    return coreFromGlwlNew;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }

    private KM_SPCD_INTCORE getIntCore(String RegionCublicName,String portName){
        KM_SPCD_REGION km_spcd_region = null;
        KM_SPCD_CUBICLE km_spcd_cubicle = null;
        try {
            if (!TextUtils.isEmpty(RegionCublicName) && RegionCublicName.contains(".")) {
                String[] split = RegionCublicName.split("\\.");
                km_spcd_region = (KM_SPCD_REGION) regionDao.getFirstForEq("substation_id", 1, "name", split[0]);
                if (km_spcd_region == null)
                    return null;
                km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getFirstForEq("region_id", km_spcd_region.getId(), "substation_id", 1, "name", split[1]);
                if (km_spcd_cubicle == null) {
                    return null;
                }
                List<KM_SPCD_INTCORE> km_spcd_intcores0 = new ArrayList<>();
                //查出port_a是odf的
                List<String> keys1 = new ArrayList<String>(asList("cubicle_id", "port_a"));
                List<Object> value1 = new ArrayList<Object>(asList(km_spcd_cubicle.getId(), portName));
                List<KM_SPCD_INTCORE> km_spcd_intcores1 = intcoreDao.getListForEq(keys1, value1);
                //查出port_b是odf的
                List<String> keys2 = new ArrayList<String>(asList("cubicle_id", "port_b"));
                List<KM_SPCD_INTCORE> km_spcd_intcores2 = intcoreDao.getListForEq(keys2, value1);
                if (km_spcd_intcores1 != null && km_spcd_intcores1.size() != 0) {
                    km_spcd_intcores0.addAll(km_spcd_intcores1);
                }
                if (km_spcd_intcores2 != null && km_spcd_intcores2.size() != 0) {
                    km_spcd_intcores0.addAll(km_spcd_intcores2);
                }
                if (km_spcd_intcores0 != null && km_spcd_intcores0.size() != 0) {
                    KM_SPCD_INTCORE km_spcd_intcore1 = km_spcd_intcores0.get(0);
                    return km_spcd_intcore1;
                }
            }
            return null;
        }catch (SQLException e){
            e.printStackTrace();;
            return null;
        }
    }

    private KM_SPCD_INTCORE getCoreFromGlwlNew(KM_SPCD_REGION km_spcd_region, KM_SPCD_CUBICLE km_spcd_cubicle, KM_SPCD_CORE km_spcd_core,KM_SPCD_CABLE km_spcd_cable) {
        String cableName = km_spcd_cable.getName();

        String cubicleA = km_spcd_cable.getCubicleA();
        String cubicleB = km_spcd_cable.getCubicleB();
        String port_a = km_spcd_core.getPort_a();
        String port_b = km_spcd_core.getPort_b();

        setWLconnect(km_spcd_region,km_spcd_cubicle,port_a,cableName);
        if (km_spcd_intcoreF!=null){
            return km_spcd_intcoreF;
        }else {
            setWLconnect(km_spcd_region,km_spcd_cubicle,port_b,cableName);
        }
        if (km_spcd_intcoreF!=null){
            return km_spcd_intcoreF;
        }
        return null;
    }

    private KM_SPCD_INTCORE km_spcd_intcoreF;

    private void setWLconnect(KM_SPCD_REGION km_spcd_region, KM_SPCD_CUBICLE km_spcd_cubicle, String port0, String beforeCable) {
        //拼装当前装置的尾缆cubicleA="R66.XLKZ1A"
        String cubicleC= km_spcd_region.getName()+"."+km_spcd_cubicle.getName();

        List<String> keys1 = new ArrayList<String>(asList("station_id", "cubicleA"));
        List<String> keys2 = new ArrayList<String>(asList("station_id", "cubicleB"));
        List<Object> value = new ArrayList<Object>(asList(1,cubicleC));
        try {
            List<KM_SPCD_CABLE> cables = new ArrayList<>();
            List<KM_SPCD_CABLE> cables1 = (List<KM_SPCD_CABLE>)cableDao.getListForEq(keys1, value);
            List<KM_SPCD_CABLE> cables2 = (List<KM_SPCD_CABLE>)cableDao.getListForEq(keys2, value);
            if (cables1!=null && cables1.size()!=0){
                cables.addAll(cables1);
            }
            if (cables2!=null && cables2.size()!=0){
                cables.addAll(cables2);
            }
            if (cables.size()!=0){
                int isGo = -1;
                for (KM_SPCD_CABLE km_spcd_cable:cables){
                    if (isGo == 1){
                        break;
                    }
                    String cubicleA = km_spcd_cable.getCubicleA();
                    String cubicleB = km_spcd_cable.getCubicleB();
                    String cableNameNow = km_spcd_cable.getName();
                    List<KM_SPCD_CORE> cores = coreDao.getListForEq("cable_id", km_spcd_cable.getId());

                    if (cubicleA.equals(cubicleC) && !cableNameNow.equals(beforeCable) && !TextUtils.isEmpty(cubicleB)){
                        if (cores!=null && cores.size()!=0){
                            //找对侧屏柜信息
                            KM_SPCD_CUBICLE cubicle2 = getcubicle(1, cubicleB);
                            KM_SPCD_REGION region = (KM_SPCD_REGION) regionDao.getFirstForEq("id", cubicle2.getRegion_id());
                            for (KM_SPCD_CORE km_spcd_core:cores){
                                String port_a = km_spcd_core.getPort_a();
                                String port_b = km_spcd_core.getPort_b();
                                if (port0.equals(port_a)){
                                    //尾缆找到对侧屏柜连接，停止循环
                                    if (cubicle2 == null){
                                        break;
                                    }
                                    //找对侧装置
                                    KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicleB, port_b);
                                    //该odf连接的装置
                                    List<KM_SPCD_INTCORE> intcores = intcoreDao.getListForEq("cubicle_id", cubicle2.getId());
                                    for (KM_SPCD_INTCORE km_spcd_intcore:intcores){
                                        String port_a1 = km_spcd_intcore.getPort_a();
                                        String port_b1 = km_spcd_intcore.getPort_b();
                                        if (port_a1.equals(port_b)){
                                            km_spcd_intcoreF = km_spcd_intcore;
                                            isGo = 1;//找到之后结束外面的循环标志
                                            break;
                                        }else if (port_b1.equals(port_b)){
                                            km_spcd_intcoreF = km_spcd_intcore;
                                            isGo = 1;//找到之后结束外面的循环标志
                                            break;
                                        }
                                    }
                                    //如果没找到接着去找光缆尾缆
                                    if (isGo!=1){
                                        setWLconnect(region,cubicle2,port_a,cableNameNow);
                                    }
                                    break;
                                }
                            }
                        }
                    }else if (cubicleB.equals(cubicleC) && !cableNameNow.equals(beforeCable) && !TextUtils.isEmpty(cubicleA)){
                        //找对侧屏柜信息
                        KM_SPCD_CUBICLE cubicle2 = getcubicle(1, cubicleA);
                        KM_SPCD_REGION region = (KM_SPCD_REGION) regionDao.getFirstForEq("id", cubicle2.getRegion_id());
                        if (cores!=null && cores.size()!=0){
                            for (KM_SPCD_CORE km_spcd_core:cores){
                                String port_a = km_spcd_core.getPort_a();
                                String port_b = km_spcd_core.getPort_b();
                                if (port0.equals(port_b)){
                                    //尾缆找到对侧屏柜连接，停止循环
                                    if (cubicle2 == null){
                                        break;
                                    }
                                    //找对侧装置
                                    KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicleA, port_a);
                                    //改造--如果连续是尾缆
                                    //该odf连接的装置
                                    List<KM_SPCD_INTCORE> intcores = intcoreDao.getListForEq("cubicle_id", cubicle2.getId());
                                    for (KM_SPCD_INTCORE km_spcd_intcore:intcores){
                                        String port_a1 = km_spcd_intcore.getPort_a();
                                        String port_b1 = km_spcd_intcore.getPort_b();
                                        if (port_a1.equals(port_a)){
                                            km_spcd_intcoreF = km_spcd_intcore;
                                            isGo = 1;//找到之后结束外面的循环标志
                                            break;
                                        }else if (port_b1.equals(port_a)){
                                            km_spcd_intcoreF = km_spcd_intcore;
                                            isGo = 1;//找到之后结束外面的循环标志
                                            break;
                                        }
                                    }
                                    //如果没找到接着去找光缆尾缆
                                    if (isGo!=1){
                                        setWLconnect(region,cubicle2,port_b,cableNameNow);
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    //通过core里面的portA="1n.1.A-Tx"，如1n去找到该
    private KM_SPCD_REGION getRegion(Integer stationId, String cubicleA) {
        if (!TextUtils.isEmpty(cubicleA)){
            String[] split = cubicleA.split("\\.");
            String region = split[0];//小室
            //查询小室id
            try {
                KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("substation_id", stationId,"name",region);
                if (km_spcd_region == null){
                    return null;
                }
                return km_spcd_region;
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    //通过core里面的portA="1n.1.A-Tx"，如1n去找到该装置
    private KM_SPCD_CUBICLE getcubicle(Integer stationId, String cubicleA) {
        if (!TextUtils.isEmpty(cubicleA)){
            String[] split = cubicleA.split("\\.");
            String region = split[0];//小室
            String cubicle = split[1];//屏柜
            //查询小室id
            try {
                KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("substation_id", stationId,"name",region);
                if (km_spcd_region == null){
                    return null;
                }
                KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getFirstForEq("region_id", km_spcd_region.getId(), "substation_id", stationId,"name",cubicle);
                if (km_spcd_cubicle!=null){
                    return km_spcd_cubicle;
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    //通过core里面的portA="1n.1.A-Tx"，如1n去找到该装置
    private KM_SPCD_UNIT getUnit(Integer stationId, String cubicleA, String port_a) {
        if (!TextUtils.isEmpty(cubicleA) && !TextUtils.isEmpty(port_a)){
            String[] split = cubicleA.split("\\.");
            String region = split[0];//小室
            String cubicle = split[1];//屏柜
            String[] split1 = port_a.split("\\.");
            String unitName = split1[0];//unitName
            //查询小室id
            try {
                KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("substation_id", stationId,"name",region);
                KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getFirstForEq("region_id", km_spcd_region.getId(), "substation_id", stationId,"name",cubicle);
                if (km_spcd_cubicle!=null){
                    KM_SPCD_UNIT km_spcd_unit = (KM_SPCD_UNIT) unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", unitName);
                    return km_spcd_unit;
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    private void getCoreFromGlwl(KM_SPCD_REGION km_spcd_region, KM_SPCD_CUBICLE km_spcd_cubicle, KM_SPCD_CORE km_spcd_core) {
        try {
            //根据odf去查找Cable连接
            String cubicleC = km_spcd_region.getName() +"." + km_spcd_cubicle.getName();//如：cubicleA="R66.XLKZ1A"
            //查找与portOdf相连的屏柜
//            List<String> keys = new ArrayList<String>(asList("station_id", "cubicleA"));
            List<Object> value = new ArrayList<Object>(asList(1,cubicleC));
//            List<KM_SPCD_CABLE> cablesA = (List<KM_SPCD_CABLE>) cableDao.getListForEq(keys, value);//cubicleA是当前屏柜

            List<String> keys0 = new ArrayList<String>(asList("station_id", "cubicleB"));
            List<KM_SPCD_CABLE> cablesB = (List<KM_SPCD_CABLE>) cableDao.getListForEq(keys0, value);//cubicleA是当前屏柜

            //改造3.0，如果光缆连接的是光缆
//            analysisCables(cablesA,km_spcd_core,1);
            analysisCables(cablesB,km_spcd_core,2);
        }catch (SQLException e){
            e.printStackTrace();
        }
    }

    private void analysisCables(List<KM_SPCD_CABLE> cables, KM_SPCD_CORE km_spcd_core0, int type) {
        if (cables!=null && cables.size()!=0) {
            List<Integer> ids = new ArrayList<>();
            for (KM_SPCD_CABLE km_spcd_cable : cables) {
                ids.add(km_spcd_cable.getId());
            }
            try {
                List<KM_SPCD_CORE> km_spcd_cores = coreDao.getListForIn("cable_id", ids);//找到与当前屏柜cubicleA相连的KM_SPCD_CORE集合
                String portOdf = km_spcd_core0.getPort_a();
                if (km_spcd_cores!=null && km_spcd_cores.size()!=0){
                    for (KM_SPCD_CORE km_spcd_core:km_spcd_cores){
                        Integer cable_id0 = km_spcd_core.getCable_id();
                        KM_SPCD_CABLE km_spcd_cable0 = (KM_SPCD_CABLE)cableDao.getById(cable_id0);
                        //cubicleA是相连的odf
                        String port_b1 = null;
                        String cubicleB = null;
                        KM_SPCD_CABLE cubicleHole= null;
                        KM_SPCD_CORE km_spcd_core1 = null;

                        if (type == 1 && km_spcd_core.getPort_a().equals(portOdf) && km_spcd_core.getId() != km_spcd_core0.getId()){
                            port_b1 = km_spcd_core.getPort_b();//与之相连的对侧屏柜的odf
                            Integer cable_id = km_spcd_core.getCable_id();
                            KM_SPCD_CABLE km_spcd_cable = (KM_SPCD_CABLE)cableDao.getById(cable_id);
                            cubicleB = km_spcd_cable.getCubicleB();//对侧屏柜信息，如：R66.XLP1A
                            cubicleHole = km_spcd_cable;
                            km_spcd_core1 = km_spcd_core;
                        }else if (type == 2 && km_spcd_core.getPort_b().equals(portOdf) && km_spcd_core.getId() != km_spcd_core0.getId()){
                            port_b1 = km_spcd_core.getPort_a();//与之相连的对侧屏柜的odf
                            Integer cable_id = km_spcd_core.getCable_id();
                            KM_SPCD_CABLE km_spcd_cable = (KM_SPCD_CABLE)cableDao.getById(cable_id);
                            cubicleB = km_spcd_cable.getCubicleA();//对侧屏柜信息，如：R66.XLP1A
                            cubicleHole = km_spcd_cable;
                            km_spcd_core1 = km_spcd_core;
                        }
                        if (!TextUtils.isEmpty(cubicleB) && cubicleB.contains(".")) {
                            String[] split = cubicleB.split("\\.");
                            String region = split[0];//小室，如：R66
                            String cubicle = split[1];//屏柜，如：XLP1A
                            KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION) regionDao.getFirstForEq("substation_id", 1, "name", region);
                            KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getFirstForEq
                                    ("region_id", km_spcd_region.getId(), "substation_id", 1, "name", cubicle);
//                            getCoreFromGlwl(km_spcd_region,km_spcd_cubicle,);
                            getIntcore(km_spcd_core1);
                            break;
                        }
                    }
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    private void getDataFromIntent() {
        Intent intent = getIntent();
        dbName = intent.getStringExtra("dbName");
        if (TextUtils.isEmpty(dbName)){
            dbName = PrefReader.getInstance(this).get(null);
        }
        cableId = intent.getIntExtra("cableID",0);
        glName = intent.getStringExtra("name");
        title.setText(glName);
    }

    @Override
    public void findViewWithId() {
        left_back = (ImageButton)findViewById(R.id.left_back);
        left_back.setOnClickListener(this);
        title = (TextView) findViewById(R.id.title);
        recyclerView = (RecyclerView) findViewById(R.id.recyclerView);
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.left_back) {
            onBackPressed();
        }
    }

    private KM_SPCD_PORT getPort(WholeCircuitBean.UnitWholeBean unitWholeBean) {
        try {
            KM_SPCD_UNIT km_spcd_unit = unitWholeBean.getKm_spcd_unit();
            KM_SPCD_BOARD board = (KM_SPCD_BOARD) boardDao.getFirstForEq("unit_id", km_spcd_unit.getId(), "slot", unitWholeBean.getBoard());
            KM_SPCD_PORT port = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", board.getId(), "no", unitWholeBean.getPort(), "direction", unitWholeBean.getPortADirection());
            return port;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    private KM_SPCD_PORT getPortByPortA(String cubicleA,String port){

        String[] split4 = port.split("\\.");
        String s1 = split4[2];
        KM_SPCD_UNIT km_spcd_unitA = null;
        KM_SPCD_CUBICLE cubicle = null;
        try {
            if (!TextUtils.isEmpty(cubicleA) && cubicleA.contains(".")){
                String[] split = cubicleA.split("\\.");
                KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION) regionDao.getFirstForEq("substation_id", 1, "name", split[0]);
                cubicle = (KM_SPCD_CUBICLE) cubicleDao.getFirstForEq
                        ("region_id", km_spcd_region.getId(), "substation_id", 1, "name", split[1]);
                if (cubicle == null){
                    return null;
                }
            }

            km_spcd_unitA = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", cubicle.getId(), "name", split4[0]);
            if (km_spcd_unitA == null){
                return null;
            }
            KM_SPCD_BOARD km_spcd_boardA = (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unitA.getId(), "slot", split4[1]);
            if (!TextUtils.isEmpty(s1) && s1.contains("-")){
                String[] split5 = s1.split("-");
                if (km_spcd_boardA!=null){
                    KM_SPCD_PORT port1 = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", km_spcd_boardA.getId(), "no", split5[0],"direction",split5[1]);
                    return port1;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }
}
