package com.kemov.visual.spcd.spcdvisualandroidapp.utils;

import android.app.Activity;
import android.text.TextUtils;

import com.baseres.base.PubApp;
import com.kemov.parsescl.HLP_VircChn;
import com.kemov.parsescl.KIEDModel;
import com.kemov.parsescl.KInputs;
import com.kemov.parsescl.KSclModel;
import com.kemov.parsescl.sclLib.ExtRef;
import com.kemov.parsescl.scl_model_lib;
import com.kemov.sclaata.common.app.PubUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.PortConnectBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.Parser.model.CorePort;
import com.share.mycustomviewlib.bean.XshlBean;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class XshlExcludePortUtils {

    static XshlBean xshlBean;
    static KIEDModel kiedModel;
    private static final int TYPE_SV = 0;
    private static final int TYPE_GS = 1;
    private static final int TYPE_SV_GS = 3;
    static Map<String, String> gooseMap;//中心ied的控制块
    private static PortConnectBean portConnectBean;
    private  static BaseDaoImp unitDao;
    private  static BaseDaoImp boardDao;
    private static BaseDaoImp portDao;
    private static BaseDaoImp intcoreDao;
    private static KM_SPCD_UNIT km_spcd_unit;
    private static Integer portOrUnitType;
    protected static KSclModel mKSclModel;

    public static XshlBean getXshlData(Activity activity,String dbName, Integer id, Integer type) {
        portOrUnitType = type;
        String unitName = getUnitName(activity, dbName, id, type);
//        String unitName = "ML2201A";
        if (TextUtils.isEmpty(unitName)){
            return null;
        }
        //scd数据
        kiedModel = getKIEDMode(unitName);//ML2201A
        if (kiedModel == null){
            return null;
        }
        //spcd数据
        portConnectBean = XsFixUtils.getConnectionData(activity,id, type,dbName);
//        portConnectBean = XsUtils.getPortData(activity,id, type,dbName);
        //整合spcd与scd数据
        XshlBean.clear();

        xshlBean = XshlBean.getInstance();
        setLeftUnit();//设置左侧主ied
        setRightList();//设置右侧连接的ied
        //将XshlBean改造成所需的对象
        refixXshlBean();
        //将最终整理的数据循环，没有找到端口的，把虚回路控制块补上去，在从端口找数据进入时这里会把原来不属于端口䣌虚连接信息补全。在setRightList就已经考虑了虚回路
        if(portOrUnitType==1)//传进来的是装置id，所有的外部ied都显示
            disposeData();

        //---结合scd来补全虚实回路端口
        complementPort();

        return xshlBean;
    }

    private static void complementPort() {
        List<XshlBean.RightUnitConnect> list = xshlBean.getList();
        if (list != null && list.size()!=0){
            for (XshlBean.RightUnitConnect rightUnitConnect:list){
                String leftIedName = xshlBean.getLeftUnit().getIedName();
                String rightIedName = rightUnitConnect.getIedName();
                List<XshlBean.ConnectBean> connectBeanList = rightUnitConnect.getConnectBean();
                if (connectBeanList!=null && connectBeanList.size()!=0){
                    for (XshlBean.ConnectBean connectBean:connectBeanList){
                        String boardA = connectBean.getBoardA();
                        String portA = connectBean.getPortA();
                        String boardB = connectBean.getBoardB();
                        String portB = connectBean.getPortB();
                        List<XshlBean.GsooseBean> gsooseBeans = connectBean.getGsooseBeans();
                        if (TextUtils.isEmpty(boardA) && connectBean.getDirection() == 2){//通过scd去找左侧端口
                            setReceivePort(kiedModel,connectBean,rightIedName,1);
                        }
                        if (TextUtils.isEmpty(boardB)  && connectBean.getDirection() == 2){//通过scd去找右侧端口
                            setSendPort(rightIedName,leftIedName,connectBean,1,null);
                        }
                        if (TextUtils.isEmpty(boardA) && connectBean.getDirection() == 1){
                            setSendPort(leftIedName,rightIedName,connectBean,2,null);
                        }
                        if (TextUtils.isEmpty(boardB) && connectBean.getDirection() == 1){
                            KIEDModel kiedModel = mKSclModel.getKIEDInfo(rightIedName);
                            setReceivePort(kiedModel,connectBean,leftIedName,2);
                        }
                    }
                }
            }
        }
    }

    private static void setSendPort(String name1,String name2,XshlBean.ConnectBean connectBean, int type,scl_model_lib.PhysConn phy) {
        scl_model_lib.Communication mCommu = mKSclModel.mCommu;
        int k = mKSclModel.getSizeOfCommunication();
        boolean isBreak = false;
        for(int i = 0;i < k;i++){
            if (isBreak){
                break;
            }
            scl_model_lib.SubNetwork sub = (scl_model_lib.SubNetwork) mCommu.getChild(i);
            int k2 = sub.getChildSize();
            for(int i2 = 0;i2 < k2;i2++){
                if (isBreak){
                    break;
                }
                scl_model_lib.ConnectedAP conAp = (scl_model_lib.ConnectedAP) sub.getChild(i2);
                if(conAp.iedName.equals(name1)) {
                    List<scl_model_lib.PhysConn> physConnList = conAp.physConnList;
                    if (physConnList!=null && physConnList.size()!=0){
                        for (scl_model_lib.PhysConn physConn:physConnList){
                            if (name2.equals(physConn.RemDev_iedName)){
                                String port = null;
                                if (phy == null){
                                    port = physConn.Port;
                                }else {
                                    port = physConn.Port;
                                }
                                if (!TextUtils.isEmpty(port) && port.contains("-")){
                                    String[] split = port.split("-");

                                    connectBean.setVirtualPort(port);
                                    if (type == 1){
                                        connectBean.setBoardB(split[0]);
                                        connectBean.setPortB(split[1]+"-Tx");
                                    }else if (type == 2){
                                        connectBean.setBoardA(split[0]);
                                        connectBean.setPortA(split[1]+"-Tx");
                                    }
                                    isBreak = true;
                                    break;
                                }
                            }else if (isSwitch(physConn)){//如果是交换机，接着找装置 XG2201A
                                setSendPort(name1,physConn.RemDev_iedName,connectBean,type,physConn);
                            }
                        }
                    }
                }
            }
        }
    }

    //判断装置是否是交换机
    private static boolean isSwitch(scl_model_lib.PhysConn phy) {
        String remDev_iedName = phy.RemDev_iedName;
        if(remDev_iedName==null)
            return false;
        List<KIEDModel> switchKIEDList = mKSclModel.getSwitchKIEDList();
        if (switchKIEDList!=null && switchKIEDList.size()!=0){
            for (KIEDModel kiedModel:switchKIEDList){
                if (remDev_iedName.equals(kiedModel.name)){
                    if (kiedModel.desc.contains("交换机")){
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private static void setReceivePort(KIEDModel kiedMode, XshlBean.ConnectBean connectBean,String name, int type) {
        int k = kiedMode.sizeOfInputs();
        for(int i = 0;i < k;i++){
            KInputs inputs = kiedMode.getInputs(i);
            int e = inputs.sizeOfExr();
            for(int m = 0;m < e;m++){
                ExtRef extRef = inputs.getChild(m);
                String iedName = extRef.iedName;
                if (!TextUtils.isEmpty(iedName) && iedName.equals(name)){
                    String intAddr = extRef.intAddr;
                    if (intAddr.contains(":") && intAddr.contains("-")){
                        String board = intAddr.substring(0, intAddr.indexOf("-"));
                        String port = intAddr.substring(intAddr.indexOf("-") + 1, intAddr.indexOf(":")) + "-Rx";

                        connectBean.setVirtualPort(intAddr.substring(0, intAddr.indexOf(":")));
                        if (type == 1){
                            connectBean.setBoardA(board);
                            connectBean.setPortA(port);
                        }else if (type == 2){
                            connectBean.setBoardB(board);
                            connectBean.setPortB(port);
                        }
                        break;
                    }
                }
            }
        }
    }

    private static void disposeData() {
        if (xshlBean != null){
            List<XshlBean.RightUnitConnect> list = xshlBean.getList();
            for (XshlBean.RightUnitConnect rightUnitConnect:list){
                if (rightUnitConnect.getConnectBean() == null || rightUnitConnect.getConnectBean().size() == 0){
                    String iedName = rightUnitConnect.getIedName();
                    for (KIEDModel.HLP_VircIED vIED : kiedModel.getVIEDs()) {
                        if (iedName.equals(vIED.sIedName)){
                            //设置主ied与外部ied的连接线关系
                            List<XshlBean.ConnectBean> connectBeanList = new ArrayList<>();
                            rightUnitConnect.setConnectBean(connectBeanList);
                            if(vIED.getVSendCtrls(TYPE_SV) !=null && vIED.getVSendCtrls(TYPE_SV).size()!=0){
                                XshlBean.ConnectBean connectBean = new XshlBean.ConnectBean();
                                connectBeanList.add(connectBean);
                                for (KIEDModel.HLP_VircCtrl vIedCtrl_SV : vIED.getVSendCtrls(TYPE_SV)) {
                                    setPort(vIedCtrl_SV,vIED,connectBean,1);
                                }
                            }
                            if(vIED.getVSendCtrls(TYPE_GS) !=null && vIED.getVSendCtrls(TYPE_GS).size()!=0){
                                XshlBean.ConnectBean connectBean = new XshlBean.ConnectBean();
                                connectBeanList.add(connectBean);
                                for (KIEDModel.HLP_VircCtrl vIedCtrl_GS : vIED.getVSendCtrls(TYPE_GS)) {
                                    setPort(vIedCtrl_GS,vIED,connectBean,2);
                                }
                            }
                            if(vIED.getVRecCtrls(TYPE_SV_GS) !=null && vIED.getVRecCtrls(TYPE_SV_GS).size()!=0){
                                XshlBean.ConnectBean connectBean = new XshlBean.ConnectBean();
                                connectBeanList.add(connectBean);
                                for (KIEDModel.HLP_VircCtrl vCtrl : vIED.getVRecCtrls(TYPE_SV_GS)) {
                                    setPort(vCtrl,vIED,connectBean,3);
                                }
                            }
                            break;
                        }
                    }
                }else {
                    String iedName = rightUnitConnect.getIedName();
                    for (KIEDModel.HLP_VircIED vIED : kiedModel.getVIEDs()) {
                        if (iedName.equals(vIED.sIedName)){
                            //设置主ied与外部ied的连接线关系
                            List<XshlBean.ConnectBean> connectBeanList = rightUnitConnect.getConnectBean();
                            if(vIED.getVSendCtrls(TYPE_SV) !=null && vIED.getVSendCtrls(TYPE_SV).size()!=0){

                                for (KIEDModel.HLP_VircCtrl vIedCtrl_SV : vIED.getVSendCtrls(TYPE_SV)) {
                                    //判断这个goose是不是已经添加过了
                                    boolean isGo = false;
                                    isGo = isHaveThisGoose(connectBeanList,vIedCtrl_SV,isGo);
                                    if (isGo){
                                        continue;
                                    }

                                    XshlBean.ConnectBean connectBean = new XshlBean.ConnectBean();
                                    connectBeanList.add(connectBean);
                                    setPort(vIedCtrl_SV,vIED,connectBean,1);
                                }
                            }
                            if(vIED.getVSendCtrls(TYPE_GS) !=null && vIED.getVSendCtrls(TYPE_GS).size()!=0){
                                for (KIEDModel.HLP_VircCtrl vIedCtrl_GS : vIED.getVSendCtrls(TYPE_GS)) {
                                    //判断这个goose是不是已经添加过了
                                    boolean isGo = false;
                                    isGo = isHaveThisGoose(connectBeanList,vIedCtrl_GS,isGo);
                                    if (isGo){
                                        continue;
                                    }

                                    XshlBean.ConnectBean connectBean = new XshlBean.ConnectBean();
                                    connectBeanList.add(connectBean);
                                    setPort(vIedCtrl_GS,vIED,connectBean,2);
                                }
                            }
                            if(vIED.getVRecCtrls(TYPE_SV_GS) !=null && vIED.getVRecCtrls(TYPE_SV_GS).size()!=0){
                                for (KIEDModel.HLP_VircCtrl vCtrl : vIED.getVRecCtrls(TYPE_SV_GS)) {
                                    //判断这个goose是不是已经添加过了
                                    boolean isGo = false;
                                    isGo = isHaveThisGoose(connectBeanList,vCtrl,isGo);
                                    if (isGo){
                                        continue;
                                    }

                                    XshlBean.ConnectBean connectBean = new XshlBean.ConnectBean();
                                    connectBeanList.add(connectBean);
                                    setPort(vCtrl,vIED,connectBean,3);
                                }
                            }
                            break;
                        }
                    }
                }
            }
        }
    }

    private static boolean isHaveThisGoose(List<XshlBean.ConnectBean> connectBeanList, KIEDModel.HLP_VircCtrl vIedCtrl_SV, boolean isGo) {
        for (XshlBean.ConnectBean connectBean1:connectBeanList){
            if (isGo){
                break;
            }
            List<XshlBean.GsooseBean> gsooseBeans = connectBean1.getGsooseBeans();
            for (XshlBean.GsooseBean gsooseBean:gsooseBeans){
                if (gsooseBean.getAppId().equals(vIedCtrl_SV.sAPPID)){
                    isGo = true;
                    break;
                }
            }
        }
        return isGo;
    }

    /**
     *
     * @param vIedCtrl_SV
     * @param vIED
     * @param connectBean
     * @param type 1 接收的 sv 2 接收的goose 3 发送的sv goose
     */
    private static void setPort(KIEDModel.HLP_VircCtrl vIedCtrl_SV, KIEDModel.HLP_VircIED vIED, XshlBean.ConnectBean connectBean, Integer type) {
        List<XshlBean.GsooseBean> gsooseBeans = connectBean.getGsooseBeans();
        if (gsooseBeans == null){
            gsooseBeans = new ArrayList<>();
        }
        String appid = vIedCtrl_SV.getAppId();
        XshlBean.GsooseBean gsooseBean = new XshlBean.GsooseBean();
        if (type == 1){
            gsooseBean.setGoose("SV 0x" + appid);
            connectBean.setDirection(2);
        }else if (type == 2){
            gsooseBean.setGoose("GOOSE 0x" + appid);
            connectBean.setDirection(2);
        }else if (type == 3){
            String goose = gooseMap.get(appid);
            gsooseBean.setGoose(goose);
            connectBean.setDirection(1);
        }
        gsooseBean.setAppId(appid);
        gsooseBeans.add(gsooseBean);
        connectBean.setGsooseBeans(gsooseBeans);
    }

    private static void refixXshlBean() {
        List<XshlBean.RightUnitConnect> list = xshlBean.getList();
        if (list!=null && list.size()!=0){
            for (XshlBean.RightUnitConnect rightUnitConnect:list){
                List<XshlBean.RightConnect> list1 = rightUnitConnect.getList();//这个装置的所有连线
                if (list1!=null && list1.size()!=0){
                    Map<String, XshlBean.ConnectBean> boardPortMaps = new HashMap<>();
                    List<XshlBean.ConnectBean> connectBeanList = new ArrayList<>();
                    for (XshlBean.RightConnect rightConnect :list1){
                        String boardPort = rightConnect.getBoardA() + "-" + rightConnect.getPortA()+ rightConnect.getDirection();
                        if (!TextUtils.isEmpty(rightConnect.getGoose())){
                            if (boardPortMaps.containsKey(boardPort)){
                                XshlBean.ConnectBean connectBean = boardPortMaps.get(boardPort);
                                List<XshlBean.GsooseBean> gsooseBeans = connectBean.getGsooseBeans();

                                XshlBean.GsooseBean gsooseBean = new XshlBean.GsooseBean();
                                gsooseBean.setAppId(rightConnect.getAppId());
                                gsooseBean.setGoose(rightConnect.getGoose());
                                gsooseBeans.add(gsooseBean);
                            }else {
                                XshlBean.ConnectBean connectBean = new XshlBean.ConnectBean();
                                List<XshlBean.GsooseBean> gsooseBeanList = new ArrayList<>();
                                connectBean.setGsooseBeans(gsooseBeanList);
                                connectBean.setHasOdf(rightConnect.isHasOdf());
                                connectBean.setHasSwitch(rightConnect.isHasSwitch());
                                connectBean.setProblem(rightConnect.getProblem());

                                //设置界面需要的对比端口
                                connectBean.setVirtualPort(rightConnect.getVirtualPort());
                                connectBean.setVirtualOffsidePort1(rightConnect.getVirtualOffsidePort1());
                                connectBean.setVirtualOffsidePort2(rightConnect.getVirtualOffsidePort2());

                                connectBean.setBoardA(rightConnect.getBoardA());
                                connectBean.setPortA(rightConnect.getPortA());
                                connectBean.setBoardB(rightConnect.getBoardB());
                                connectBean.setPortB(rightConnect.getPortB());
                                connectBean.setDirection(rightConnect.getDirection());
                                XshlBean.GsooseBean gsooseBean = new XshlBean.GsooseBean();
                                gsooseBean.setAppId(rightConnect.getAppId());
                                gsooseBean.setGoose(rightConnect.getGoose());
                                gsooseBeanList.add(gsooseBean);
                                connectBeanList.add(connectBean);
                                //新建过对象添加到集合做标识
                                boardPortMaps.put(boardPort,connectBean);
                            }
                        }
                    }
                    rightUnitConnect.setConnectBean(connectBeanList);
                }
            }
        }
    }

    private static String getUnitName(Activity activity, String dbName, Integer id, Integer type){
        unitDao = new BaseDaoImp(activity.getApplicationContext(), KM_SPCD_UNIT.class, dbName);
        boardDao = new BaseDaoImp(activity.getApplicationContext(), KM_SPCD_BOARD.class, dbName);
        portDao = new BaseDaoImp(activity.getApplicationContext(), KM_SPCD_PORT.class, dbName);
        intcoreDao = new BaseDaoImp(activity.getApplicationContext(), KM_SPCD_INTCORE.class, dbName);
        if (type == 1){//传进来的是装置unitid
            km_spcd_unit = (KM_SPCD_UNIT)unitDao.getById(id);//装置
        }else if (type == 2){//传进来的是装置IntCoreId
            KM_SPCD_INTCORE intcore = (KM_SPCD_INTCORE) intcoreDao.getById(id);
            km_spcd_unit = SpcdUtils.getUnitByIntcoreId(activity, dbName, intcore);
        }else if (type == 3){//传进来的是装置PortId
            KM_SPCD_PORT port = (KM_SPCD_PORT) portDao.getById(id);
            km_spcd_unit = SpcdUtils.getUnitByPortId(activity, dbName, port);
        }
        if (km_spcd_unit!=null){
            String ied_name = km_spcd_unit.getIed_name();
            if (!TextUtils.isEmpty(ied_name)){
                return ied_name;
            }
        }
        return null;
    }

    private static KIEDModel getKIEDMode(String name){
        PubUtils mPubUtils = PubUtils.getPubUtils();
        mKSclModel = mPubUtils.getSclModel();
        if (mKSclModel == null){
            return null;
        }else {
            return mKSclModel.getKIEDInfo(name);
        }
    }

    private static void setLeftUnit() {
        XshlBean.LeftUnit leftUnit = new XshlBean.LeftUnit();
        leftUnit.setIedName(km_spcd_unit.getIed_name());
        leftUnit.setDesc(kiedModel.desc);
        leftUnit.setId(km_spcd_unit.getId());
        xshlBean.setLeftUnit(leftUnit);
    }

    private static void setRightList() {
        if(kiedModel.getVIEDs()==null){
            return;
        }
        gooseMap = getCentricIED();
        //spcd查询到的ied
        List<PortConnectBean.IedConnectBean> iedList = portConnectBean.getIedList();
        List<String> iedsList = new ArrayList<>();
        if (iedList!=null && iedList.size()!=0){
            for (PortConnectBean.IedConnectBean iedConnectBean:iedList){
                KM_SPCD_UNIT otherUnit = iedConnectBean.getOtherUnit();
                if (!TextUtils.isEmpty(otherUnit.getIed_name())){
                    iedsList.add(otherUnit.getIed_name());
                }
            }
        }
        //判断是否是对时，对时没有虚实回路，显示界面要做提示
        if (iedList!=null && iedList.size()!=0){
            if (iedsList == null || iedsList.size() == 0){
                xshlBean.setIsDS(1);
            }
        }

        List<XshlBean.RightUnitConnect> unitList = new ArrayList<>();
        for (KIEDModel.HLP_VircIED vIED : kiedModel.getVIEDs()) {
            if (portOrUnitType == 1){//传进来的是装置id，所有的外部ied都显示
                setUnitList(vIED,unitList,iedList);
            }else{//传进来的是portId,或者intcoreId，就只显示部分
                if (iedsList!=null && iedsList.size()!=0){
                    if (iedsList.contains(vIED.sIedName)){
                        setUnitList(vIED,unitList,iedList);
                    }
                }
            }

        }
        xshlBean.setList(unitList);
    }

    private static void setUnitList(KIEDModel.HLP_VircIED vIED, List<XshlBean.RightUnitConnect> unitList,List<PortConnectBean.IedConnectBean> iedList){
        List<XshlBean.RightConnect> connectList = new ArrayList<>();
        //设置右侧外部ied基本信息
        XshlBean.RightUnitConnect rightUnitConnect = new XshlBean.RightUnitConnect();
        rightUnitConnect.setIedName(vIED.sIedName);
        rightUnitConnect.setDesc(vIED.getVIedDescInfo());
        //设置Id
        if (iedList !=null && iedList.size()!=0){
            for (PortConnectBean.IedConnectBean iedConnectBean:iedList){
                KM_SPCD_UNIT otherUnit = iedConnectBean.getOtherUnit();
                if (otherUnit!=null){
                    String ied_name = otherUnit.getIed_name();
                    if (ied_name.equals(vIED.sIedName)){
                        rightUnitConnect.setId(otherUnit.getId());
                        break;
                    }
                }
            }
        }
        //设置主ied与外部ied的连接线关系
        for (KIEDModel.HLP_VircCtrl vIedCtrl_SV : vIED.getVSendCtrls(TYPE_SV)) {
            setUnitConnectEx(vIedCtrl_SV,vIED,connectList,1);
        }
        for (KIEDModel.HLP_VircCtrl vIedCtrl_GS : vIED.getVSendCtrls(TYPE_GS)) {
            setUnitConnectEx(vIedCtrl_GS,vIED,connectList,2);
        }
        ArrayList<String> recList = new ArrayList<String>();
        for (KIEDModel.HLP_VircCtrl vCtrl : vIED.getVRecCtrls(TYPE_SV_GS)) {
            recList.add(vCtrl.getAppId());
            setUnitConnectEx(vCtrl,vIED,connectList,3);
        }
        rightUnitConnect.setList(removeErrorConnect(connectList));
        unitList.add(rightUnitConnect);
    }

    /**
     * 去除错误的链接，根据scd的接收虚端子去除错误的链接
     * @param connectList
     */
    private static List<XshlBean.RightConnect> removeErrorConnect(List<XshlBean.RightConnect> connectList){
        List<XshlBean.RightConnect> ret=new ArrayList<>();
        Map<String,List<XshlBean.RightConnect>> appIdMap=new HashMap<>();
        //1.1个appid从两个口发送或者接收需要进行去除操作
        for(XshlBean.RightConnect elem :connectList) {
            if(!appIdMap.containsKey(elem.getAppId())){
               List<XshlBean.RightConnect> values=new ArrayList<>();
               values.add(elem);
               appIdMap.put(elem.getAppId(),values);
            }else{
                List<XshlBean.RightConnect> tmpList=appIdMap.get(elem.getAppId());
                tmpList.add(elem);
                appIdMap.put(elem.getAppId(),tmpList);
            }
        }
        for(String appId:appIdMap.keySet()){
            if(appIdMap.get(appId).size()==1){
                ret.addAll(appIdMap.get(appId));
            }else{//有appid从多个端口发送或者接收
                List<XshlBean.RightConnect> tmpList=appIdMap.get(appId);
                if(tmpList==null)
                    continue;
                boolean bAdded=false;
                for(XshlBean.RightConnect elem:tmpList){
                    //virtualPort为SCD中的端口,VirtualOffsidePort1以及VirtualOffsidePort2为spcd中的端口
                    String scdVirtualPort=elem.getVirtualPort();
                    if(scdVirtualPort!=null){
                        List<String> scdPortList=new ArrayList<>();
                        for(String port:scdVirtualPort.split("/"))
                            scdPortList.add(port);
                        if((!scdPortList.contains(elem.getVirtualOffsidePort1()))
                         &&(!scdPortList.contains(elem.getVirtualOffsidePort2())))
                            continue;
                    }
                    bAdded=true;
                    ret.add(elem);
                }
                if(!bAdded)//如果通过此规则把所有端口都排除了,可能这个规则就有不周全的地方了,需要重现考虑
                    ret.addAll(tmpList);
            }
        }
        return ret;
    }
    static boolean  isAppIdFromIedConnection(PortConnectBean.IedConnectBean iedConnectBean,String scdVirPort,String iedName,boolean isPortA){
        if(iedConnectBean.getIntcoress().size()==0)
            return false;
        String port="";
        String board="";
        if(isPortA){
            port=iedConnectBean.getIntcoress().get(0).getPortA().split("-")[0];
            board=iedConnectBean.getIntcoress().get(0).getBoardA();
        } else {
            port=iedConnectBean.getIntcoress().get(0).getPortB().split("-")[0];
            board=iedConnectBean.getIntcoress().get(0).getBoardB();
        }
        if((board==null)||board.isEmpty())
            return false;
        String virtualOffsidePort1=getScdPort(board,port,0);
        String virtualOffsidePort2=getScdPort(board,port,1);
        List<String> scdPortArray=new ArrayList<>();
        if(scdVirPort==null||scdVirPort.isEmpty())
            return true;
        for(String portItm:scdVirPort.split("/"))
            scdPortArray.add(portItm);
        if(scdPortArray.contains(virtualOffsidePort1)||scdPortArray.contains(virtualOffsidePort2))
            return true;
        //判断此unit是否还有其他的端口和scdPort一致
        List<KM_SPCD_UNIT> unitList=unitDao.getListForEq("ied_name",iedName);
        if(unitList==null||unitList.size()==0)
            return true;
        KM_SPCD_UNIT unit=unitList.get(0);
        List<KM_SPCD_BOARD> boards=boardDao.getListForEq("unit_id",unit.getId());
        if(boards==null||boards.size()==0)
            return true;
        for(KM_SPCD_BOARD boardItm:boards){
            List<KM_SPCD_PORT> ports=portDao.getListForEq("board_id",boardItm.getId());
            if(ports==null||ports.size()==0)
                continue;
            for(KM_SPCD_PORT portItm:ports) {
                if(scdPortArray.contains(getScdPort(boardItm.getSlot(), portItm.getNo(),1))||
                        scdPortArray.contains(getScdPort(boardItm.getSlot(), portItm.getNo(),2)))
                    return false;
            }
        }
        return true;
    }
    private static String getVirPortFromScd(KIEDModel.HLP_VircCtrl vIedCtrl_SV,boolean isReceived){
        //找到虚回路端的端口,这里是接收端口
        String port = null;
        if(isReceived) {//接收的虚连接
            if (vIedCtrl_SV.isHasPhysConnPath()) {
                port = vIedCtrl_SV.getPhysConnPath().getRecvPort();
            }
        } else {//发送的端子，要从接收端找物理连接
            HLP_VircChn.VirFCDAList mVirFCDAList = vIedCtrl_SV.mVirFCDAList;
            ArrayList<HLP_VircChn.HLP_VircFCDA> chns = mVirFCDAList.chns;
            if (chns!=null && chns.size()!=0) {//这里要循环找,可能有的extRef没有配置端口
                for(HLP_VircChn.HLP_VircFCDA hlp_vircFCDA :chns) {
                    ArrayList<ExtRef> extrefs = hlp_vircFCDA.extrefs;
                    if (extrefs != null && extrefs.size() != 0) {
                        for (ExtRef extRef : extrefs) {
                            List<String> phyPorts = extRef.getReceivePhyPorts();
                            if (phyPorts.size() > 0) {
                                port = phyPorts.stream().collect(Collectors.joining("/"));
                                break;
                            }
                        }
                    }
                    if(port!=null&&!port.isEmpty())
                        break;
                }
            }
        }
        return port==null?"":port;
    }
    /**
     * 虚实回路的端口并与实际回路进行匹配
     * @param vIedCtrl_SV
     * @param vIED
     * @param connectList
     * @param virConnectionType 1 其他ied 发送的sv 2 其他ied发送的 goose 3 本ied发送的goose sv
     */
    public static void setUnitConnectEx(KIEDModel.HLP_VircCtrl vIedCtrl_SV, KIEDModel.HLP_VircIED vIED, List<XshlBean.RightConnect> connectList, Integer virConnectionType) {
        String appid = vIedCtrl_SV.getAppId();
        //找到虚回路端的端口,这里是接收端口
        final String port = getVirPortFromScd(vIedCtrl_SV, virConnectionType != 3);//可能是1-E/1-F
        //找实回路找到连接并配置
        List<PortConnectBean.IedConnectBean> iedList = portConnectBean.getIedList();
        if ((iedList == null) || iedList.size() == 0)
            return;
        //过滤找到符合条件的 iedConnectBean
        List<PortConnectBean.IedConnectBean> findList = iedList.stream().filter(iedConnectBean ->
                vIED.sIedName.equals(iedConnectBean.getOtherUnit().getIed_name()) && isAppIdFromIedConnection(iedConnectBean, port, vIED.sIedName, virConnectionType != 3)).collect(Collectors.toList());
        if (findList.size() == 0)
            return;
        //找到符合条件的intCoreBean
        for (PortConnectBean.IedConnectBean iedConnectBean : findList) {
            if (iedConnectBean.getIntcoress().size() == 0)
                continue;
            ///1.在intcoress中找到符合条件(读写匹配)的intcores,注意这里认为一个端口连接最多2个intcore：Tx Rx
            Optional<PortConnectBean.IntcoreBean> findIntCoreOption = iedConnectBean.getIntcoress().stream().filter(intcoreBean -> {
                if (virConnectionType != 3)
                    return !TextUtils.isEmpty(intcoreBean.getPortA()) && intcoreBean.getPortA().contains("-") && intcoreBean.getPortA().contains("Rx");
                else
                    return !TextUtils.isEmpty(intcoreBean.getPortA()) && intcoreBean.getPortA().contains("-") && intcoreBean.getPortA().contains("Tx");
            }).findFirst();
            PortConnectBean.IntcoreBean intcoreBean = iedConnectBean.getIntcoress().get(0);
            boolean txRxError = true;//发送端口错误
            if (findIntCoreOption.isPresent()) {
                intcoreBean = findIntCoreOption.get();
                txRxError = false;
            }
            XshlBean.RightConnect rightConnect=null;
            List<String> errorList=new ArrayList<>();
            if (virConnectionType != 3) {//右侧ied发到本ied上,本ied是接收端
                String[] split = intcoreBean.getPortA().split("-");
                //转换规则，适配另一种scd中的表达模式比如spcd的 B15-01对应scd的15-A
                String scdPort = getScdPort(intcoreBean.getBoardA(), split[0], 0);
                String scdPort1 = getScdPort(intcoreBean.getBoardA(), split[0], 1);
                //设置虚、实端口，以便界面比对
                rightConnect = new XshlBean.RightConnect();
                rightConnect.setHasOdf(iedConnectBean.isHasOdf());
                rightConnect.setHasSwitch(iedConnectBean.isHasSwitch());
                rightConnect.setVirtualPort(port);//设置虚回路端口
                rightConnect.setVirtualOffsidePort1(scdPort);//设置实回路转换端口1
                rightConnect.setVirtualOffsidePort2(scdPort1);//设置实回路转换端口2
                //设置数据
                rightConnect.setAppId(appid);
                rightConnect.setBoardA(intcoreBean.getBoardA());
                rightConnect.setPortA(intcoreBean.getPortA());

                rightConnect.setBoardB(intcoreBean.getBoardB());
                rightConnect.setPortB(intcoreBean.getPortB());
                rightConnect.setDirection(2);
                if (virConnectionType == 1) {
                    rightConnect.setGoose("SV 0x" + appid);
                } else if (virConnectionType == 2) {
                    rightConnect.setGoose("GOOSE 0x" + appid);
                }

                if(txRxError)
                    errorList.add("SCD虚端口["+port+"]是接收端口,但SPCD中["+scdPort+"]是发送端口");
                if (!TextUtils.isEmpty(port)) {
                    List<String> scdPortLst=new ArrayList<>();
                    scdPortLst.addAll(Arrays.asList(port.split("/")));
                    if(!scdPortLst.contains(scdPort)&&!scdPortLst.contains(scdPort1)){
                        errorList.add("scd中的端口[" + scdPort + "]和spcd中的端口[" + scdPort + "]不一致！");
                    }
                }
                rightConnect.setProblem(errorList.stream()
                        .collect(Collectors.joining("\n")));
                connectList.add(rightConnect);
            } else {//本ied发到右侧ied上,右侧ied是接收端
                String[] split = intcoreBean.getPortB().split("-");
                //转换规则，适配另一种scd中的表达模式比如spcd的 B15-01对应scd的15-A
                String scdPort = getScdPort(intcoreBean.getBoardB(), split[0], 0);
                String scdPort1 = getScdPort(intcoreBean.getBoardB(), split[0], 1);

                rightConnect = new XshlBean.RightConnect();
                rightConnect.setHasOdf(iedConnectBean.isHasOdf());
                rightConnect.setHasSwitch(iedConnectBean.isHasSwitch());
                //设置虚、实端口，以便界面比对
                rightConnect.setVirtualPort(port);//设置虚回路端口
                rightConnect.setVirtualOffsidePort1(scdPort);//设置实回路转换端口1
                rightConnect.setVirtualOffsidePort2(scdPort1);//设置实回路转换端口2
                //设置数据
                rightConnect.setAppId(appid);
                rightConnect.setBoardA(intcoreBean.getBoardA());
                rightConnect.setPortA(intcoreBean.getPortA());
                rightConnect.setBoardB(intcoreBean.getBoardB());
                rightConnect.setPortB(intcoreBean.getPortB());
                rightConnect.setDirection(1);

                String goose = gooseMap.get(appid);
                rightConnect.setGoose(goose);
                if(txRxError)
                    rightConnect.setProblem("SCD虚端口["+port+"]是接收端口,但SPCD中["+scdPort+"]是发送端口");
                if (!TextUtils.isEmpty(port)) {
                    List<String> scdPortLst=new ArrayList<>();
                    scdPortLst.addAll(Arrays.asList(port.split("/")));
                    if(!scdPortLst.contains(scdPort)&&!scdPortLst.contains(scdPort1)){
                        errorList.add("scd中的端口[" + scdPort + "]和spcd中的端口[" + scdPort + "]不一致！");
                    }
                }
                rightConnect.setProblem(errorList.stream()
                        .collect(Collectors.joining("\n")));
                connectList.add(rightConnect);
            }
        }
    }

    private static String getScdPort(String boardA, String port,int type) {
        String board = Utils.replaceLetter(boardA);
        String portTmp=port;
        if (portTmp.length()<2){
            portTmp = "0"+portTmp;
        }
        String letter = Utils.getLetter(portTmp);
        if (type == 1){
            if (board.startsWith("0")){
                board = board.substring(1, board.length());
            }
        }
        if(letter==null)
            return board+"-"+port;
        return board+"-"+letter;
    }

    private static Map<String,String> getCentricIED() {
        Map<String, String> map = new HashMap<>();
        int cSvSum = kiedModel.sizeOfSend(TYPE_SV);
        int cGsSum = kiedModel.sizeOfSend(TYPE_GS);
        //init centricIEDs --SV
        for(int i = 0; i<cSvSum; i++){
            String appid = kiedModel.listOfSend(TYPE_SV).get(i).APPID;
            map.put(appid,"SV 0x" + appid);
        }
        //init centricIEDs --GOOSE
        for(int i = 0; i < cGsSum; i++){
            String appid = kiedModel.listOfSend(TYPE_GS).get(i).APPID;
            map.put(appid,"GOOSE 0x" + appid);
        }
        return map;
    }
}
