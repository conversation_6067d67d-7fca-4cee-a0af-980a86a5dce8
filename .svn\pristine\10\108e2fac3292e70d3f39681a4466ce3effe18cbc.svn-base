package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.text.TextPaint;

import java.util.List;

//端口的组件
public class PortsCanvasBean implements CanvasDraw{
    int index;//排序
    int parent;//端口所属装置、所属装置
    //PortConnectType s;
    PortsLocation portsLocation;

    List<PortBaseBean> ports;

    //查看端口的


    public PortsLocation getPortsLocation() {
        return portsLocation;
    }

    public void setPortsLocation(PortsLocation portsLocation) {
        this.portsLocation = portsLocation;
    }

    public List<PortBaseBean> getPorts() {
        return ports;
    }

    public void setPorts(List<PortBaseBean> ports) {
        this.ports = ports;
    }

    public void intiPortsRects(RectF device_rect) {
        if (this.ports == null ) throw new NullPointerException("请先赋值端口ports信息！");

        if (portsLocation == PortsLocation.Middle_ShortCircuited){//odf短接的情况才会画2对ports
            if (this.ports.size() != 4){
                //throw new RuntimeException("短接情况下,装置（ODF）的ports数量应该为4，实际为"+this.ports.size());
            }
            for (PortBaseBean port: ports) {
                port.initRectWithParentDevRect(device_rect,portsLocation);
            }
            // TODO: guoyong 2019/11/1  暂不考虑此种情况
        } else {
            // TODO: guoyong 2019/11/1  暂不考虑IED中存在的插件只含有Rx或Tx的情况，必须成对出现。
            if (this.ports.size() != 2){
                //throw new RuntimeException("其他情况下,装置（IED）的ports数量应该为2，实际为"+this.ports.size());
            }
            for (PortBaseBean port: ports) {
                port.initRectWithParentDevRect(device_rect,portsLocation);
            }
        }


    }

    @Override
    public void draw(Canvas canvas, Paint mPaint, TextPaint txtPaint){
        mPaint.setColor(0xFFF3C6A0);
        for (PortBaseBean port: ports) {
            port.draw(canvas,mPaint,txtPaint);
        }
        //canvas.drawText("hhhhh",10,10, txtPaint);
    }
}
