package com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual;

import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ExpandableListView;
import android.widget.ListView;

import com.kemov.visual.spcd.spcdvisualandroidapp.R;

public class DevicePortAboutListViewFragment extends Fragment {
    public final static int PORT_UNIT_TYPE_IED = 1;
    public final static int PORT_UNIT_TYPE_ODF = 2;
    public final static int PORT_UNIT_TYPE = PORT_UNIT_TYPE_IED;

    View rootView;
    ExpandableListView iedCbListView;
    ListView odfListView;
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        rootView = inflater.inflate(R.layout.device_portabout_frament, container, false);

        return rootView;
        //return super.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        iedCbListView = rootView.findViewById(R.id.iedCbListView);
        odfListView = rootView.findViewById(R.id.odfListView);
        /**/
    }
}
