package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.complexgraphics;

import android.graphics.Canvas;
import android.graphics.Rect;

/**
 * 绘制压板 方框里面包含两个圆圈+方框外的描述文字
 * <AUTHOR>
 *
 */
public class CanvasYaBan extends CanvasBase {

	//压板的所在区域
	private Rect rect = new Rect(50, 50,300,150); 
	
	public CanvasYaBan(Canvas canvas, Rect rect) {
		this.canvas = canvas;
		this.rect = rect;
		initDraw();
	}

	@Override
	protected void initDraw() {
		
		//绘制边框
		canvas.drawRect(rect, defPaint);
		
		//绘制两圆圈
		canvas.drawCircle(rect.left+rect.height()/2, rect.top+rect.height()/2, rect.height()/2 -3, defPaint);
		canvas.drawCircle(rect.right-rect.height()/2, rect.top+rect.height()/2, rect.height()/2 -3, defPaint);
		
		//绘制右侧描述文字
		canvas.drawText("YaBanDesc", rect.right + 10, rect.centerY()+10, DefTxtPaint);
	}
	
	//更新压板状态
	public void update(){
		
	}

}
