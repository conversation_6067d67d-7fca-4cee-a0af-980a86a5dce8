package com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.connection;

import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.Parser.model.*;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 端口连接IED-->IED
 */
public class PortPhyPath {
    private final Port startPort;
    private Port endPort;
    private List<Port> switchOrODFPortNodes;//中间经过的路径
    protected PortPhyPath(Port startPort){
        this.startPort=startPort;
    }

    /**
     *添加一个新端口
     * 1.如果新端口的unit是IED,OTHER,并且和startPort的设备不一样，那么找到了path，返回true
     * 2.如果是ODF，只要在switchOrODFNodes中没有出现过，则路径正确，返回true
     * 3.如果是SWITCH,在switchOrODFNodes中出现过小于1次(最多出现2次)，则路径正确，返回true
     * @param newPort 待加入的新端口
     * @return 能够加入路径中的正确端口 返回true，否则返回false
     */
    protected boolean add(Port newPort){
        //1.port是否重复 unit相等(只有switch允许)，board相等 port
        if(newPort==null)
            return false;
        if(switchOrODFPortNodes==null)
            switchOrODFPortNodes=new LinkedList<>();
        UnitClass newUnitClass=newPort.getUnit().getUnitClass();
        if (newUnitClass == UnitClass.IED) {
            if (!newPort.getUnit().equals(startPort.getUnit())) {
                endPort = newPort;
                return true;
            }
            return false;
        }
        //交换机会在经过的路径上出现两次，大于两次就属于回环，不能加入
        long maxCountLimit =  newUnitClass==UnitClass.SWITCH?2:1;
        //统计此port对应Unit在路径中出现的次数
        long count = switchOrODFPortNodes.stream()
                .filter(portNode -> portNode.getUnit().equals(newPort.getUnit()))
                .count();
        if (count >= maxCountLimit)
            return false;
        switchOrODFPortNodes.add(newPort);
        return true;
    }
    protected Port getLastConnectedPort(){
        if((switchOrODFPortNodes==null)||(switchOrODFPortNodes.isEmpty()))
            return startPort;
        return switchOrODFPortNodes.get(switchOrODFPortNodes.size()-1);
    }
    public Port getStartPort() {
        return startPort;
    }

    public Port getEndPort() {
        return endPort;
    }
    public List<Port> getSwitchOrODFPortNodes(){
        if(switchOrODFPortNodes==null)
           return new LinkedList<>();
        return switchOrODFPortNodes;
    }
    public List<Port> getPathAllPort(){
        List<Port> ret=new ArrayList<>();
        if(startPort!=null)
            ret.add(startPort);
        ret.addAll(getSwitchOrODFPortNodes());
        if(endPort!=null)
            ret.add(endPort);
        return ret;
    }
    @Override
    public PortPhyPath clone(){
        PortPhyPath cloneObject=new PortPhyPath(this.startPort);
        if(switchOrODFPortNodes!=null){
            for(Port nodeItm:this.switchOrODFPortNodes){
                cloneObject.add(nodeItm);
            }
        }
        cloneObject.endPort=this.endPort;
        return cloneObject;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PortPhyPath that = (PortPhyPath) o;
        return Objects.equals(startPort, that.startPort) && Objects.equals(endPort, that.endPort) && Objects.equals(switchOrODFPortNodes, that.switchOrODFPortNodes);
    }

    @Override
    public int hashCode() {
        return Objects.hash(startPort, endPort, switchOrODFPortNodes);
    }

    public void print(){
        if(startPort==null)
            return;
        StringBuilder builder=new StringBuilder();
        builder.append(isRxTxPair()?"[O]":"[X]");
        builder.append(startPort.toString()).append("<-->");
        if(switchOrODFPortNodes!=null){
            for(Port portItm:switchOrODFPortNodes){
                builder.append(portItm.toString()).append("<-->");
            }
        }
        builder.append(endPort.toString());
        System.out.println(builder.toString());
    }

    /**
     * 判断物理路径是否配对的物理路径
     * a.判断规则每一个节点的端口都相同，并且收发配对
     * b.没有判断路径反向的情况
     * @param otherPath 其他的路径
     * @return true 可以配对，false
     */
    public boolean isPairPortPath(PortPhyPath otherPath){
        //
        if(!getStartPort().equals(otherPath.getStartPort(),true)
                ||!getEndPort().equals(otherPath.getEndPort(),true))
            return false;
        if(!PortDirection.isPairDirection(getStartPort(),otherPath.getStartPort())
                ||!PortDirection.isPairDirection(getEndPort(),otherPath.getEndPort()))
            return false;
        //看看中间节点是否一致
        List<Port> thisConnectNodes=getSwitchOrODFPortNodes();
        List<Port> otherConnectNodes=otherPath.getSwitchOrODFPortNodes();
        if(thisConnectNodes.size()!=otherConnectNodes.size())
            return false;
        boolean isPairPortRet=true;
        for(int i=0;i<thisConnectNodes.size();i++){
            Port thisPortItm=thisConnectNodes.get(i);
            Port otherPortItm=otherConnectNodes.get(i);
            //需要针对ODF单独来判断，ODF的配对端口可以是任意端口(只要端口号不一样就可以配对)
            if(thisPortItm.getUnit().equals(otherPortItm.getUnit())) {
                if (UnitClass.ODF.equals(thisPortItm.getUnit().getUnitClass())) {
                    continue;
                } else if (!thisPortItm.equals(otherPortItm, true) ||
                        !PortDirection.isPairDirection(thisPortItm, otherPortItm)) {
                    isPairPortRet = false;
                    break;
                }
            }
        }
        return isPairPortRet;
    }

    /**
     *
     * @param port 起始的端口
     * @param bCheckTxRxPair true 去掉路径中相邻的RX,TX不配对的路径 false 找到所有的路径
     * @return 找到的路径列表
     */
    public static List<PortPhyPath> getPortPhyPaths(Port port,boolean bCheckTxRxPair)
    {
        List<PortPhyPath> ret=new ArrayList<>();
        if(port==null)
            return ret;
        if(!port.hasConnected())
            return ret;
        //从Port的所有连接中找下一个节点
        for(ConnectionCoreBase coreBaseItm:port.getPortConnections()){
            if((coreBaseItm.getPortBObject()!=null)&&port.equals(coreBaseItm.getPortAObject()))
            {
                PortPhyPath newPortPath=new PortPhyPath(port);
                ret.addAll(newPortPath.findPortPhyPath(newPortPath, coreBaseItm.getPortBObject()));
            }else if((coreBaseItm.getPortAObject()!=null)&&port.equals(coreBaseItm.getPortBObject()))
            {
                PortPhyPath newPortPath=new PortPhyPath(port);
                ret.addAll(newPortPath.findPortPhyPath(newPortPath, coreBaseItm.getPortAObject()));
            }
        }
        if(!bCheckTxRxPair)
            return ret;
        //需要去掉配对错误的路径
        return  ret.stream()
                .filter(PortPhyPath::isRxTxPair)
                .collect(Collectors.toList());
    }

    /**
     * 检查路径向相邻的两个Port是否是RxTx配对错误
     * @return true 配对没有错误，false 配对错误
     */
    private boolean isRxTxPair(){
        Port currentPort=startPort;
        if(switchOrODFPortNodes!=null){
            for(Port portItm:switchOrODFPortNodes){
                if(!PortDirection.isPairDirection(currentPort,portItm))
                    return false;
                currentPort=portItm;
            }
        }
        return PortDirection.isPairDirection(currentPort,endPort);
    }

    /**
     * 规则1.nextPort对应的是IED则结束
     *    2.nextPort是ODF找另外一个连接为下一个连接
     *    3.nextPort是switch找其他的端口连接
     * @param path 输入的path
     * @param nextPort 需要加入的下一个Port
     * @return 找到的所有路径
     */
    private List<PortPhyPath> findPortPhyPath(PortPhyPath path,Port nextPort){
        List<PortPhyPath> ret=new LinkedList<>();
        if(nextPort==null)
            return ret;
        switch(nextPort.getUnit().getUnitClass()){
            case IED:
            case OTHER:
                if(path.add(nextPort))
                    ret.add(path);
                break;
            case ODF:
                //找到ODF连接的另外一个Port
                Port pathLastPort=path.getLastConnectedPort();
                Port connectedOdfNextPort=getODFConnectedNextPort(pathLastPort,nextPort);
                if(connectedOdfNextPort==null)
                    return ret;
                if(path.add(nextPort)){
                    //找到下一个Port
                    ret.addAll(findPortPhyPath(path,connectedOdfNextPort));
                }
                break;
            case SWITCH:
                ret.addAll(findSwitchPortPhyPath(path,nextPort));
                break;
        }
        return ret;
    }

    /**
     * 如果nextPort的类型是交换机的物理路径查找
     * 1.首先判断nextPort是否能加入路径，如果不能则这条路径走不通
     * 2.找出所有有连接的其他端口,加入路径，如果不能则这条路不通
     * 3.找到和交换机其他端口连接的另一个设备的端口，使用findPortPhyPath进行递归
     * @param path 输入的路径
     * @param nextPort 交换机上待加入的Port
     * @return 找到的类路列表
     */
    private List<PortPhyPath>findSwitchPortPhyPath(PortPhyPath path,Port nextPort){
        List<PortPhyPath> ret=new LinkedList<>();
        if(!path.add(nextPort))
            return ret;
        for(Port portItm:getSwitchOtherPort(nextPort)){
            if(!path.add(portItm))
                continue;
            //复制一份路径
            PortPhyPath clonePath=path.clone();
            for(ConnectionCoreBase coreItm:portItm.getPortConnections())
            {
                if(portItm.equals(coreItm.getPortAObject()))
                    ret.addAll(findPortPhyPath(clonePath,coreItm.getPortBObject()));
                else
                    ret.addAll(findPortPhyPath(clonePath,coreItm.getPortAObject()));
            }
        }
        return ret;
    }
    private Port getODFConnectedNextPort(Port oneConnectedPort,Port odfPort){
        for(IntCore intCoreItm:odfPort.getIntCores())
        {
            if(!oneConnectedPort.equals(intCoreItm.getPortBObject())&&!oneConnectedPort.equals(intCoreItm.getPortAObject())){
                if(odfPort.equals(intCoreItm.getPortAObject()))
                    return intCoreItm.getPortBObject();
                else
                    return intCoreItm.getPortAObject();
            }
        }
        for(Core coreItm:odfPort.getCores()){
            if(!oneConnectedPort.equals(coreItm.getPortBObject())&&!oneConnectedPort.equals(coreItm.getPortAObject())){
                if(odfPort.equals(coreItm.getPortAObject()))
                    return coreItm.getPortBObject();
                else
                    return coreItm.getPortAObject();
            }
        }
        return null;
    }
    private List<Port> getSwitchOtherPort(Port switchPort){
        List<Port> ret=new ArrayList<>();
        for(Board boardItm:switchPort.getUnit().getBoards()){
            for(Port portItm:boardItm.getPorts()){
                if(!portItm.hasConnected())//没有对外或者对内连接的忽略
                    continue;
                if(!portItm.equals(switchPort,true)//是同一端口的不同收发的忽略
                   &&PortDirection.isPairDirection(portItm,switchPort))//注意这里应该保持收发配对才进行处理
                    ret.add(portItm);
            }
        }
        return ret;
    }

    @Override
    public String toString() {
        return startPort +"<-->" + endPort +"("+getSwitchOrODFPortNodes().size()+")";
    }
}
