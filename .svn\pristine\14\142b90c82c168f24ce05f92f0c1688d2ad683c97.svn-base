package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.gl;

import android.graphics.RectF;

import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Config;

import java.util.List;

public class CubicleOdfWLBean {
    RectF rectF;//左侧屏柜框
    String cubicleName;//左侧屏柜名称
    RectF cubicleNameRectF;//左侧屏柜区域
    List<OdfRectFBean> odfRectFs;//左侧odf小框
    List<OdfConnectRectFBean> odfConnectRectF;//左侧连接odf小框的框

    RectF rectFRight;//右侧屏柜框
    String cubicleNameRight;//右侧屏柜名称
    RectF cubicleNameRectFRight;//右侧屏柜区域
    List<OdfRectFBeanRight> odfRectFsRight;//左侧odf小框
    List<OdfConnectRectFBeanRight> odfConnectRectFRight;//左侧连接odf小框的框

    String centerName;//中间连接线名字
    RectF centerRectF;//中间连接线

    public String getCenterName() {
        return centerName;
    }

    public void setCenterName(String centerName) {
        this.centerName = centerName;
    }

    public RectF getCenterRectF() {
        return centerRectF;
    }

    public void setCenterRectF(RectF centerRectF) {
        this.centerRectF = centerRectF;
    }

    public RectF getRectFRight() {
        return rectFRight;
    }

    public void setRectFRight(RectF rectFRight) {
        this.rectFRight = rectFRight;
        cubicleNameRectFRight = new RectF(rectFRight.left + Config.GL_ODF_WIDTH_WL,rectFRight.top,rectFRight.right,rectFRight.bottom);
    }

    public String getCubicleNameRight() {
        return cubicleNameRight;
    }

    public void setCubicleNameRight(String cubicleNameRight) {
        this.cubicleNameRight = cubicleNameRight;
    }

    public RectF getCubicleNameRectFRight() {
        return cubicleNameRectFRight;
    }

    public void setCubicleNameRectFRight(RectF cubicleNameRectFRight) {
        this.cubicleNameRectFRight = cubicleNameRectFRight;
    }

    public List<OdfRectFBeanRight> getOdfRectFsRight() {
        return odfRectFsRight;
    }

    public void setOdfRectFsRight(List<OdfRectFBeanRight> odfRectFsRight) {
        this.odfRectFsRight = odfRectFsRight;
        if (odfRectFsRight!=null && odfRectFsRight.size()!=0){
            int count = 0;
            float beforTop = rectFRight.top + Config.wl_param;
            for (OdfRectFBeanRight odfRectFBean:odfRectFsRight){
                float top = beforTop+Config.GL_ODF_HEIGHT * count;
                odfRectFBean.OdfRectFRight = new RectF(rectFRight.left,top,rectFRight.left + Config.GL_ODF_WIDTH_WL,top+Config.GL_ODF_HEIGHT);
                odfRectFBean.unitRectFRight = new RectF(odfRectFBean.OdfRectFRight.right,top,odfRectFBean.OdfRectFRight.right + Config.GL_ODF_WIDTH_WL,top+Config.GL_ODF_HEIGHT);
                count ++;
            }
        }
    }

    public List<OdfConnectRectFBeanRight> getOdfConnectRectFRight() {
        return odfConnectRectFRight;
    }

    public void setOdfConnectRectFRight(List<OdfConnectRectFBeanRight> odfConnectRectFRight) {
        this.odfConnectRectFRight = odfConnectRectFRight;
        if (odfConnectRectFRight!=null && odfConnectRectFRight.size()!=0){
            int count = 0;
            float beforTop = rectFRight.top + Config.wl_param + Config.GL_ODF_HEIGHT/2;
            for (OdfConnectRectFBeanRight odfConnectRectFBeanRight:odfConnectRectFRight){
                float top = beforTop + Config.GL_ODF_HEIGHT * count;
                odfConnectRectFBeanRight.OdfConnetRectFRight = new RectF(rectFRight.left,top,rectFRight.left - Config.GL_ODF_CONNECT_WIDTH,top + Config.GL_ODF_HEIGHT);
                count ++;
            }
        }
    }

    public RectF getRectF() {
        return rectF;
    }

    public void setRectF(RectF rectF) {
        this.rectF = rectF;
        cubicleNameRectF = new RectF(rectF.left,rectF.top,rectF.right- Config.GL_ODF_WIDTH_WL,rectF.bottom);
    }

    public List<OdfRectFBean> getOdfRectFs() {
        return odfRectFs;
    }

    public void setOdfRectFs(List<OdfRectFBean> odfRectFs,float screenWidth) {
        this.odfRectFs = odfRectFs;
        float beforTop = rectF.top + Config.wl_param;
        if (odfRectFs!=null && odfRectFs.size()!=0){
            int count = 0;
            for (OdfRectFBean odfRectFBean:odfRectFs){
                float top = beforTop+Config.GL_ODF_HEIGHT * count;
                odfRectFBean.OdfRectF = new RectF(rectF.right-Config.GL_ODF_WIDTH_WL,top,rectF.right,top+Config.GL_ODF_HEIGHT);
                odfRectFBean.unitRectF = new RectF(odfRectFBean.OdfRectF.left-Config.GL_ODF_WIDTH_WL,top,odfRectFBean.OdfRectF.left,top+Config.GL_ODF_HEIGHT);
                count ++;
            }

            if (odfRectFs.size() == 1){
                //有1个odf端口，没有连接框，中间连接线画的长
                float right = screenWidth - Config.GL_WIDTH;
                centerRectF = new RectF(rectF.right,beforTop + Config.GL_ODF_HEIGHT/2, right,beforTop + Config.GL_ODF_HEIGHT/2);
            }else {
                //有多个odf端口，有连接框，中间连接线画的短
                float top = beforTop + odfRectFs.size() * Config.GL_ODF_HEIGHT/2;
                float right = screenWidth - Config.GL_WIDTH - Config.GL_ODF_CONNECT_WIDTH;
                centerRectF = new RectF(rectF.right + Config.GL_ODF_CONNECT_WIDTH,top, right,top);
            }
        }else {
            //没有odf端口，中间连接线画的长
            float top = beforTop + (rectF.bottom - beforTop)/2;
            float right = screenWidth - Config.GL_WIDTH;
            centerRectF = new RectF(rectF.right,top, right,top);
        }
    }

    public List<OdfConnectRectFBean> getOdfConnectRectF() {
        return odfConnectRectF;
    }

    public void setOdfConnectRectF(List<OdfConnectRectFBean> odfConnectRectF) {
        this.odfConnectRectF = odfConnectRectF;
        if (odfConnectRectF!=null && odfConnectRectF.size()!=0){
            int count = 0;
            float beforTop = rectF.top + Config.wl_param + Config.GL_ODF_HEIGHT/2;
            for (OdfConnectRectFBean odfConnectRectFBean:odfConnectRectF){
                float top = beforTop + Config.GL_ODF_HEIGHT * count;
                odfConnectRectFBean.OdfConnetRectF = new RectF(rectF.right,top,rectF.right + Config.GL_ODF_CONNECT_WIDTH,top + Config.GL_ODF_HEIGHT);
                count ++;
            }
        }
    }

    public String getCubicleName() {
        return cubicleName;
    }

    public void setCubicleName(String cubicleName) {
        this.cubicleName = cubicleName;
    }

    public RectF getCubicleNameRectF() {
        return cubicleNameRectF;
    }

    public static class OdfRectFBean {
        String odfName;

        RectF OdfRectF;

        String unitName;

        RectF unitRectF;

        public String getUnitName() {
            return unitName;
        }

        public void setUnitName(String unitName) {
            this.unitName = unitName;
        }

        public RectF getUnitRectF() {
            return unitRectF;
        }

        public void setUnitRectF(RectF unitRectF) {
            this.unitRectF = unitRectF;
        }

        public String getOdfName() {
            return odfName;
        }

        public void setOdfName(String odfName) {
            this.odfName = odfName;
        }

        public RectF getOdfRectF() {
            return OdfRectF;
        }

        public void setOdfRectF(RectF odfRectF) {
            OdfRectF = odfRectF;
        }
    }

    public static class OdfConnectRectFBean {

        RectF OdfConnetRectF;

        public RectF getOdfConnetRectF() {
            return OdfConnetRectF;
        }

        public void setOdfConnetRectF(RectF odfConnetRectF) {
            OdfConnetRectF = odfConnetRectF;
        }
    }

    public static class OdfRectFBeanRight {
        String odfNameRight;

        RectF OdfRectFRight;

        String unitNameRight;

        RectF unitRectFRight;

        public String getUnitNameRight() {
            return unitNameRight;
        }

        public void setUnitNameRight(String unitNameRight) {
            this.unitNameRight = unitNameRight;
        }

        public RectF getUnitRectFRight() {
            return unitRectFRight;
        }

        public void setUnitRectFRight(RectF unitRectFRight) {
            this.unitRectFRight = unitRectFRight;
        }

        public String getOdfNameRight() {
            return odfNameRight;
        }

        public void setOdfNameRight(String odfNameRight) {
            this.odfNameRight = odfNameRight;
        }

        public RectF getOdfRectFRight() {
            return OdfRectFRight;
        }

        public void setOdfRectFRight(RectF odfRectFRight) {
            OdfRectFRight = odfRectFRight;
        }

    }

    public static class OdfConnectRectFBeanRight {

        RectF OdfConnetRectFRight;

        public RectF getOdfConnetRectFRight() {
            return OdfConnetRectFRight;
        }

        public void setOdfConnetRectFRight(RectF odfConnetRectFRight) {
            OdfConnetRectFRight = odfConnetRectFRight;
        }

    }
}
