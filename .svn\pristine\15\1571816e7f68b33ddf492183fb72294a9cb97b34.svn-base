package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.vir_real_circuit;

import android.graphics.RectF;

public class CtrlBlockBean{
	
	private float l, t, r, b;
	//private float mWidth=150,mHeight=30;
	
	/*
	 * 接收者允许接收很多发送者发的信息。
	 * 发送者可以有多个接收者
	 * */
	private boolean isReciver =false;
	private boolean isGoose = false;
	
	private String AppID;//所属的IDE名称/APPID name  如果为空则为"接受块儿"
	private int num;//第几个控制块（IDE中）
	
	public String senderIedName="";
	public String senderAppId="";
	public String receiverIedName="";
	
	
	//为控制块添加相关的信息  方便拾取的时候调用
	public CtrlBlockBean setAttachedInfo(String senderIedName, String senderAppId, String receiverIedName){
    	this.senderIedName = senderIedName;
    	this.senderAppId = senderAppId;
    	this.receiverIedName = receiverIedName;
    	return this;
    }    
	
	public CtrlBlockBean setRect(float l, float t, float r, float b){
		this.l = l;
		this.t = t;
		this.r = r;
		this.b = b;
		return this;
	}
	public CtrlBlockBean setRect(RectF rectF){
		this.l = rectF.left;
		this.t = rectF.top;
		this.r = rectF.right;
		this.b = rectF.bottom;
		return this;
	}
	
	public boolean isReciver() {
		return isReciver;
	}

	public CtrlBlockBean setReciver(boolean isReciver) {
		this.isReciver = isReciver;
		return this;
	}

	public boolean isGoose() {
		return isGoose;
	}

	public CtrlBlockBean setGoose(boolean isGoose) {
		this.isGoose = isGoose;
		return this;
	}

	public String getName() {
		if(null != this.AppID && this.isReciver){
			System.out.println("wtf");
			return null;
		}
		StringBuilder bulider = new StringBuilder();
		if(!this.isReciver){
			if(!this.isGoose){
				bulider.append("SV 0x");
			}else{
				bulider.append("GOOSE 0x");
			}
			bulider.append(AppID);
		}else{
			bulider.append("\u63a5\u6536");// "接收" \u63a5\u6536  \u000d\u000a
		}
		return bulider.toString();
	}
	public String getAppId(){
		return this.AppID;
	}

	public CtrlBlockBean setAppID(String AppID) {
		this.AppID = AppID;
		return this;
	}

	public int getNum() {
		return num;
	}

	public CtrlBlockBean setNum(int num) {
		this.num = num;
		return this;
	}



	public RectF getRectF(){
		return new RectF(l,t,r,b);
	}
	
	/**
	 * 判断坐标点是否在矩形上
	 * @param x
	 * @param y
	 * @return
	 */
	public boolean isOnTouch(float x, float y, float totalOffX, float totalOffY, float mSclTot){
		x -= totalOffX;
		y -= totalOffY;
		/*if(x>=getRectF().centerX()-getRectF().width()*mSclTot && x<=getRectF().centerX()+getRectF().width()*mSclTot 
				&& y>=getRectF().centerY()-getRectF().height()*mSclTot &&y<=getRectF().centerY()+getRectF().height()*mSclTot)
		    return true;
		else {
			return false;
		}*/
		if(x>=getRectF().left*mSclTot && x<=getRectF().right*mSclTot 
				&& y>=getRectF().top*mSclTot &&y<=getRectF().bottom*mSclTot)
		    return true;
		else {
			return false;
		}
	}
	
	/**
	 * 获取左边和右边的中点坐标
	 * */
	public float getLeft_Middle_X(){
		return this.l;
	}
	public float getLeft_Middle_Y(){
		return (this.t + this.b)/2;
	}
	public float getRight_Middle_X(){
		return this.r;
	}
	public float getRight_Middle_Y(){
		return (this.t + this.b)/2;
	}

	
	
	
	

}
