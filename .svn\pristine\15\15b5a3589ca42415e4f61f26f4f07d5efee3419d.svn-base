package com.kemov.visual.spcd.spcdvisualandroidapp.bean;

import android.graphics.RectF;

import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class OdfViewDataNewBean implements Serializable {

    private static OdfViewDataNewBean instance;

    public synchronized static OdfViewDataNewBean getInstance() {
        if (instance == null) {
            instance = new OdfViewDataNewBean();
        }
        return instance;
    }

    List<KM_SPCD_PORT> ports;
    List<CableBean> cableBeans;//左侧连接线数据
    List<CableBean> cableRightBeans;//右侧连接线数据

    public List<CableBean> getCableRightBeans() {
        return cableRightBeans;
    }

    public void setCableRightBeans(List<CableBean> cableRightBeans) {
        this.cableRightBeans = cableRightBeans;
    }

    public List<CableBean> getCableBeans() {
        return cableBeans;
    }

    public void setCableBeans(List<CableBean> cableBeans) {
        this.cableBeans = cableBeans;
    }

    public List<KM_SPCD_PORT> getPorts() {
        return ports;
    }

    public void setPorts(List<KM_SPCD_PORT> ports) {
        this.ports = ports;
    }


    public static class CableBean implements Serializable{
        KM_SPCD_CABLE km_spcd_cable;
        List<Integer> cableCoreIds;
        int lineColor;

        KM_SPCD_INTCORE km_spcd_intcore;//连接的是光纤
        int type;//1是光纤

        //新增该端口连接（连进或者连出的装置端口）
        Map<Integer,OdfViewUnitPortBean> map;

        //用来做点击判断
        public RectF rectF;

        public boolean isOnTouch(float x, float y, float totalOffX, float totalOffY, float mSclTot){
            if (rectF == null){
                return false;
            }
            x -= totalOffX;
            y -= totalOffY;
            if(x>=rectF.left*mSclTot && x<=rectF.right*mSclTot
                    && y>=rectF.top*mSclTot && y<= rectF.bottom*mSclTot)
                return true;
            else {
                return false;
            }
        }

        public RectF getRectF() {
            return rectF;
        }

        public void setRectF(RectF rectF) {
            this.rectF = rectF;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public KM_SPCD_INTCORE getKm_spcd_intcore() {
            return km_spcd_intcore;
        }

        public void setKm_spcd_intcore(KM_SPCD_INTCORE km_spcd_intcore) {
            this.km_spcd_intcore = km_spcd_intcore;
        }

        public Map<Integer, OdfViewUnitPortBean> getMap() {
            return map;
        }

        public void setMap(Map<Integer, OdfViewUnitPortBean> map) {
            this.map = map;
        }

        public int getLineColor() {
            return lineColor;
        }

        public void setLineColor(int lineColor) {
            this.lineColor = lineColor;
        }

        public List<Integer> getCableCoreIds() {
            return cableCoreIds;
        }

        public void setCableCoreIds(List<Integer> cableCoreIds) {
            this.cableCoreIds = cableCoreIds;
        }

        public KM_SPCD_CABLE getKm_spcd_cable() {
            return km_spcd_cable;
        }

        public void setKm_spcd_cable(KM_SPCD_CABLE km_spcd_cable) {
            this.km_spcd_cable = km_spcd_cable;
        }
    }

    public static void clear(){
        if (instance != null) {
            instance = null;
        }
    }

    public void clearData(){
        if (ports!=null) ports.clear();
        if (cableBeans!=null) cableBeans.clear();
        if (cableRightBeans!=null) cableRightBeans.clear();
    }
}
