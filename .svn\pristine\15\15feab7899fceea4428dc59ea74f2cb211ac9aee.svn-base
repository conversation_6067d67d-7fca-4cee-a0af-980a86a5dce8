package com.kemov.visual.spcd.spcdvisualandroidapp.parse;

import com.google.gson.Gson;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_REGION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.dev_doc_bean.TdSubstation;
import com.kemov.visual.spcd.spcdvisualandroidapp.dev_doc_bean.TdUnit;
import com.kemov.visual.spcd.spcdvisualandroidapp.dev_doc_bean.TdUnits;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *功能：解析 设备图档
 *Created by RuanJian-GuoYong on 2020/1/15 9:03
 */
public class ParseDeviceDocsXML {
    //解析器
    private XmlPullParser parser = null;

    String name="";
    private int event =  0;

    //进度
    private float fProgress = 0.0f;
    private float progressStep = 1.0f;
    private boolean bOver = false;

    //解析结果
    private int nParserResultId = -1;

    public List<TdSubstation> substationList = new ArrayList<>();
    public List<TdUnits> unitsList = new ArrayList<>();
    public List<TdUnit> unitList = new ArrayList<>();


    private int rejustID = 1;

    private Map<String,Integer> tableMaxIds;
    public ParseDeviceDocsXML(Map<String,Integer> tableMaxIds) {
    	this.tableMaxIds = tableMaxIds;
    }

    public void clear(){
    	substationList.clear();
        unitList.clear();
    }

    private void readNext(){
        try {
            event = parser.next();
        } catch (XmlPullParserException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    private String readElementText(){
        String sRet = parser.getText();
        return sRet;
    }

    private String nextText(){
        readNext();
        return parser.getText();
    }
    private String name(){
        name = parser.getName();
        return name;
    }


    private boolean isStartElement(){
        if(event== XmlPullParser.START_TAG){
            return true;
        }
        return false;
    }
    private boolean isEndElement(){
        if(event== XmlPullParser.END_TAG){
            return true;
        }
        return false;
    }

    private boolean atEnd(){
        if(event== XmlPullParser.END_DOCUMENT){
            return true;
        }
        return false;
    }

    public void start(InputStream inputStream)throws Exception {
        //获得工程解析器
        XmlPullParserFactory pullParserFactory = XmlPullParserFactory.newInstance();
        //获得实例
        parser = pullParserFactory.newPullParser();
        //设置需要解析的xml数据
        parser.setInput(inputStream, "UTF-8");

        event = parser.getEventType();

        while(event!=XmlPullParser.START_DOCUMENT){
            event  = parser.next();
        }

        readSCL();
        bOver = true;
        printResult();
    }
    
    private void printResult() {
    	Gson gson = new Gson();
    	
    	System.out.println("substationList:" + "["+substationList.size()+"]" + gson.toJson(substationList));
    	System.out.println("unitList:" + "["+unitList.size()+"]" + gson.toJson(unitList));
	}

    public void readSCL() {
//        logger.info("Read SCL Start......");
        System.out.println("解析SCL根节点开始......");
        while(!atEnd())
        {
            readNext();
            if(isStartElement())
            {
                if("Substation".equals(name()))
                {
                    readSubstation();
                }
            }

            if(isEndElement())
            {
                if("SCL".equals(name()))
                {
//                    logger.info("Read SLCL End......");
                    System.out.println("解析SCL跟节点结束");
                    break;
                }
            }
        }
    }

    public void readSubstation() {
        TdSubstation  sub = new TdSubstation ();
        sub.setId(0);
        //sub.setId(substationList.size()+tableMaxIds.get("Substation"));
        sub.setName(getAttributeValue("name"));
        sub.setDescription(getAttributeValue("desc"));
        substationList.add(sub);
//        System.out.println("解析Substation : "+"id = "+sub.getId()+"  name = "+sub.getName()+"   开始");
//        logger.info("Read Substation Start......");
        System.out.println(sub);
        while(!atEnd())
        {
            readNext();
            if(isStartElement())
            {
                if("Units".equals(name()))
                {
                    readUnits(sub.getId());
                }
            }

            if(isEndElement())
            {
                if("Substation".equals(name()))
                {
                    break;
                }
            }
        }

    }


    public void readUnits(int substation_id) {
        TdUnits tdUnits = new TdUnits ();
        tdUnits.setId(0);
        unitsList.add(tdUnits);
        System.out.println(tdUnits);
        while(!atEnd())
        {
            readNext();
            if(isStartElement())
            {
                if("Unit".equals(name()))
                {
                    readUnit(tdUnits.getId());
                }
            }

            if(isEndElement())
            {
                if("Units".equals(name()))
                {
//                    System.out.println("解析Cubicle : "+"id = "+cub.getId()+"  name = "+cub.getName()+"   结束");
//                    logger.info("Read Cubicle +("+cub.getId()+")  End......");
                    break;
                }
            }
        }

    }

    public void readUnit(int unitsId) {
    	TdUnit dev = new TdUnit ();
        dev.setId(0);
        //dev.setId(unitList.size()+tableMaxIds.get("TdUnit"));
        dev.setIedname(getAttributeValue("iedname"));
        dev.setIeddesc(getAttributeValue("ieddesc"));
        dev.setFilepath(getAttributeValue("filepath"));
        unitList.add(dev);
//        logger.info("Read Device +("+dev.getId()+")  Start......");
//        System.out.println("解析Device : "+"id = "+dev.getId()+"  name = "+dev.getName()+"   开始");
        System.out.println(dev);
        while(!atEnd())
        {
            readNext();
            if(isEndElement())
            {
                if("Unit".equals(name()))
                {
                    break;
                }
            }
        }
    }

    private String getAttributeValue(String name){

        String sRet = "";
        int index = indexOfAttribute(name);
        if (index >= 0){
            sRet = parser.getAttributeValue(index);
        }

        return  sRet;
    }
    private int getAttributeValueInt(String name){

        String str = getAttributeValue(name);

        int nRet = 0;
        try {
            nRet = Integer.parseInt(str);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }

        return  nRet;
    }
    private float getAttributeValueFloat(String name){

        String str = getAttributeValue(name);

        float nRet = 0f;
        try {
            nRet = Float.parseFloat(str);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }

        return  nRet;
    }
    private boolean getAttributeValueBoolean(String name){

        String str = getAttributeValue(name);

        if (str != null && !str.isEmpty()){

            if (str.equals("1")){
                return true;
            }
        }
        return  false;
    }

    private int indexOfAttribute(String arg0){

        int k = parser.getAttributeCount();
        for(int i= 0;i < k;i++){
            if(parser.getAttributeName(i).equals(arg0)){
                return i;
            }
        }
        return -1;

    }

    private boolean hasAttribute(String arg0){

        int k = parser.getAttributeCount();
        for(int i= 0;i < k;i++){
            if(parser.getAttributeName(i).equals(arg0)){
                return true;
            }
        }
        return false;

    }

    public boolean equalsName(String arg0){

        if(arg0.equals(name())){
            return true;
        }

        return false;
    }
}
