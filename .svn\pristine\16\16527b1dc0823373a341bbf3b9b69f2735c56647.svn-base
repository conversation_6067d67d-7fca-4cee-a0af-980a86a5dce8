package com.kemov.visual.spcd.spcdvisualandroidapp.bean;

import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;

public class OdfViewUnitPortBean {

    private String unitName;
    private String port;
    private KM_SPCD_UNIT km_spcd_unit;
    private KM_SPCD_PORT km_spcd_port;//图中端口的对象
    private KM_SPCD_PORT km_spcd_port1;//图中端口连接的对象

    private Integer type;//1是光缆  尾缆；2是光纤

    private KM_SPCD_CABLE km_spcd_cable;//1是光缆  尾缆
    private KM_SPCD_CUBICLE km_spcd_cubicle;

    private KM_SPCD_INTCORE intcore;//2是光纤

    //该端口连出或者连进的信息
    private OtherConnectUnit otherConnectUnit;

    //改造
    private Integer portIdLeftAndRight;

    public Integer getPortIdLeftAndRight() {
        return portIdLeftAndRight;
    }

    public void setPortIdLeftAndRight(Integer portIdLeftAndRight) {
        this.portIdLeftAndRight = portIdLeftAndRight;
    }

    public Integer getType() {
        return type;
    }

    public KM_SPCD_PORT getKm_spcd_port1() {
        return km_spcd_port1;
    }

    public void setKm_spcd_port1(KM_SPCD_PORT km_spcd_port1) {
        this.km_spcd_port1 = km_spcd_port1;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public KM_SPCD_CABLE getKm_spcd_cable() {
        return km_spcd_cable;
    }

    public void setKm_spcd_cable(KM_SPCD_CABLE km_spcd_cable) {
        this.km_spcd_cable = km_spcd_cable;
    }

    public KM_SPCD_CUBICLE getKm_spcd_cubicle() {
        return km_spcd_cubicle;
    }

    public void setKm_spcd_cubicle(KM_SPCD_CUBICLE km_spcd_cubicle) {
        this.km_spcd_cubicle = km_spcd_cubicle;
    }

    public KM_SPCD_INTCORE getIntcore() {
        return intcore;
    }

    public void setIntcore(KM_SPCD_INTCORE intcore) {
        this.intcore = intcore;
    }

    public KM_SPCD_UNIT getKm_spcd_unit() {
        return km_spcd_unit;
    }

    public void setKm_spcd_unit(KM_SPCD_UNIT km_spcd_unit) {
        this.km_spcd_unit = km_spcd_unit;
    }

    public KM_SPCD_PORT getKm_spcd_port() {
        return km_spcd_port;
    }

    public void setKm_spcd_port(KM_SPCD_PORT km_spcd_port) {
        this.km_spcd_port = km_spcd_port;
    }

    public OtherConnectUnit getOtherConnectUnit() {
        return otherConnectUnit;
    }

    public void setOtherConnectUnit(OtherConnectUnit otherConnectUnit) {
        this.otherConnectUnit = otherConnectUnit;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public static class OtherConnectUnit {
        private Integer type;//1是光缆  尾缆；2是光纤
        private String unitName;
        private String port;

        private KM_SPCD_CABLE km_spcd_cable;//1是光缆  尾缆
        private KM_SPCD_CUBICLE km_spcd_cubicle;

        private KM_SPCD_UNIT km_spcd_unit;
        private KM_SPCD_PORT km_spcd_port;

        private KM_SPCD_INTCORE intcore;//2是光纤

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getUnitName() {
            return unitName;
        }

        public void setUnitName(String unitName) {
            this.unitName = unitName;
        }

        public String getPort() {
            return port;
        }

        public void setPort(String port) {
            this.port = port;
        }

        public KM_SPCD_CABLE getKm_spcd_cable() {
            return km_spcd_cable;
        }

        public void setKm_spcd_cable(KM_SPCD_CABLE km_spcd_cable) {
            this.km_spcd_cable = km_spcd_cable;
        }

        public KM_SPCD_CUBICLE getKm_spcd_cubicle() {
            return km_spcd_cubicle;
        }

        public void setKm_spcd_cubicle(KM_SPCD_CUBICLE km_spcd_cubicle) {
            this.km_spcd_cubicle = km_spcd_cubicle;
        }

        public KM_SPCD_UNIT getKm_spcd_unit() {
            return km_spcd_unit;
        }

        public void setKm_spcd_unit(KM_SPCD_UNIT km_spcd_unit) {
            this.km_spcd_unit = km_spcd_unit;
        }

        public KM_SPCD_PORT getKm_spcd_port() {
            return km_spcd_port;
        }

        public void setKm_spcd_port(KM_SPCD_PORT km_spcd_port) {
            this.km_spcd_port = km_spcd_port;
        }

        public KM_SPCD_INTCORE getIntcore() {
            return intcore;
        }

        public void setIntcore(KM_SPCD_INTCORE intcore) {
            this.intcore = intcore;
        }

    }
}
