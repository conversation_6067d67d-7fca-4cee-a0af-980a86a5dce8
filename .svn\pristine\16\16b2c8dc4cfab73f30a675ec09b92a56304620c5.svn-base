<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.kemov.visual.spcd.spcdvisualandroidapp.activity.TestActivity">

    <Button
        android:id="@+id/glView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        android:text="光缆连接图"/>
    <Button
        android:id="@+id/wlView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/glView"
        android:layout_marginTop="10dp"
        android:text="尾缆连接图"/>
    <Button
        android:id="@+id/odfView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/wlView"
        android:layout_marginTop="10dp"
        android:text="odf视图"/>

    <Button
        android:id="@+id/odfLogicView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/odfView"
        android:layout_marginTop="10dp"
        android:text="odf逻辑视图"/>
    <Button
        android:id="@+id/xsView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/odfLogicView"
        android:layout_marginTop="10dp"
        android:text="虚实回路图"/>

    <Button
        android:id="@+id/hzView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/xsView"
        android:layout_marginTop="10dp"
        android:text="ODF视图汇总"/>
    <Button
        android:id="@+id/qhlView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/hzView"
        android:layout_marginTop="10dp"
        android:text="全回路数据测试"/>

</android.support.constraint.ConstraintLayout>
