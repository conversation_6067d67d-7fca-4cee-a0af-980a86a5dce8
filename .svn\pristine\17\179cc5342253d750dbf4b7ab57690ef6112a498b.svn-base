package com.kemov.visual.spcd.spcdvisualandroidapp.activity;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentTransaction;
import android.support.v7.widget.Toolbar;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;

import com.baseres.base.PubApp;
import com.kemov.visual.spcd.spcdvisualandroidapp.R;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.fragment.OdfLogicFragment;
import com.kemov.visual.spcd.spcdvisualandroidapp.fragment.OdfViewTierFragment;
import com.kemov.visual.spcd.spcdvisualandroidapp.fragment.OdfViewTierNewFragment;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Utils;
import com.kemov.visual.spcd.spcdvisualandroidapp.visualview.CubicleFrontView;

import java.util.ArrayList;

import static android.view.MenuItem.SHOW_AS_ACTION_ALWAYS;

public class OdfNewActivity extends BaseActivity implements View.OnClickListener{
    Toolbar toolbar;
    private String dbName;
    private  final int VIRTUAL_REAL_CIRCUIT_MODEL = 1;
    int n_model = VIRTUAL_REAL_CIRCUIT_MODEL;
    String[] toolbarTtitle = new String[]{"odf视图", "odf逻辑视图"/*,"光纤可视化"*/};//odf没有光纤可视化 mdf20190924

    private final int IED_MODEL = 100;
    private final int INTCORE_MODEL = 101;
    private final int ERROR_MODEL= 99;
    int ACTIVITY_MODEL = INTCORE_MODEL;
    private final ArrayList<Fragment> listFragment = new ArrayList<Fragment> ();
    private int odfUinitId;
    private BaseDaoImp unitDao;
    KM_SPCD_UNIT km_spcd_unit;
    String ied_name = null;

    private ImageButton left_back;
    private TextView title;
    private  TextView tv_left;
    private  TextView tv_center;
    private  TextView tv_right;
    private BaseDaoImp cubicleDao;
    private int portId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_odf);
        initView();
    }

    @Override
    public void initView() {
        Intent intent = getIntent();
        dbName = intent.getStringExtra("dbName");
        if (dbName == null){
            dbName = PubApp.getApplication().getDbName();
        }
        odfUinitId = intent.getIntExtra("unitId",0);
        portId = intent.getIntExtra("portId",0);

        unitDao = new BaseDaoImp(this.getApplicationContext(), KM_SPCD_UNIT.class, dbName);
        cubicleDao = new BaseDaoImp(this.getApplicationContext(), KM_SPCD_CUBICLE.class, dbName);
        //查询该ied的名称
        km_spcd_unit = (KM_SPCD_UNIT)unitDao.getById(odfUinitId);

        findViewWithId();
        setSupportActionBar(toolbar);
        initFragments();
        if (ACTIVITY_MODEL != ERROR_MODEL){
            switchToFragment( R.id.fragment_layout,listFragment.get(n_model-1));
        }
        if (km_spcd_unit!=null){
            ied_name = km_spcd_unit.getIed_name();
            KM_SPCD_CUBICLE cubicle = (KM_SPCD_CUBICLE)cubicleDao.getListForEq("id", km_spcd_unit.getCubicle_id()).get(0);
            if (cubicle!=null){
//                title.setText(cubicle.getDescription()+"");
                title.setText("ODF视图");
            }else {
                title.setText("ODF视图");
            }
        }
    }

    @Override
    public void findViewWithId() {
        toolbar = findViewById(R.id.toolbar);
        toolbar.setTitle(toolbarTtitle[n_model-1]);
        initToolbar();
    }

    public void initFragments() {
        Bundle bundle = new Bundle();
        bundle.putString("dbName",dbName);
        bundle.putInt("odfUinitId",odfUinitId);
        bundle.putInt("portId",portId);
        for(int i =0;i < toolbarTtitle.length;i++){
            Fragment fm = null;
            if(i == 0){
//                fm = new OdfViewTierFragment();
                fm = new OdfViewTierNewFragment();
            }
            else if(i == 1){
                fm = new OdfLogicFragment();
            }
            if (fm!=null){
                fm.setArguments(bundle);
                listFragment.add(fm);
            }
        }
    }
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        for (int i = 0;i < toolbarTtitle.length;i++) {
            menu.add(1, i+1, 1,toolbarTtitle[i]).setShowAsAction(SHOW_AS_ACTION_ALWAYS);
        }
        return super.onCreateOptionsMenu(menu);
    }
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (ACTIVITY_MODEL == IED_MODEL || ACTIVITY_MODEL == ERROR_MODEL){
            return false;
        }
        toolbar.setTitle(item.getTitle());
        n_model = item.getItemId();
        if (n_model < 3){
            switchToFragment( R.id.fragment_layout,listFragment.get(n_model-1));
        }/*else { //odf没有iedName mdf20190924
            Intent intent = new Intent(this, OpticalFiberVisualActivity.class);
            intent.putExtra("IEDName",ied_name);
            startActivity(intent);
        }*/
        return super.onOptionsItemSelected(item);
    }

    public void switchToFragment(int containerViewId ,Fragment dfragment){
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        ft.replace(containerViewId, dfragment);
        ft.commit();
    }

    private void initToolbar() {
        left_back = (ImageButton)findViewById(R.id.left_back);
        title = (TextView)findViewById(R.id.title);
        tv_left = (TextView)findViewById(R.id.tv_left);
        tv_center = (TextView)findViewById(R.id.tv_center);
        tv_right = (TextView)findViewById(R.id.tv_right);
//        initClick(1,0,1,"ODF逻辑视图","","ODF视图");
        left_back.setOnClickListener(this);
    }

    private void initClick(int one, int two, int three, String st1, String st2, String st3) {
        left_back.setOnClickListener(this);
        if (one == 1){
            tv_left.setVisibility(View.VISIBLE);
            tv_left.setText(st1);
            tv_left.setOnClickListener(this);
            setTvWidth(tv_left,90);
        }
        if (two == 1){
            tv_center.setVisibility(View.VISIBLE);
            tv_center.setText(st2);
            tv_center.setOnClickListener(this);
        }
        if (three == 1){
            tv_right.setVisibility(View.VISIBLE);
            tv_right.setText(st3);
            tv_right.setOnClickListener(this);
            setTvWidth(tv_right,70);
        }
    }

    public void setTvWidth(TextView tv,float fl){
        ViewGroup.LayoutParams layoutParams = tv.getLayoutParams();
        layoutParams.width = Utils.dip2px(this,fl);
        tv.setLayoutParams(layoutParams);
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.left_back){
            finish();
        }else if (id == R.id.tv_left){
            setColor(1);
            switchToFragment( R.id.fragment_layout,listFragment.get(0));
        }else if (id == R.id.tv_center){
            setColor(2);
        }else if (id == R.id.tv_right){
            setColor(3);
            switchToFragment( R.id.fragment_layout,listFragment.get(1));
        }
    }

    private void setColor(int type) {
        tv_left.setBackgroundColor(getResources().getColor(R.color.dodgerblue));
        tv_left.setTextColor(getResources().getColor(R.color.black));
        tv_center.setBackgroundColor(getResources().getColor(R.color.dodgerblue));
        tv_center.setTextColor(getResources().getColor(R.color.black));
        tv_right.setBackgroundColor(getResources().getColor(R.color.dodgerblue));
        tv_right.setTextColor(getResources().getColor(R.color.black));
        if (type == 1){
            setSelfColor(tv_left);
        }else if (type == 2){
            setSelfColor(tv_center);
        }else if (type == 3){
            setSelfColor(tv_right);
        }
    }

    private void setSelfColor(TextView tv) {
        tv.setBackgroundColor(getResources().getColor(R.color.PrimaryDarkColor));
        tv.setTextColor(getResources().getColor(R.color.white));
    }

    private static final int EVENT_TIME_TO_CHANGE_IMAGE = 100;

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if(hasFocus){
            Message message = mHandler.obtainMessage(EVENT_TIME_TO_CHANGE_IMAGE);
            mHandler.sendMessage(message);
        }
    }

    private Handler mHandler = new Handler() {
        public void handleMessage(Message msg) {
            switch(msg.what){
                case EVENT_TIME_TO_CHANGE_IMAGE:
                    CubicleFrontView.closeProgressDialog();
                    break;
            }
        }};

    @Override
    protected void onRestart() {
        super.onRestart();
    }
}
