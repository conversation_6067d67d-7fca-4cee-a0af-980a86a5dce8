<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/title_rel"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@color/colorPrimary"
    android:gravity="center">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal">
            <ImageButton
                android:layout_centerVertical="true"
                android:id="@+id/left_back"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:clickable="true"
                android:padding="10dp"
                android:layout_alignParentLeft="true"
                android:background="@null"
                android:src="@mipmap/pic_left_arrow" />
            <TextView
                android:id="@+id/title_topLevel"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:layout_toRightOf="@+id/left_back"
                android:singleLine="true"
                android:paddingLeft="5dp"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>


        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp"
            android:layout_centerInParent="true"
            android:singleLine="true"
            android:padding="6dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="18sp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/right_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:text=""
                android:visibility="gone"
                android:layout_marginRight="10dp"/>

            <LinearLayout
                android:visibility="gone"
                android:id="@+id/ll_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:layout_marginRight="10dp">

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/pic_more" />
            </LinearLayout>
            <ImageView
                android:id="@+id/edit_circle"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:src="@mipmap/pic_photo"
                android:visibility="gone"
                android:layout_marginRight="10dp"
                />
        </LinearLayout>

</RelativeLayout>