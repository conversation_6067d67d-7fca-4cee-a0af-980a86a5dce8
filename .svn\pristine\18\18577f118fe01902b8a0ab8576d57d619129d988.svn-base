package com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.Parser.model;

import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.ParserUtils;
import org.xmlpull.v1.XmlPullParser;

/***
 * 有 name 和desc属性的继承基类
 */
public abstract class SPCDItemBaseEx extends SPCDItemBase{
    private String name;
    private String desc;
    protected SPCDItemBaseEx(Object parent){
        super(parent);
    }
    public void parser(XmlPullParser parser) throws Exception{
        this.name= ParserUtils.parseAttribute(parser,"name");
        this.desc= ParserUtils.parseAttribute(parser,"desc");
    }
    public String getName() {
        return name==null?"":name;
    }
    public String getDesc() {
        return desc==null?"":desc;
    }
}
