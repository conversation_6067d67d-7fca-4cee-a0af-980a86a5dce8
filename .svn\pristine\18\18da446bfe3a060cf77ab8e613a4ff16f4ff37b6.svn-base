package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.vir_real_circuit;

import android.graphics.Canvas;
import android.graphics.RectF;

/**
 *功能：虚实回路图 右侧IED（本IED的对侧IED）图元
 *Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/9/5 9:13
 */
public class ExtIedBean extends BaseIedBean {
	private String TAG = "ExtIdeBean";

	private float height = 0;
	
	public ExtIedBean() {
	}

	@Override
	void initOthersRectFs() {
		typeRectF = new RectF(rectF.left,rectF.top,rectF.left+VR_CONTANTS.IED_TYPE_WIDTH,rectF.top+VR_CONTANTS.IED_TYPE_HEIGHT);
		iedNameRectF = new RectF(rectF.left+VR_CONTANTS.IED_TYPE_WIDTH,rectF.top,rectF.right,rectF.top+VR_CONTANTS.IED_TYPE_HEIGHT);
		descRectF = new RectF(
				rectF.left+VR_CONTANTS.BOARD_SLOT_WIDTH +VR_CONTANTS.IED_DESC_PADDING,
				rectF.top+VR_CONTANTS.IED_TYPE_HEIGHT,
				rectF.right-VR_CONTANTS.IED_DESC_PADDING,
				rectF.bottom-VR_CONTANTS.IED_DESC_PADDING);
	}

	@Override
	public void initDescRectF() {
		super.descRectF = new RectF(this.l+VR_CONTANTS.BOARD_SLOT_WIDTH,
				this.t+VR_CONTANTS.IED_TYPE_HEIGHT,
				this.r,
				this.t+VR_CONTANTS.IED_TYPE_HEIGHT + getIncludedHeight());
	}
	
	@Override
	public float getIncludedHeight() {
		float height = 0;

		height = 100;
		if(height < VR_CONTANTS.IED_MIN_HEIGHT){
			height = VR_CONTANTS.IED_MIN_HEIGHT;
		}
		return height;
	}

	@Override
	public void draw(Canvas canvas) {
		super.draw(canvas);
	}

	public boolean isOnTouch(float x,float y){
		return this.getDescRectF().contains(x, y);
	}
	/**
	 * 判断坐标点是否在矩形上
	 * @param x
	 * @param y
	 * @return
	 */
	public boolean isOnTouch(float x, float y, float totalOffX, float totalOffY, float mSclTot){
		x -= totalOffX;
		y -= totalOffY;
		if(x>= descRectF.left*mSclTot && x<= descRectF.right*mSclTot
				&& y>= descRectF.top*mSclTot && y<= descRectF.bottom*mSclTot)
		    return true;
		else {
			return false;
		}
	}

	public void setCtrlBlocksNum(int ctrlBlocksNum) {
		float items_height = VR_CONTANTS.ITEM_HEIGHT * ctrlBlocksNum;
		height = VR_CONTANTS.IED_TYPE_HEIGHT + VR_CONTANTS.ITEM_PORT_TOP_PADDING
				+ items_height
				+ VR_CONTANTS.ITEM_PORT_BOTTOM_PADDING;
		if(height < VR_CONTANTS.IED_MIN_HEIGHT){
			height = VR_CONTANTS.IED_MIN_HEIGHT;
		}
	}

	public void initRectF(int index, float mWidth) {
		float l = mWidth;
		rectF = new RectF();
	}
}
