package com.kemov.visual.spcd.spcdvisualandroidapp.activity.ied_whole_circuit;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import com.kemov.visual.spcd.spcdvisualandroidapp.bean.WholeCircuitBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_REGION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_SUBSTATION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;

import java.sql.SQLException;
import java.util.List;

/**
 * 传入尾缆上连接的odf端口，找到中间经过的装置，及目的装置（只找到交换机截止）
 */
public class HandleForWholeCircuitsFixOdf {

    //DAO
    public static BaseDaoImp substationDao;
    public static BaseDaoImp regionDao;
    public static BaseDaoImp cubicleDao;
    public static BaseDaoImp<KM_SPCD_UNIT,Integer> unitDao;
    public static BaseDaoImp<KM_SPCD_BOARD,Integer> boardDao;
    public static BaseDaoImp portDao;
    public static BaseDaoImp intCoreDao;

    public static BaseDaoImp cableDao;
    public static BaseDaoImp coreDao;

    public static WholeCircuitBean getWholeCircuitBean(Context mCtx, String dbName, Integer portId){
        initDao(mCtx,dbName);

        HandleForWholeCircuitsFix2 handleForWholeCircuitsFix3 = new HandleForWholeCircuitsFix2((Activity) mCtx, dbName, portId,3);
        WholeCircuitBean data = handleForWholeCircuitsFix3.wholeCircuitBean1;//wholeCircuitBean0是2侧端口连接数据，wholeCircuitBean1当前侧连接数据，wholeCircuitBean2对侧连接数据
        if (data!=null){
            List<WholeCircuitBean.CubicleWholeBean> cubicleWholeBeans = data.getCubicleWholeBeans();
            if (cubicleWholeBeans!=null && cubicleWholeBeans.size()!=0){
                WholeCircuitBean.CubicleWholeBean cubicleWholeBean1 = cubicleWholeBeans.get(0);
                List<WholeCircuitBean.UnitWholeBean> unitWholeBeans1 = cubicleWholeBean1.getUnitWholeBeans();

                if (unitWholeBeans1!=null && unitWholeBeans1.size()!=0){
                    KM_SPCD_UNIT km_spcd_unit1 = unitWholeBeans1.get(0).getKm_spcd_unit();
                    if (km_spcd_unit1.getDev_class().equals("ODF")){
                        WholeCircuitBean.CubicleWholeBean cubicleWholeBean2 = cubicleWholeBeans.get(cubicleWholeBeans.size() - 1);
                        KM_SPCD_CUBICLE km_spcd_cubicle2 = cubicleWholeBean2.getKm_spcd_cubicle();
                        List<WholeCircuitBean.UnitWholeBean> unitWholeBeans2 = cubicleWholeBean2.getUnitWholeBeans();
                        WholeCircuitBean.UnitWholeBean unitWholeBean2 = unitWholeBeans2.get(unitWholeBeans2.size() - 1);
                        KM_SPCD_UNIT km_spcd_unit2 = unitWholeBean2.getKm_spcd_unit();

                        KM_SPCD_PORT km_spcd_port2 = getPortByPortA(km_spcd_cubicle2, km_spcd_unit2.getName()+"."+unitWholeBean2.getBoard()+"."+unitWholeBean2.getPort()+"-"+unitWholeBean2.getPortADirection());
                        HandleForWholeCircuitsFix2 handleForWholeCircuitsFix = new HandleForWholeCircuitsFix2((Activity) mCtx, dbName,km_spcd_port2.getId() ,3);
                        data = handleForWholeCircuitsFix.wholeCircuitBean1;
                    }
                }
            }
        }
        return data;
    }

    private static void initDao(Context mCtx, String dbName) {
        substationDao = new BaseDaoImp<KM_SPCD_SUBSTATION,Integer>(mCtx, KM_SPCD_SUBSTATION.class, dbName);
        regionDao = new BaseDaoImp<KM_SPCD_REGION,Integer>(mCtx, KM_SPCD_REGION.class, dbName);
        cubicleDao = new BaseDaoImp<KM_SPCD_CUBICLE,Integer>(mCtx,KM_SPCD_CUBICLE.class, dbName);
        unitDao = new BaseDaoImp<KM_SPCD_UNIT,Integer>(mCtx,KM_SPCD_UNIT.class, dbName);
        boardDao = new BaseDaoImp<KM_SPCD_BOARD,Integer>(mCtx,KM_SPCD_BOARD.class, dbName);
        portDao = new BaseDaoImp<KM_SPCD_PORT,Integer>(mCtx,KM_SPCD_PORT.class, dbName);
        intCoreDao = new BaseDaoImp<KM_SPCD_INTCORE,Integer>(mCtx,KM_SPCD_INTCORE.class, dbName);

        cableDao = new BaseDaoImp<KM_SPCD_CABLE,Integer>(mCtx,KM_SPCD_CABLE.class, dbName);
        coreDao = new BaseDaoImp<KM_SPCD_CORE,Integer>(mCtx,KM_SPCD_CORE.class, dbName);
    }

    //通过
    private static KM_SPCD_PORT getPortByPortA(KM_SPCD_CUBICLE cubicle, String port){
        String[] split4 = port.split("\\.");
        String s1 = split4[2];
        KM_SPCD_UNIT km_spcd_unitA = null;
        try {
            km_spcd_unitA = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", cubicle.getId(), "name", split4[0]);
            KM_SPCD_BOARD km_spcd_boardA = (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unitA.getId(), "slot", split4[1]);
            if (!TextUtils.isEmpty(s1) && s1.contains("-")){
                String[] split5 = s1.split("-");
                if (km_spcd_boardA!=null){
                    KM_SPCD_PORT port1 = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", km_spcd_boardA.getId(), "no", split5[0],"direction",split5[1]);
                    return port1;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }
}
