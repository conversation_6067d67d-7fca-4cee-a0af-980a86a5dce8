package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.cubicle_back_panel;

import android.graphics.RectF;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/7/16.
 */
//板卡 高度跟随插件多少来变，宽固定
public class BoardBean {

    private float l,t,r,b;
    float width,height;//TODO:宽度固定，高度可变
    RectF rectF;
    String name;
    String desc;
    RectF nameRectF;
    RectF descRectF;

    List<? extends PlugInUnitBean> plugInUnitBeans;//支持多种插板类型（根据父标签类型ied\switch\odf来决定具体类型）
    List<? extends PlugBaseSvgBean> plugSvgBeanList;

    public RectF getRectF() {
        return rectF;
    }

    public void setRectF(RectF rectF) {
        this.rectF = rectF;
        //init
        this.l=rectF.left;
        this.t=rectF.top;
        this.r=rectF.right;
        this.b=rectF.bottom;

        this.setNameRectF(new RectF(l,t,r,t+CubicleBackPanelConstants.BOARD_NAME1_HEIGHT));
        this.setDescRectF(new RectF(l,t+CubicleBackPanelConstants.BOARD_NAME1_HEIGHT,
                r,t+CubicleBackPanelConstants.BOARD_NAME1_HEIGHT+CubicleBackPanelConstants.BOARD_NAME2_HEIGHT));
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public RectF getNameRectF() {
        return nameRectF;
    }

    public void setNameRectF(RectF nameRectF) {
        this.nameRectF = nameRectF;
    }

    public RectF getDescRectF() {
        return descRectF;
    }

    public void setDescRectF(RectF descRectF) {
        this.descRectF = descRectF;
    }

    public List<? extends PlugInUnitBean> getPlugInUnitBeans() {
        return plugInUnitBeans;
    }

    public void setPlugInUnitBeans(List<? extends PlugInUnitBean> plugInUnitBeans) {
        this.plugInUnitBeans = plugInUnitBeans;
    }

    public List<? extends PlugBaseSvgBean> getPlugSvgBeanList() {
        return plugSvgBeanList;
    }

    public void setPlugSvgBeanList(List<? extends PlugBaseSvgBean> plugSvgBeanList) {
        this.plugSvgBeanList = plugSvgBeanList;
    }
}
