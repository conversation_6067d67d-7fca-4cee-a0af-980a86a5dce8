package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.vir_real_circuit_v2;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.text.Layout;
import android.text.TextPaint;
import android.util.Log;

import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.complexgraphics.CanvasBase;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.CanvasDraw;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.utils.DrawUtils;

import java.util.ArrayList;
import java.util.List;

public class CanvasCBList extends CanvasBase{
	private String TAG = "CanvasList";

	private float left=0, top =0;
	private int row = 0;
	private float rowHeight;
	private float rowWidth;
	private List<String> strList = new ArrayList<String>(){};
	private RectF listRectF = new RectF();


	Paint mPaint = new Paint();
	TextPaint mTxtPaint = new TextPaint();
	TextPaint txtPaintLeft = new TextPaint();
	Paint paintWithEffect = new Paint();

	public CanvasCBList(float left, float top, float rowHeight, float rowWidth,
						List<String> list1) {
		this.rowHeight = rowHeight;
		this.rowWidth = rowWidth;

		//canvas.save();
		//canvas.translate(0, -rowHeight/2);
		strList = list1;

		this.row = list1.size();
		
		this.left = left;
		this.top = top;
        initPaint();
		//initDraw();
		//canvas.restore();
	}

	public CanvasCBList setCanvas(Canvas canvas){
        this.canvas = canvas;
        return this;
    }
	
	protected void initDraw() {
		//initPaint();
		//drawRect();
		//drawAllRectAndString();
		drawAllRectAndString2();
	}

	private void initPaint(){
		mPaint.setColor(Color.RED);
		mPaint.setStyle(Paint.Style.STROKE);
		
		mTxtPaint.setColor(Color.BLACK);
		mTxtPaint.setTextSize(24);
		mTxtPaint.setAntiAlias(true);
		mTxtPaint.setTextAlign(Paint.Align.CENTER);
		
		txtPaintLeft.setColor(Color.BLACK);
		txtPaintLeft.setTextSize(24);
		txtPaintLeft.setAntiAlias(true);
		txtPaintLeft.setTextAlign(Paint.Align. LEFT);
		
		paintWithEffect.setStyle ( Paint.Style.STROKE ) ;
		paintWithEffect.setStrokeWidth(2);
		//设置虚线效果
		paintWithEffect.setPathEffect ( new DashPathEffect ( new float [ ] { 6, 8 }, 0 ) ) ;
		
	}
	
	/***
	 * first define the Whole Rect
	 * @return
	 */
	public void drawRect(){
		RectF rect = new RectF(this.left, this.top, this.left + this.rowWidth, this.top +this.row*this.rowHeight);
		this.canvas.drawRect(rect, mPaint);
	}
	
	public RectF getRowRect(int index){
		RectF rect = new RectF(this.left, this.top + index * this.rowHeight, this.left + this.rowWidth, this.top + (index+1) * this.rowHeight);
		return rect;
	}
	public void drawRowRect(int index){
		int orgPaintColor=mPaint.getColor();
		Paint.Style orgPaintStyle=mPaint.getStyle();
		if((strList!=null)&&(strList.size()>index)) {
			if (strList.get(index).contains("SV")) {
				mPaint.setColor(VR_CONTANTS_V2.COLOR_ARROW_SV);
			} else {
				mPaint.setColor(VR_CONTANTS_V2.COLOR_ARROW_GOOSE);
			}
			mPaint.setStyle(Paint.Style.FILL);
		}
		canvas.drawRect(getRowRect(index), mPaint);
		mPaint.setColor(orgPaintColor);
		mPaint.setStyle(orgPaintStyle);
		//Log.e(TAG, "Rect"+index + getRowRect(index).toShortString());
	}
	
	//绘制单列文本
	private void drawAllRectAndString(){
		for (int i = 0; i < row; i++) {
			drawRowRect(i);
			drawRowString(i);
		}
	}
	//绘制两列文本 use this!!!
	public void drawAllRectAndString2(){
		for (int i = 0; i < row; i++) {
			drawRowRect(i);
			drawRowString2(i);
		}
		//绘制中间的交换机点击区域
		//canvas.drawPath(getSwitchPath(), paintWithEffect);
	}
	
	//绘制一列文本
	private void drawRowString(int index) {
		//Utils.textCenter(strList.get(index), mTxtPaint, canvas, getRowRect(index), Layout.Alignment.ALIGN_NORMAL, 1.3f, 0, false);
		mTxtPaint.setTextAlign(Paint.Align.LEFT);
		canvas.drawText(strList.get(index), getRowRect(index).left, getRowRect(index).bottom, mTxtPaint);
		//canvas.drawtext
		mTxtPaint.setTextAlign(Paint.Align.CENTER);
	}
	
	//绘制两列文本
	private void drawRowString2(int index) {
		//DrawUtils.textLeft(strList.get(index), txtPaintLeft, canvas, getRowRectLeft(index), Layout.Alignment.ALIGN_NORMAL, 1.3f, 0, false, 300);
        //DrawUtils.textCenter(" - - ", mTxtPaint, canvas, getRowRect(index), Layout.Alignment.ALIGN_NORMAL, 1.3f, 0, false);
        DrawUtils.textCenter(strList.get(index), mTxtPaint, canvas, getRowRect(index), Layout.Alignment.ALIGN_NORMAL, 1.3f, 0, false);
        //DrawUtils.textLeft(strList2.get(index), txtPaintLeft, canvas, getRowRectRight(index), Layout.Alignment.ALIGN_NORMAL, 1.3f, 0, false,330);
		
	}
	public RectF getRowRectLeft(int index){
		RectF rect = new RectF(this.left + 15, this.top + index * this.rowHeight, this.left + this.rowWidth/2-50, this.top + (index+1) * this.rowHeight);
		return rect;
	}
	public RectF getRowRectRight(int index){
		RectF rect = new RectF(this.left + rowWidth/2 + 50, this.top + index * this.rowHeight, this.left + this.rowWidth, this.top + (index+1) * this.rowHeight);
		return rect;
	}
	
	
	
	
	
	
	////
	public boolean isPointIn(int x, int y){
		if (x < this.left || x>(this.left + rowWidth) || y < this.top || y > this.top + rowHeight * row) {
			return false;
		}else {
			return true;//
		}
	}
	public RectF getListRect(){
		return new RectF(this.left, this.top, this.left + this.rowWidth, this.top +this.row*this.rowHeight);
	}
	
	public RectF getSwitchRect(){
		if (row ==1) {
			return new RectF(getRowRect(0).centerX()-40,getRowRect(0).top -10,getRowRect(row-1).centerX()+40,getRowRect(row-1).bottom+10);
		}else {
			return new RectF(getRowRect(0).centerX()-40,getRowRect(0).top -10,getRowRect(row-1).centerX()+40,getRowRect(row-1).bottom+10);
		}
	}
	
	private Path getSwitchPath(){
		Path path = new Path();
		path.moveTo(getSwitchRect().left, getSwitchRect().top);
		path.addRect(getSwitchRect(), Path.Direction.CW);
		return  path;
	}
	
	public boolean isOnTouchSwitchRect(float x, float y, float totalOffX, float totalOffY, float mScale){
		x -= totalOffX;
		y -= totalOffY;
		if(x>=getSwitchRect().left*mScale && x<=getSwitchRect().right*mScale 
				&& y>=getSwitchRect().top*mScale &&y<=getSwitchRect().bottom*mScale)
		    return true;
		else {
			return false;
		}
	}
	

}
