package com.kemov.visual.spcd.spcdvisualandroidapp.CommonAdapterHelper;

import android.app.Activity;
import android.support.annotation.NonNull;
import android.support.v7.widget.RecyclerView;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import com.kemov.visual.spcd.spcdvisualandroidapp.R;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CORE;

import java.util.List;

public class GqAdapter extends RecyclerView.Adapter<GqAdapter.ViewHolder> {

    private Activity mActivity;
    private List<KM_SPCD_CORE> mList;

    public GqAdapter(Activity activity, List<KM_SPCD_CORE> list){
        this.mActivity = activity;
        this.mList = list;
    }

    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mActivity).inflate(R.layout.list_item_cubicle_fill,parent,false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder,int position) {
        final KM_SPCD_CORE km_spcd_core = mList.get(position);
        holder.num.setText(position+1+"");
        holder.name.setText("");
        holder.desc.setText(km_spcd_core.getPort_a()+"--"+km_spcd_core.getPort_b());
        holder.detail_btn.setVisibility(View.VISIBLE);
        holder.desc.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mOnItemClick != null){
                    mOnItemClick.OnItem(km_spcd_core);
                }
            }
        });
        holder.detail_btn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mOnArrowClick != null){
                    mOnArrowClick.OnArrow(km_spcd_core);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mList == null?0:mList.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        TextView name;
        TextView num;
        TextView desc;
        Button detail_btn;

        public ViewHolder(View itemView) {
            super(itemView);
            num = (TextView) itemView.findViewById(R.id.num);
            name = (TextView) itemView.findViewById(R.id.name);
            desc = (TextView) itemView.findViewById(R.id.desc);
            detail_btn = (Button) itemView.findViewById(R.id.detail_btn);
        }
    }

    public OnItemClick mOnItemClick;

    public interface OnItemClick {
        void OnItem(KM_SPCD_CORE km_spcd_core);
    }

    public void setOnItemClick(OnItemClick onItemClick) {
        this.mOnItemClick = onItemClick;
    }

    public OnArrowClick mOnArrowClick;

    public interface OnArrowClick {
        void OnArrow(KM_SPCD_CORE km_spcd_core);
    }

    public void setOnArrowClick(OnArrowClick onArrowClick) {
        this.mOnArrowClick = onArrowClick;
    }
}
