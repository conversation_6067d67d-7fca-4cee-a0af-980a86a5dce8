package com.kemov.visual.spcd.spcdvisualandroidapp.database.beans;

import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;

import java.io.Serializable;
import java.util.List;

@DatabaseTable(tableName = "KM_SPCD_BOARD")
public class KM_SPCD_BOARD implements Serializable {

    public List<KM_SPCD_PORT> ports = null;

    @DatabaseField(generatedId = true)
    private Integer id;

    @DatabaseField(columnName = "description")
    private String description;

    @DatabaseField(columnName = "type")
    private String type;

    @DatabaseField(columnName = "slot",index = true)
    private String slot;

    @DatabaseField(columnName = "unit_id",index = true)
    private Integer unit_id;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public String getSlot() {
        return slot;
    }

    public void setSlot(String slot) {
        this.slot = slot == null ? null : slot.trim();
    }

    public Integer getUnit_id() {
        return unit_id;
    }

    public void setUnit_id(Integer unit_id) {
        this.unit_id = unit_id;
    }
}