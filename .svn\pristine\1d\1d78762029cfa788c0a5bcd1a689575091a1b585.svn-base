package com.kemov.visual.spcd.spcdvisualandroidapp.bean;

public class GlwlLabelBean {

    private static GlwlLabelBean instance;

    public synchronized static GlwlLabelBean getInstance() {
        if (instance == null) {
            instance = new GlwlLabelBean();
        }
        return instance;
    }

    public static void clear(){
        if (instance != null) {
            instance = null;
        }
    }

    private String cubicleNameA;
    private String cubicleNameB;
    private String glwlName;
    private InfoBean infoBeanA;
    private InfoBean infoBeanB;

    private Integer tag_type;//0是国网版本，1是陕西版本

    public Integer getTag_type() {
        return tag_type;
    }

    public void setTag_type(Integer tag_type) {
        this.tag_type = tag_type;
    }
    public String getCubicleNameA() {
        return cubicleNameA;
    }

    public void setCubicleNameA(String cubicleNameA) {
        this.cubicleNameA = cubicleNameA;
    }

    public String getCubicleNameB() {
        return cubicleNameB;
    }

    public void setCubicleNameB(String cubicleNameB) {
        this.cubicleNameB = cubicleNameB;
    }

    public String getGlwlName() {
        return glwlName;
    }

    public void setGlwlName(String glwlName) {
        this.glwlName = glwlName;
    }

    public InfoBean getInfoBeanA() {
        return infoBeanA;
    }

    public void setInfoBeanA(InfoBean infoBeanA) {
        this.infoBeanA = infoBeanA;
    }

    public InfoBean getInfoBeanB() {
        return infoBeanB;
    }

    public void setInfoBeanB(InfoBean infoBeanB) {
        this.infoBeanB = infoBeanB;
    }

    public static class InfoBean {
        private String no;
        private String info;
        private String fr;
        private String to;
        private String qrCode;

        //陕西版本二维码图
        String glName;
        String glgq;
        String startUnit;
        String endUnit;
        String startGS;
        String endGS;
        String startPort;
        String endPort;

        public String getGlName() {
            return glName;
        }

        public void setGlName(String glName) {
            this.glName = glName;
        }

        public String getGlgq() {
            return glgq;
        }

        public void setGlgq(String glgq) {
            this.glgq = glgq;
        }

        public String getStartUnit() {
            return startUnit;
        }

        public void setStartUnit(String startUnit) {
            this.startUnit = startUnit;
        }

        public String getEndUnit() {
            return endUnit;
        }

        public void setEndUnit(String endUnit) {
            this.endUnit = endUnit;
        }

        public String getStartGS() {
            return startGS;
        }

        public void setStartGS(String startGS) {
            this.startGS = startGS;
        }

        public String getEndGS() {
            return endGS;
        }

        public void setEndGS(String endGS) {
            this.endGS = endGS;
        }

        public String getStartPort() {
            return startPort;
        }

        public void setStartPort(String startPort) {
            this.startPort = startPort;
        }

        public String getEndPort() {
            return endPort;
        }

        public void setEndPort(String endPort) {
            this.endPort = endPort;
        }

        public String getQrCode() {
            return qrCode;
        }

        public void setQrCode(String qrCode) {
            this.qrCode = qrCode;
        }

        public String getNo() {
            return no;
        }

        public void setNo(String no) {
            this.no = no;
        }

        public String getInfo() {
            return info;
        }

        public void setInfo(String info) {
            this.info = info;
        }

        public String getFr() {
            return fr;
        }

        public void setFr(String fr) {
            this.fr = fr;
        }

        public String getTo() {
            return to;
        }

        public void setTo(String to) {
            this.to = to;
        }
    }
}
