package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2;

import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;

public class IntCorePort2PortBean extends LinePort2PortBean {
    KM_SPCD_INTCORE intCore;


    public KM_SPCD_INTCORE getIntCore() {
        return intCore;
    }

    public void setIntCore(KM_SPCD_INTCORE intCore) {
        this.intCore = intCore;
        // TODO: guoyong 2019/11/4 通过IntCore找到两个端口PortA、PortB
    }
}
