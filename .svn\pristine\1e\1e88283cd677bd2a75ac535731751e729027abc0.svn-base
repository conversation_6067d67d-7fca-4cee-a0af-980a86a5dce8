package com.kemov.visual.spcd.spcdvisualandroidapp.bean;

import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;

import java.util.List;

public class OdfSwitchBean {
    private Integer type;//1是intcore  2是core
    private KM_SPCD_UNIT km_spcd_unit;//当前经过的装置（odf，交换机）
    private KM_SPCD_INTCORE km_spcd_intcore;//当前连接的intcore
    private KM_SPCD_CORE km_spcd_core;//当前连接的core
    private KM_SPCD_CABLE km_spcd_cable;//当前连接的光缆 尾缆
    private String portRx;//当前连接进来的 51n.B13.05-Tx
    private String portTx;//当前连接出去的 51n.B13.05-Tx
    private List<OdfSwitchBean> connectedBeans;//当前

    //新增
    private String PortAdsc;
    private String PortAdscInt;
    private String BoardPort;
    private String Board;
    private String Port;

    //新增"物理全回路图"数据结构
    private KM_SPCD_CUBICLE km_spcd_cubicle;
    private WholeCircuitBean.UnitWholeBean unitWholeBean;

    private WholeCircuitBean.UnitWholeBean unitWholeBean1;

    //设置高亮显示的一对Port
    List<KM_SPCD_PORT> highlightPorts;

    public WholeCircuitBean.UnitWholeBean getUnitWholeBean1() {
        return unitWholeBean1;
    }

    public void setUnitWholeBean1(WholeCircuitBean.UnitWholeBean unitWholeBean1) {
        this.unitWholeBean1 = unitWholeBean1;
    }

    public List<KM_SPCD_PORT> getHighlightPorts() {
        return highlightPorts;
    }

    public void setHighlightPorts(List<KM_SPCD_PORT> highlightPorts) {
        this.highlightPorts = highlightPorts;
    }

    public String getPort() {
        return Port;
    }

    public void setPort(String port) {
        Port = port;
    }

    public String getBoardPort() {
        return BoardPort;
    }

    public void setBoardPort(String boardPort) {
        BoardPort = boardPort;
    }

    public String getBoard() {
        return Board;
    }

    public void setBoard(String board) {
        Board = board;
    }

    public String getPortAdsc() {
        return PortAdsc;
    }

    public void setPortAdsc(String portAdsc) {
        PortAdsc = portAdsc;
    }

    public String getPortAdscInt() {
        return PortAdscInt;
    }

    public void setPortAdscInt(String portAdscInt) {
        PortAdscInt = portAdscInt;
    }

    public String getPortTx() {
        return portTx;
    }

    public void setPortTx(String portTx) {
        this.portTx = portTx;
    }

    public KM_SPCD_CUBICLE getKm_spcd_cubicle() {
        return km_spcd_cubicle;
    }

    public void setKm_spcd_cubicle(KM_SPCD_CUBICLE km_spcd_cubicle) {
        this.km_spcd_cubicle = km_spcd_cubicle;
    }

    public WholeCircuitBean.UnitWholeBean getUnitWholeBean() {
        return unitWholeBean;
    }

    public void setUnitWholeBean(WholeCircuitBean.UnitWholeBean unitWholeBean) {
        this.unitWholeBean = unitWholeBean;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getPortRx() {
        return portRx;
    }

    public void setPortRx(String portRx) {
        this.portRx = portRx;
    }

    public KM_SPCD_INTCORE getKm_spcd_intcore() {
        return km_spcd_intcore;
    }

    public void setKm_spcd_intcore(KM_SPCD_INTCORE km_spcd_intcore) {
        this.km_spcd_intcore = km_spcd_intcore;
    }

    public KM_SPCD_CORE getKm_spcd_core() {
        return km_spcd_core;
    }

    public void setKm_spcd_core(KM_SPCD_CORE km_spcd_core) {
        this.km_spcd_core = km_spcd_core;
    }

    public KM_SPCD_CABLE getKm_spcd_cable() {
        return km_spcd_cable;
    }

    public void setKm_spcd_cable(KM_SPCD_CABLE km_spcd_cable) {
        this.km_spcd_cable = km_spcd_cable;
    }

    public KM_SPCD_UNIT getKm_spcd_unit() {
        return km_spcd_unit;
    }

    public void setKm_spcd_unit(KM_SPCD_UNIT km_spcd_unit) {
        this.km_spcd_unit = km_spcd_unit;
    }

    public List<OdfSwitchBean> getConnectedBeans() {
        return connectedBeans;
    }

    public void setConnectedBeans(List<OdfSwitchBean> connectedBeans) {
        this.connectedBeans = connectedBeans;
    }
}
