package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.vir_real_circuit;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.RectF;
import android.text.Layout;
import android.text.TextPaint;
import android.util.Log;

import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.utils.DrawUtils;

import java.util.List;

/**
 *功能：虚实回路图绘图自定义端口控制块列表图元，连接两个IED。
 *Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/9/5 9:12
 */
public class PortCtrlBlockListCanvas {
    private static final boolean SHOW_DRAWING_HELPER = false;//开启辅助线
    Paint arrowPaint = new Paint();

    Paint mPaint = new Paint();
    TextPaint mTxtPaint = new TextPaint();
    TextPaint txtPaintLeft = new TextPaint();
    Paint paintWithEffect = new Paint();

    float right = -1, top = -1, width = -1, height = -1;
    RectF rectF;
    List<PortCtrlBlockItem> portCtrlBlockItems;

    public PortCtrlBlockListCanvas() {
        initPaint();
    }

    private void initPaint(){
        arrowPaint.setColor(Color.RED);
        arrowPaint.setStyle(Paint.Style.STROKE);
        arrowPaint.setStrokeWidth(5);
        arrowPaint.setAntiAlias(true);

        mPaint.setColor(Color.RED);
        mPaint.setStyle(Paint.Style.FILL);

        mTxtPaint.setColor(Color.BLACK);
        mTxtPaint.setTextSize(16);
        mTxtPaint.setAntiAlias(true);
        mTxtPaint.setTextAlign(Paint.Align.CENTER);

        txtPaintLeft.setColor(Color.BLACK);
        txtPaintLeft.setTextSize(16);
        txtPaintLeft.setAntiAlias(true);
        txtPaintLeft.setTextAlign(Paint.Align. LEFT);

        paintWithEffect.setColor(0x7f000000);
        paintWithEffect.setStyle ( Paint.Style.STROKE ) ;
        paintWithEffect.setStrokeWidth(3);
        //设置虚线效果
        paintWithEffect.setPathEffect ( new DashPathEffect( new float [ ] { 6, 8 }, 0 ) ) ;

    }

    public RectF getRectF() {
        return rectF;
    }

    public void setRectF(RectF rectF) {
        this.rectF = rectF;
    }

    public List<PortCtrlBlockItem> getPortCtrlBlockItems() {
        return portCtrlBlockItems;
    }

    //Thirdly
    public PortCtrlBlockListCanvas setPortCtrlBlockItems(List<PortCtrlBlockItem> portCtrlBlockItems) {
        if (width==-1 ||right==-1 ||top==-1 ) throw new RuntimeException("请先调用setRightTop(xx)和setWidth(xx)!");
        this.portCtrlBlockItems = portCtrlBlockItems;
        initHeight();
        initRects();
        for (int i = 0; i < portCtrlBlockItems.size(); i++) {
            PortCtrlBlockItem item = portCtrlBlockItems.get(i);
            item.setItemRectF(getNthItemRectF(i));
        }
        return this;
    }

    private void initHeight() {
        height = VR_CONTANTS.ITEM_HEIGHT*portCtrlBlockItems.size();
    }

    //Firstly
    public PortCtrlBlockListCanvas setWidth(float width){
        this.width = width;
        return this;
    }
    //Secondly
    public PortCtrlBlockListCanvas setRightTop(float right,float top){
        this.right = right;
        this.top = top;
        //initRects();
        return this;
    }

    private void initRects(){
        rectF = new RectF(right-width,top,right,top+height);
    }

    public void draw(Canvas canvas){
        ///绘制辅助线
        if (SHOW_DRAWING_HELPER){
            canvas.drawRect(rectF, paintWithEffect);
        }

        for (PortCtrlBlockItem item : portCtrlBlockItems) {
            item.draw(canvas);
        }
    }

    public class PortCtrlBlockItem implements draw{
        PortInfo cenPortInfo;
        PortInfo extPortInfo;
        CtrlBlock ctrlBlock;
        RectF itemRectF;

        public PortCtrlBlockItem() {
        }

        public PortInfo getCenPortInfo() {
            return cenPortInfo;
        }

        public PortInfo getExtPortInfo() {
            return extPortInfo;
        }

        public CtrlBlock getCtrlBlock() {
            return ctrlBlock;
        }

        public void setCenPortInfo(PortInfo cenPortInfo) {
            this.cenPortInfo = cenPortInfo;
        }

        public void setExtPortInfo(PortInfo extPortInfo) {
            this.extPortInfo = extPortInfo;

        }

        public void setCtrlBlock(CtrlBlock ctrlBlock) {
            this.ctrlBlock = ctrlBlock;
        }

        public void setItemRectF(RectF itemRectF){
            this.itemRectF = itemRectF;
            initItemRects(itemRectF);
        }

        private void initItemRects(RectF itemRectF) {
            initLeftPort(itemRectF);
            initRightPort(itemRectF);
            initMiddleCtrlBlock(itemRectF);

        }

        private void initLeftPort(RectF itemRectF) {
            float portRectLeft = itemRectF.left;
            float portRectTop = itemRectF.top+VR_CONTANTS.ITEM_PORT_MARGIN_TOP_BOTTOM;
            float portRectRight = itemRectF.left+VR_CONTANTS.PORT_WIDTH;
            float portRectBottom = portRectTop+VR_CONTANTS.PORT_HEIGHT;
            cenPortInfo.setIedBoardPortInfoRectF(new RectF(portRectLeft,portRectTop,portRectRight,portRectBottom));
            cenPortInfo.setIedBoardSlotRectF(
                    new RectF(portRectLeft-VR_CONTANTS.BOARD_SLOT_WIDTH,
                            portRectTop,portRectLeft,portRectBottom));
        }

        private void initRightPort(RectF itemRectF) {
            float portRectLeft = itemRectF.right-VR_CONTANTS.PORT_WIDTH;
            float portRectTop = itemRectF.top+VR_CONTANTS.ITEM_PORT_MARGIN_TOP_BOTTOM;
            float portRectRight = itemRectF.right;
            float portRectBottom = portRectTop+VR_CONTANTS.PORT_HEIGHT;
            extPortInfo.setIedBoardPortInfoRectF(new RectF(portRectLeft,portRectTop,portRectRight,portRectBottom));
            extPortInfo.setIedBoardSlotRectF(
                    new RectF(portRectRight, portRectTop,
                            portRectRight+VR_CONTANTS.BOARD_SLOT_WIDTH,
                            portRectBottom));
        }

        private void initMiddleCtrlBlock(RectF itemRectF) {
            float cbRectLeft = itemRectF.centerX()-VR_CONTANTS.CB_WIDTH/2;
            float cbRectTop = itemRectF.centerY()-VR_CONTANTS.CB_HEIGHT/2;
            float cbRectRight = itemRectF.centerX()+VR_CONTANTS.CB_WIDTH/2;
            float cbRectBottom = itemRectF.centerY()+VR_CONTANTS.CB_HEIGHT/2;
            ctrlBlock.setCbRectF(new RectF(cbRectLeft,cbRectTop,cbRectRight,cbRectBottom));

            if (ctrlBlock.getDirection() == null) return;
            ctrlBlock.initLineCanvas(
                    ctrlBlock.getDirection(),
                    itemRectF.left+VR_CONTANTS.PORT_WIDTH,
                    itemRectF.centerY(),
                    itemRectF.right-VR_CONTANTS.PORT_WIDTH,
                    itemRectF.centerY()
                    );
        }

        @Override
        public void draw(Canvas canvas) {
            //辅助线
            if (SHOW_DRAWING_HELPER){
                paintWithEffect.setColor(0x7fff0000);
                canvas.drawRect(itemRectF,paintWithEffect);
                paintWithEffect.setColor(0x7f000000);
            }

            cenPortInfo.draw(canvas);
            extPortInfo.draw(canvas);
            ctrlBlock.draw(canvas);
        }
    }

    public class PortInfo implements draw{
        boolean isCentre;//是否中心IED的port
        boolean isSend;
        String iedName;
        String iedBoardSlot;
        String iedBoardPortInfo;

        RectF iedBoardSlotRectF;
        RectF iedBoardPortInfoRectF;

        public PortInfo(boolean isCentre, boolean isSend, String iedName, String iedBoardSlot, String iedBoardPortInfo) {
            this.isCentre = isCentre;
            this.isSend = isSend;
            this.iedName = iedName;
            this.iedBoardSlot = iedBoardSlot;
            this.iedBoardPortInfo = iedBoardPortInfo;
        }

        @Override
        public void draw(Canvas canvas) {
            //板卡
            mPaint.setColor(0xffc0c0c0);
            canvas.drawRect(iedBoardSlotRectF,mPaint);
            DrawUtils.textCenter(iedBoardSlot,mTxtPaint,canvas,iedBoardSlotRectF,
                    Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);
            //端口
            if (isSend){
                mPaint.setColor(0xff548B54);
            } else {
                mPaint.setColor(0xff9ACD32);
            }
            canvas.drawRect(iedBoardPortInfoRectF,mPaint);
            DrawUtils.textCenter(iedBoardPortInfo,mTxtPaint,canvas,iedBoardPortInfoRectF,
                    Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);
            mPaint.setColor(0xff000000);//还原画笔
        }

        public boolean isSend() {
            return isSend;
        }

        public void setSend(boolean send) {
            isSend = send;
        }

        public String getIedName() {
            return iedName;
        }

        public void setIedName(String iedName) {
            this.iedName = iedName;
        }

        public String getIedBoardSlot() {
            return iedBoardSlot;
        }

        public void setIedBoardSlot(String iedBoardSlot) {
            this.iedBoardSlot = iedBoardSlot;
        }

        public String getIedBoardPortInfo() {
            return iedBoardPortInfo;
        }

        public void setIedBoardPortInfo(String iedBoardPortInfo) {
            this.iedBoardPortInfo = iedBoardPortInfo;
        }

        public boolean isCentre() {
            return isCentre;
        }

        public void setCentre(boolean centre) {
            isCentre = centre;
        }

        public RectF getIedBoardSlotRectF() {
            return iedBoardSlotRectF;
        }

        public void setIedBoardSlotRectF(RectF iedBoardSlotRectF) {
            this.iedBoardSlotRectF = iedBoardSlotRectF;
        }

        public RectF getIedBoardPortInfoRectF() {
            return iedBoardPortInfoRectF;
        }

        public void setIedBoardPortInfoRectF(RectF iedBoardPortInfoRectF) {
            this.iedBoardPortInfoRectF = iedBoardPortInfoRectF;
        }


    }
    public class CtrlBlock implements draw{
        boolean isGs;
        String AppID;
        Direction direction;
        RectF cbRectF;
        LineCanvas lineCanvas;

        public CtrlBlock(boolean isGs, String appID, Direction direction) {
            this.isGs = isGs;
            this.AppID = appID;
            this.direction = direction;
            this.lineCanvas = new LineCanvas();
            initRectF();
        }

        private void initRectF() {
        }

        @Override
        public void draw(Canvas canvas) {
            //变色
            if (isGs){
                mPaint.setColor(0xff0000ff);
                arrowPaint.setColor(0xff0000ff);

            } else {
                mPaint.setColor(0xff00ff00);
                arrowPaint.setColor(0xff00ff00);
            }

            //箭头
            lineCanvas.draw(canvas,arrowPaint);

            //控制块
            canvas.drawRect(cbRectF,mPaint);
            DrawUtils.textCenter((isGs?"GSE 0x":"SV 0x")+AppID,mTxtPaint,canvas,cbRectF,
                    Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);
            mPaint.setColor(0xff000000);//还原画笔
        }

        public boolean isGs() {
            return isGs;
        }

        public void setGs(boolean gs) {
            isGs = gs;
        }

        public String getAppID() {
            return AppID;
        }

        public void setAppID(String appID) {
            AppID = appID;
        }

        public Direction getDirection() {
            return direction;
        }

        public void setDirection(Direction direction) {
            this.direction = direction;
            lineCanvas.setDirection(direction);
        }

        public RectF getCbRectF() {
            return cbRectF;
        }

        public void setCbRectF(RectF cbRectF) {
            this.cbRectF = cbRectF;
        }

        public void set2PointsOfLine(float leftX, float leftY, float rightX, float rightY){
            lineCanvas.set2Points(leftX, leftY, rightX, rightY);
        }

        public boolean isOnTouch(float x, float y, float totalOffX, float totalOffY, float mSclTot){
            Log.e("doubleClickOnCtrlBlock", "isOnTouch: rectF="+ String.format("%s",rectF.toString()));
            Log.e("doubleClickOnCtrlBlock", "isOnTouch: "+String.format("%f,%f,%f,%f,%f",x,y,totalOffX,totalOffY,mSclTot) );
            x -= totalOffX;
            y -= totalOffY;
            if(x>=cbRectF.left*mSclTot && x<=cbRectF.right*mSclTot
                    && y>=cbRectF.top*mSclTot && y<= cbRectF.bottom*mSclTot)
                return true;
            else {
                return false;
            }
        }

        public void initLineCanvas(Direction direction,float leftX, float leftY, float rightX, float rightY){
            lineCanvas.setDirection(direction);
            lineCanvas.set2Points(leftX, leftY, rightX, rightY);
            lineCanvas.initPath();
        }
    }

    //获取第N个PortItem的rectF
    public RectF getNthItemRectF(int itemIndex){
        float itemTop,itemBottom;
        itemTop = rectF.top+VR_CONTANTS.ITEM_HEIGHT * itemIndex;
        itemBottom = rectF.top+VR_CONTANTS.ITEM_HEIGHT * (itemIndex+1);

        return new RectF(rectF.left,itemTop,rectF.right,itemBottom);
    }

    //获取整个IED的高度
    public float getHeight(){
        int itemSize = portCtrlBlockItems.size();
        return VR_CONTANTS.IED_TYPE_HEIGHT+VR_CONTANTS.ITEM_PORT_TOP_PADDING+VR_CONTANTS.ITEM_PORT_BOTTOM_PADDING
                +itemSize*VR_CONTANTS.ITEM_HEIGHT;
    }

    interface draw {
        void draw(Canvas canvas);
    }

    public enum Direction {
        L2R("L2R"),
        R2L("R2L"),
        DOUBLE("DOUBLE"),
        NONE("NONE");

        private String msg;
        Direction(String msg){
            this.msg=msg;
        };

        @Override
        public String toString() {
            return super.toString();
        }
    }

}
