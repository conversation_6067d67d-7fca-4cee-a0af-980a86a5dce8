package com.kemov.visual.spcd.spcdvisualandroidapp.database.beans;

import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;

import java.io.Serializable;

@DatabaseTable(tableName = "KM_SPCD_PORT")
public class KM_SPCD_PORT implements Serializable {
    @DatabaseField(generatedId = true)
    private Integer id;

    @DatabaseField(columnName = "no",index = true)
    private String no;

    @DatabaseField(columnName = "description")
    private String description;

    @DatabaseField(columnName = "direction",index = true)
    private String direction;

    @DatabaseField(columnName = "plug")
    private String plug;

    @DatabaseField(columnName = "vlanid")
    private Integer vlanid;

    @DatabaseField(columnName = "board_id",index = true)
    private Integer board_id;

    @DatabaseField(columnName = "usage",defaultValue = "")
    private String usage;

    @DatabaseField(columnName = "signal_type",defaultValue = "")
    private String signal_type;


    public String getSignal_type() {
        return signal_type;
    }

    public void setSignal_type(String signal_type) {
        this.signal_type = signal_type;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no == null ? null : no.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction == null ? null : direction.trim();
    }

    public String getPlug() {
        return plug;
    }

    public void setPlug(String plug) {
        this.plug = plug == null ? null : plug.trim();
    }

    public Integer getVlanid() {
        return vlanid;
    }

    public void setVlanid(Integer vlanid) {
        this.vlanid = vlanid;
    }

    public Integer getBoard_id() {
        return board_id;
    }

    public void setBoard_id(Integer board_id) {
        this.board_id = board_id;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }
}