package com.kemov.visual.spcd.spcdvisualandroidapp.fragment;


import android.os.Bundle;
import android.support.v4.app.Fragment;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;

import com.j256.ormlite.stmt.QueryBuilder;
import com.kemov.visual.spcd.spcdvisualandroidapp.CommonAdapterHelper.BoardAdapter;
import com.kemov.visual.spcd.spcdvisualandroidapp.R;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.OdfViewDataNewBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.OdfViewTierBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.OdfViewUnitPortBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.TierBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_REGION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_SUBSTATION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDao;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.PrefWriter;
import com.kemov.visual.spcd.spcdvisualandroidapp.visualview.OdfNewFontView;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Arrays.asList;

@Deprecated
public class OdfViewNewFragment extends Fragment implements View.OnClickListener {
    private String dbName;
    private int odfUinitId;

    final int [] lineColors = new int[]{
            R.color.black,
            R.color.red,
            R.color.blue,
            R.color.magenta,
            R.color.maroon,
            R.color.deepskyblue,
            R.color.orange,
            R.color.forestgreen,
            R.color.darkkhaki,
            R.color.deep_gray,
    };

    BaseDao cableDao = null;
    BaseDao coreDao = null;
    BaseDaoImp unitDao = null;
    BaseDaoImp boardDao = null;
    BaseDaoImp portDao = null;
    BaseDaoImp cubicleDao = null;
    BaseDaoImp regionDao = null;
    BaseDaoImp substationDao =null;
    KM_SPCD_CUBICLE km_spcd_cubicle;//查出的传进来装置所属的屏柜
    KM_SPCD_SUBSTATION km_spcd_substation;//查出传进来装置所属的厂站
    KM_SPCD_UNIT km_spcd_unit;//传进来的装置

    OdfViewDataNewBean odfViewDataNewBean;
    View view;

    private LinearLayout ll_zoom_bar;
    private ImageView mapBiggerView0, mapSmallerView0, mapOriginalView0, mapOriginalViewFilled0;//缩放适配
//    private OdfNewView odfView;
    private OdfNewFontView odfView;
    private OdfViewTierBean.TierBean tierBean;
    private int num;
    private KM_SPCD_REGION km_spcd_region;
    private BaseDaoImp intcoreDao;

    List<OdfViewDataNewBean.CableBean> leftList = new ArrayList<>();
    List<OdfViewDataNewBean.CableBean> rightList = new ArrayList<>();

    List<OdfViewUnitPortBean> leftOdfViewUnitPortBeanList = new ArrayList<>();
    List<OdfViewUnitPortBean> rightOdfViewUnitPortBeanList = new ArrayList<>();
    private Spinner spinner;
    List<OdfViewTierBean.TierBean> tierList;
    private int hbPortID;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.fragment_odf_new_view, container, false);
        initView();
        return view;
    }

    private void initView() {
        Bundle bundle = getArguments();
        dbName = bundle.getString("dbName");
        odfUinitId = bundle.getInt("odfUinitId",0);
        hbPortID = bundle.getInt("hbPortID",0);
        //连线关系
        tierBean = (OdfViewTierBean.TierBean)bundle.getSerializable("tierBean");
        num = bundle.getInt("num",-1);

        findViewWithId();
        initDaos();
        getData0(num);
        //初始化spinner
        initSpinner();

//        getData();
//        //将数据整理，生成中间页面需要的数据
//        TrimData();
    }

    private void initSpinner() {
        if (odfUinitId!=0){
            km_spcd_unit = (KM_SPCD_UNIT)unitDao.getById(odfUinitId);
            List<KM_SPCD_BOARD> boardList = (List<KM_SPCD_BOARD>) boardDao.getListForEq("unit_id", km_spcd_unit.getId());
            BoardAdapter odfAdapter= new BoardAdapter(boardList,getActivity());
            spinner.setAdapter(odfAdapter);
            spinner.setSelection(num);
            spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) spinner.getSelectedItem();
                    //查询现在显示的板卡
                    OdfViewTierBean.TierBean tierBean = tierList.get(num);
                    KM_SPCD_PORT km_spcd_port = tierBean.getPorts().get(0);
                    if (km_spcd_board.getId().intValue() == km_spcd_port.getBoard_id().intValue()){
                        return;
                    }
                    for (int x=0;x<tierList.size();x++){
                        OdfViewTierBean.TierBean tierBean1 = tierList.get(x);
                        if (tierBean1.getPorts().get(0).getBoard_id().intValue() == km_spcd_board.getId().intValue()){
                            num = x;
                            getData0(x);
                            //擦除之前画画的信息
                            scratchPic();

                            odfView.drawAgain();
                            break;
                        }
                    }
                }
                @Override
                public void onNothingSelected(AdapterView<?> parent) {
                }
            });
        }
    }

    private void scratchPic() {
        TierBean tierBean = TierBean.getInstance();
        List<OdfViewDataNewBean.CableBean> cableBeans = tierBean.getCableBeans();//左侧光缆线
        List<OdfViewDataNewBean.CableBean> cableRightBeans = tierBean.getCableRightBeans();//左侧光缆线
        if (cableBeans!=null && cableBeans.size()!=0){
            for (OdfViewDataNewBean.CableBean cableBean:cableBeans){
                KM_SPCD_CABLE km_spcd_cable = cableBean.getKm_spcd_cable();
                String name = km_spcd_cable.getName();
                PrefWriter.getInstance(getActivity()).remove(name + "max");
                PrefWriter.getInstance(getActivity()).remove(name + "min");
            }
        }
        if (cableRightBeans!=null && cableRightBeans.size()!=0){
            for (OdfViewDataNewBean.CableBean cableBean:cableRightBeans){
                KM_SPCD_CABLE km_spcd_cable = cableBean.getKm_spcd_cable();
                String name = km_spcd_cable.getName();
                PrefWriter.getInstance(getActivity()).remove(name + "maxRight");
                PrefWriter.getInstance(getActivity()).remove(name + "minRight");
            }
        }
    }

    private void getData0(Integer num) {
        tierList = OdfViewTierBean.getInstance().getTierList();
        if (tierList == null || tierList.size()==0){
            getData();
            trimData();
            tierList = OdfViewTierBean.getInstance().getTierList();
        }
        if (tierList !=null && tierList.size()!=0 && tierList.size() >= num){
            OdfViewTierBean.TierBean tierBean = tierList.get(num);
            TierBean tierBean1 = TierBean.getInstance();
            tierBean1.setPorts(tierBean.getPorts());
            tierBean1.setCableBeans(tierBean.getCableBeans());
            tierBean1.setCableRightBeans(tierBean.getCableRightBeans());
        }
    }

    private void trimData() {
        OdfViewTierBean odfViewTierBean = OdfViewTierBean.getInstance();
        List<OdfViewTierBean.TierBean> tierBeans = new ArrayList<>();
        odfViewTierBean.setTierList(tierBeans);
        odfViewTierBean.setPortId(hbPortID);

        if (odfViewDataNewBean != null){
            List<KM_SPCD_PORT> ports = odfViewDataNewBean.getPorts();
            if (ports !=null && ports.size()!=0){
                for (int x =0;x<ports.size();x++){
                    KM_SPCD_PORT km_spcd_port = ports.get(x);
                    List<KM_SPCD_PORT> ports1 = null;
                    if (x > 0 && !km_spcd_port.getBoard_id().equals(ports.get(x -1).getBoard_id())){
                        ports1 = newTierBean(tierBeans);
                    }else {
                        if (x == 0){
                            ports1 = newTierBean(tierBeans);
                        }else {
                            List<OdfViewTierBean.TierBean> tierList = OdfViewTierBean.getInstance().getTierList();
                            OdfViewTierBean.TierBean tierBean = tierList.get(tierList.size() - 1);
                            ports1 = tierBean.getPorts();
                        }
                    }
                    //添加端口
                    ports1.add(km_spcd_port);
                }
            }
        }
    }

    public List<KM_SPCD_PORT> newTierBean(List<OdfViewTierBean.TierBean> tierBeans){
        //新建对象
        OdfViewTierBean.TierBean tierBean = new OdfViewTierBean.TierBean();
        List<KM_SPCD_PORT> ports1 = new ArrayList<>();
        tierBean.setPorts(ports1);
        tierBeans.add(tierBean);

        tierBean.setCableBeans(odfViewDataNewBean.getCableBeans());
        tierBean.setCableRightBeans(odfViewDataNewBean.getCableRightBeans());
        return ports1;
    }

    private void initDaos() {
        unitDao = new BaseDaoImp(getActivity().getApplicationContext(), KM_SPCD_UNIT.class, dbName);
        boardDao = new BaseDaoImp(getActivity().getApplicationContext(), KM_SPCD_BOARD.class, dbName);
        intcoreDao = new BaseDaoImp(getActivity().getApplicationContext(), KM_SPCD_INTCORE.class, dbName);
        portDao = new BaseDaoImp(getActivity().getApplicationContext(), KM_SPCD_PORT.class, dbName);
        cableDao = new BaseDaoImp(getActivity().getApplicationContext(), KM_SPCD_CABLE.class, dbName);
        coreDao = new BaseDaoImp(getActivity().getApplicationContext(), KM_SPCD_CORE.class, dbName);
        cubicleDao = new BaseDaoImp(getActivity().getApplicationContext(), KM_SPCD_CUBICLE.class, dbName);
        regionDao = new BaseDaoImp(getActivity().getApplicationContext(), KM_SPCD_REGION.class, dbName);
        substationDao = new BaseDaoImp(getActivity().getApplicationContext(), KM_SPCD_SUBSTATION.class, dbName);
    }

    public void findViewWithId() {
//        odfView = (OdfNewView)view.findViewById(R.id.odfView);
        odfView = (OdfNewFontView)view.findViewById(R.id.odfView);
        spinner = (Spinner)view.findViewById(R.id.spinner);

        ll_zoom_bar = view.findViewById(R.id.ll_zoom_bar);
        mapBiggerView0 = (ImageView) view.findViewById(R.id.mapBigger0);
        mapSmallerView0 = (ImageView) view.findViewById(R.id.mapSmaller0);
        mapOriginalView0 = (ImageView) view.findViewById(R.id.mapOriginal0);
        mapOriginalViewFilled0 = (ImageView)view.findViewById(R.id.mapOriginalFilled);
        mapBiggerView0.setOnClickListener(this);
        mapSmallerView0.setOnClickListener(this);
        mapOriginalView0.setOnClickListener(this);
        mapOriginalViewFilled0.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.mapBigger0) {
            odfView.SetScale(1.5f);
        } else if (id == R.id.mapSmaller0) {
            odfView.SetScale(0.5f);
        } else if (id == R.id.mapOriginal0) {
            odfView.RequestLayout_FillWidth();
        } else if (id == R.id.mapOriginalFilled) {//全局显示
            odfView.RequestLayout_FillWidthHeight();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void getData(){
        if (odfUinitId <= 0){
            return;
        }
        odfViewDataNewBean = OdfViewDataNewBean.getInstance();
        km_spcd_unit = (KM_SPCD_UNIT)unitDao.getById(odfUinitId);
        //1.查找端口列表信息
        List<KM_SPCD_PORT> ports = null;
        try {
            QueryBuilder<KM_SPCD_BOARD, Integer> boardQB = boardDao.getDao().queryBuilder();
            QueryBuilder<KM_SPCD_PORT, Integer> portQB = portDao.getDao().queryBuilder();
            boardQB.where().eq("unit_id", odfUinitId);
            ports = portQB.join("board_id","id",boardQB).query();
        } catch (SQLException e) {
            e.printStackTrace();
        }

        List<KM_SPCD_PORT> portsFinal = null;
        if (ports!=null && ports.size()!=0){
            //去除收发相同的端口其中一个
            portsFinal = new ArrayList<>();
            List<String> nos = new ArrayList<>();
            for (KM_SPCD_PORT km_spcd_port : ports){
                KM_SPCD_BOARD km_spcd_board1 = (KM_SPCD_BOARD)boardDao.getById(km_spcd_port.getBoard_id());
                String slotNo = km_spcd_board1.getSlot() + km_spcd_port.getNo();
                if (!nos.contains(km_spcd_board1.getSlot()+km_spcd_port.getNo())){
                    nos.add(slotNo);
                    //拼装端口名，board-desc和port-no,放到usage里面，用作后面展示
                    km_spcd_port.setUsage(km_spcd_board1.getDescription()+km_spcd_port.getNo());
                    portsFinal.add(km_spcd_port);
                }
            }
            Log.e("去重之后的km_spcd_port",portsFinal.toString());
        }
        odfViewDataNewBean.setPorts(portsFinal);

        //2.查找端口连接光缆信息，接收端集合(cubicleB="R66.XLP1A")
        km_spcd_cubicle = (KM_SPCD_CUBICLE)cubicleDao.getById(km_spcd_unit.getCubicle_id());//屏柜
        km_spcd_substation = (KM_SPCD_SUBSTATION)substationDao.getById(km_spcd_cubicle.getSubstation_id());//厂站
        km_spcd_region = (KM_SPCD_REGION)regionDao.getById(km_spcd_cubicle.getRegion_id());//小室

        leftList.clear();
        rightList.clear();
        leftOdfViewUnitPortBeanList.clear();
        rightOdfViewUnitPortBeanList.clear();
        //左侧数据
        String cubicleB = km_spcd_region.getName()+"."+km_spcd_cubicle.getName();//cubicleB参数，如：R66.XLKZ1A
        List<KM_SPCD_CABLE> cables = cableDao.getListForEq("cubicleB", cubicleB,"station_id",km_spcd_substation.getId());//连接进来的光缆
        getLeftAndRightData(cables, 1,ports);
        //右侧数据
        List<KM_SPCD_CABLE> cables1 = cableDao.getListForEq("cubicleA", cubicleB,"station_id",km_spcd_substation.getId());//连接出去的光缆
        getLeftAndRightData(cables1,2,ports);

        odfViewDataNewBean.setCableBeans(leftList);
        odfViewDataNewBean.setCableRightBeans(rightList);
    }

    private void getLeftAndRightData(List<KM_SPCD_CABLE> cables, int type, List<KM_SPCD_PORT> ports) {
        //3.查找光缆连接的是哪些端口
        if (cables!=null && cables.size()!=0){
            List<OdfViewDataNewBean.CableBean> cableBeans = new ArrayList<>();
            for (int x=0;x<cables.size();x++){
                KM_SPCD_CABLE km_spcd_cable = cables.get(x);
                Map<Integer, OdfViewUnitPortBean> map = new HashMap<>();

                List<KM_SPCD_CORE> cores = coreDao.queryLike("cable_id", km_spcd_cable.getId());//获取到光缆连接的多个端口，通过端口信息去找端口id
                List<Integer> coreIds = new ArrayList<>();
                if (cores!=null && cores.size()!=0){
                    for (KM_SPCD_CORE km_spcd_core: cores){
                        //比如portB="1n.1.B-Rx"
                        Integer coreId = getCoreId(km_spcd_core,type,map);
                        if (coreId!=null){
                            coreIds.add(coreId);
                        }
                    }
                }
                if (coreIds!=null && coreIds.size()!=0){
                    OdfViewDataNewBean.CableBean cableBean = new OdfViewDataNewBean.CableBean();
                    //对侧屏柜名称
                    String cubicleA = null;
                    if (type == 1){
                        cubicleA = km_spcd_cable.getCubicleA();
                    }else if (type == 2){
                        cubicleA = km_spcd_cable.getCubicleB();
                    }
                    //lgj added 20191119
                    if(TextUtils.isEmpty(cubicleA)){
                        return;
                    }
                    String regionName = cubicleA.substring(0,cubicleA.indexOf("."));//小室如：R66
                    String cubicle = cubicleA.substring(cubicleA.indexOf(".") + 1, cubicleA.length());//屏柜名称，如：XLP1A
                    try {
                        KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("substation_id", km_spcd_cable.getStation_id(),"name",regionName);
                        KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE)cubicleDao.getFirstForEq
                                ("region_id", km_spcd_region.getId(),"name", cubicle, "substation_id", km_spcd_cable.getStation_id());
                        if (km_spcd_cubicle!=null){
                            km_spcd_cable.setDescription(km_spcd_cubicle.getDescription());
                        }
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                    cableBean.setLineColor(lineColors[x%10]);//设置后面画线的颜色
                    cableBean.setKm_spcd_cable(km_spcd_cable);
                    cableBean.setCableCoreIds(coreIds);
                    cableBean.setMap(map);
                    cableBeans.add(cableBean);
                }
            }
            if (type == 1){
                //收集右侧连接线集合
                trimLeftAndRight(ports,cableBeans,1);
                leftList.addAll(cableBeans);
//                odfViewDataNewBean.setCableBeans(cableBeans);
            }else if (type == 2){
                //收集左侧连接线集合
                trimLeftAndRight(ports,cableBeans,2);
                rightList.addAll(cableBeans);
//                odfViewDataNewBean.setCableRightBeans(cableBeans);
            }
        }
    }

    public void trimLeftAndRight(List<KM_SPCD_PORT> ports, List<OdfViewDataNewBean.CableBean> cableBeans, int type){
        for (KM_SPCD_PORT km_spcd_port:ports){
            for (OdfViewDataNewBean.CableBean cableBean:cableBeans){
                Map<Integer, OdfViewUnitPortBean> map = cableBean.getMap();
                OdfViewUnitPortBean odfViewUnitPortBean = map.get(km_spcd_port.getId());
                if (odfViewUnitPortBean == null){
                    continue;
                }
                OdfViewUnitPortBean.OtherConnectUnit otherConnectUnit = odfViewUnitPortBean.getOtherConnectUnit();
                if (otherConnectUnit!=null){
                    OdfViewUnitPortBean odfViewUnitPortBean1 = new OdfViewUnitPortBean();
                    odfViewUnitPortBean1.setType(otherConnectUnit.getType());
                    odfViewUnitPortBean1.setKm_spcd_unit(otherConnectUnit.getKm_spcd_unit());
                    odfViewUnitPortBean1.setKm_spcd_port(km_spcd_port);
                    odfViewUnitPortBean1.setKm_spcd_port1(otherConnectUnit.getKm_spcd_port());
                    odfViewUnitPortBean1.setPort(otherConnectUnit.getPort());

                    //查找对侧装置的屏柜信息
                    if (otherConnectUnit.getKm_spcd_unit()!=null){
                        KM_SPCD_UNIT km_spcd_unit = otherConnectUnit.getKm_spcd_unit();
                        KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getById(km_spcd_unit.getCubicle_id());
                        odfViewUnitPortBean1.setPortIdLeftAndRight(getPortId(km_spcd_cubicle,km_spcd_unit.getName()+"."+otherConnectUnit.getPort()));
                    }
                    odfViewUnitPortBean1.setUnitName(otherConnectUnit.getUnitName());
                    odfViewUnitPortBean1.setIntcore(otherConnectUnit.getIntcore());
                    odfViewUnitPortBean1.setKm_spcd_cable(otherConnectUnit.getKm_spcd_cable());
                    odfViewUnitPortBean1.setKm_spcd_cubicle(otherConnectUnit.getKm_spcd_cubicle());
                    if (type == 1){
                        rightOdfViewUnitPortBeanList.add(odfViewUnitPortBean1);
                    }else if (type == 2){
                        leftOdfViewUnitPortBeanList.add(odfViewUnitPortBean1);
                    }
                }
                break;
            }
        }
        //如果缆相同整理成一根缆线
        if (rightOdfViewUnitPortBeanList!=null && rightOdfViewUnitPortBeanList.size()!=0 && type == 1){
            Map<String, OdfViewDataNewBean.CableBean> cableBeanMap = new HashMap<>();
            for (OdfViewUnitPortBean odfViewUnitPortBean:rightOdfViewUnitPortBeanList){
                if (odfViewUnitPortBean.getType() == 1){//连接的是光纤
                    OdfViewDataNewBean.CableBean cableBean = new OdfViewDataNewBean.CableBean();
                    cableBean.setLineColor(lineColors[0]);
                    cableBean.setKm_spcd_cable(odfViewUnitPortBean.getKm_spcd_cable());
                    cableBean.setKm_spcd_intcore(odfViewUnitPortBean.getIntcore());
                    cableBean.setType(1);
                    List<Integer> coreIds = new ArrayList<>();
                    coreIds.add(odfViewUnitPortBean.getKm_spcd_port().getId());
                    cableBean.setCableCoreIds(coreIds);

                    Map<Integer, OdfViewUnitPortBean> map = new HashMap<>();
                    map.put(odfViewUnitPortBean.getKm_spcd_port().getId(),odfViewUnitPortBean);
                    cableBean.setMap(map);

                    rightList.add(cableBean);
                }else if (odfViewUnitPortBean.getType() == 2){//连接的是光缆
                    OdfViewDataNewBean.CableBean cableBean = null;
                    List<Integer> portss = null;
                    if (cableBeanMap.containsKey(odfViewUnitPortBean.getKm_spcd_cable().getName())){
                        cableBean = cableBeanMap.get(odfViewUnitPortBean.getKm_spcd_cable().getName());
                        portss = cableBean.getCableCoreIds();
                    }else {
                        cableBean = new OdfViewDataNewBean.CableBean();
                        portss = new ArrayList<>();
                        cableBean.setCableCoreIds(portss);
                        cableBeanMap.put(odfViewUnitPortBean.getKm_spcd_cable().getName(),cableBean);
                        //设置值
                        cableBean.setKm_spcd_cable(odfViewUnitPortBean.getKm_spcd_cable());
                        //设置map
                        Map<Integer, OdfViewUnitPortBean> map = new HashMap<>();
                        map.put(odfViewUnitPortBean.getKm_spcd_port().getId(),odfViewUnitPortBean);
                        cableBean.setMap(map);
                    }
                    portss.add(odfViewUnitPortBean.getKm_spcd_port().getId());
                    cableBean.setLineColor(lineColors[0]);
                    rightList.add(cableBean);
                }
            }
        }
        //如果缆相同整理成一根缆线
        if (leftOdfViewUnitPortBeanList!=null && leftOdfViewUnitPortBeanList.size()!=0 && type == 2){
            Map<String, OdfViewDataNewBean.CableBean> cableBeanMap = new HashMap<>();
            for (OdfViewUnitPortBean odfViewUnitPortBean:leftOdfViewUnitPortBeanList){
                if (odfViewUnitPortBean.getType() == 1){//连接的是光纤
                    OdfViewDataNewBean.CableBean cableBean = new OdfViewDataNewBean.CableBean();
                    cableBean.setLineColor(lineColors[0]);
                    cableBean.setKm_spcd_cable(odfViewUnitPortBean.getKm_spcd_cable());
                    cableBean.setKm_spcd_intcore(odfViewUnitPortBean.getIntcore());
                    cableBean.setType(1);
                    List<Integer> coreIds = new ArrayList<>();
                    coreIds.add(odfViewUnitPortBean.getKm_spcd_port().getId());
                    cableBean.setCableCoreIds(coreIds);

                    Map<Integer, OdfViewUnitPortBean> map = new HashMap<>();
                    map.put(odfViewUnitPortBean.getKm_spcd_port().getId(),odfViewUnitPortBean);
                    cableBean.setMap(map);

                    leftList.add(cableBean);
                }else if (odfViewUnitPortBean.getType() == 2){//连接的是光缆
                    OdfViewDataNewBean.CableBean cableBean = null;
                    List<Integer> portss = null;
                    if (cableBeanMap.containsKey(odfViewUnitPortBean.getKm_spcd_cable().getName())){
                        cableBean = cableBeanMap.get(odfViewUnitPortBean.getKm_spcd_cable().getName());
                        portss = cableBean.getCableCoreIds();
                    }else {
                        cableBean = new OdfViewDataNewBean.CableBean();
                        portss = new ArrayList<>();
                        cableBean.setCableCoreIds(portss);
                        cableBeanMap.put(odfViewUnitPortBean.getKm_spcd_cable().getName(),cableBean);
                        //设置值
                        cableBean.setKm_spcd_cable(odfViewUnitPortBean.getKm_spcd_cable());
                        //设置map
                        Map<Integer, OdfViewUnitPortBean> map = new HashMap<>();
                        map.put(odfViewUnitPortBean.getKm_spcd_port().getId(),odfViewUnitPortBean);
                        cableBean.setMap(map);
                    }
                    cableBean.setLineColor(lineColors[0]);
                    portss.add(odfViewUnitPortBean.getKm_spcd_port().getId());
                    leftList.add(cableBean);
                }
            }
        }
    }


    private Integer getCoreId(KM_SPCD_CORE km_spcd_core, int type, Map<Integer, OdfViewUnitPortBean> map) {
        String port_b = null;
        String port_a = null;
        if (type == 1){
            port_b = km_spcd_core.getPort_b();
            port_a = km_spcd_core.getPort_a();
        }else if (type == 2){
            port_b = km_spcd_core.getPort_a();
            port_a = km_spcd_core.getPort_b();
        }
        if (!TextUtils.isEmpty(port_b) && port_b.contains(".")){
            String[] split = port_b.split("\\.");//4n.1.E-Rx
            String name = split[0];//装置名称，如4n
            String  slot= split[1];//板卡，如1
            String no = split[2].substring(0,split[2].indexOf("-"));//端口中的no，如E
            String direction = split[2].substring(split[2].indexOf("-")+1,split[2].length());//端口中的no，如E
            if (!km_spcd_unit.getName().equals(name)){//如果不是该装置的连接
                return null;
            }
            try {
                KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD)boardDao.getFirstForEq("slot", slot, "unit_id", km_spcd_unit.getId());
                KM_SPCD_PORT km_spcd_port = (KM_SPCD_PORT)portDao.getFirstForEq("board_id", km_spcd_board.getId(), "no",no,"direction",direction);

                KM_SPCD_CABLE km_spcd_cable = (KM_SPCD_CABLE)cableDao.getFirstForEq("id", km_spcd_core.getCable_id());
                String cubicleA = null;
                if (type == 1){
                    cubicleA = km_spcd_cable.getCubicleA();//对侧小室屏柜
                }else if (type ==2){
                    cubicleA = km_spcd_cable.getCubicleB();//对侧小室屏柜
                }

                List<String> cableCoreList = new ArrayList<>();//用来记录已经找过的纤，避免重复查找造成死循环
                cableCoreList.add(km_spcd_cable.getName()+"/"+km_spcd_core.getNo());
                //去找该端口连接的装置和端口
                String port =  km_spcd_unit.getName()+"."+km_spcd_board.getSlot()+"."+km_spcd_port.getNo()+"-"+km_spcd_port.getDirection();
                setUnitPort(cableCoreList,km_spcd_region,km_spcd_cubicle,km_spcd_port,port,map);
                //去找该端口对侧的连接装置及端口
                setUnitPortOther(cableCoreList,port_a,cubicleA,km_spcd_port,map,1,km_spcd_cable,km_spcd_cubicle);

                return km_spcd_port.getId();
            } catch (SQLException e) {
                e.printStackTrace();
                return null;
            }
        }else {
            return null;
        }
    }

    public void setUnitPortOther(List<String> cableCoreList, String portOther, String cubicleOther, KM_SPCD_PORT km_spcd_port, Map<Integer, OdfViewUnitPortBean> map, Integer first, KM_SPCD_CABLE km_spcd_cableOld, KM_SPCD_CUBICLE km_spcd_cubicleOld){
        try {
            OdfViewUnitPortBean odfViewUnitPortBean = map.get(km_spcd_port.getId());
            //找该端口对侧的连接装置
            if (!TextUtils.isEmpty(cubicleOther) && cubicleOther.contains(".") &&!TextUtils.isEmpty(portOther)){
                String[] split = cubicleOther.split("\\.");//小室+屏柜
                KM_SPCD_REGION km_spcd_region1 = (KM_SPCD_REGION) regionDao.getFirstForEq("substation_id", 1, "name", split[0]);
                if (km_spcd_region1!=null){
                    KM_SPCD_CUBICLE km_spcd_cubicle1 = (KM_SPCD_CUBICLE)cubicleDao.getFirstForEq("substation_id", 1, "region_id", km_spcd_region1.getId(), "name", split[1]);
                    if (km_spcd_cubicle1!=null){
                        KM_SPCD_INTCORE firstForEq3 = (KM_SPCD_INTCORE)intcoreDao.getFirstForEq("cubicle_id", km_spcd_cubicle1.getId(), "port_a", portOther);
                        KM_SPCD_INTCORE firstForEq4 = (KM_SPCD_INTCORE)intcoreDao.getFirstForEq("cubicle_id", km_spcd_cubicle1.getId(), "port_b", portOther);
                        String odfPortOhter = null;
                        KM_SPCD_INTCORE firstForEq0 = null;
                        if (firstForEq3 != null && firstForEq4 == null){
                            odfPortOhter = firstForEq3.getPort_b();
                            firstForEq0 = firstForEq3;
                        }else if (firstForEq3 == null && firstForEq4 != null){
                            odfPortOhter = firstForEq4.getPort_a();
                            firstForEq0 = firstForEq4;
                        }else if (firstForEq3 == null && firstForEq4 == null){
                            //查询该端口是不是就是ied装置
                            String[] split1 = portOther.split("\\.");
                            KM_SPCD_UNIT km_spcd_unit = (KM_SPCD_UNIT) unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle1.getId(), "name", split1[0]);
                            if (km_spcd_unit.getDev_class().equals("IED")){
                                odfPortOhter = portOther;
                            }
                        }

                        if (!TextUtils.isEmpty(odfPortOhter)){
                            //找odf连接的本侧装置
                            String[] split1 = odfPortOhter.split("\\.");
                            String unitName = split1[0];//如：4n
                            KM_SPCD_UNIT km_spcd_unit = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle1.getId(), "name", unitName);
                            KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) boardDao.getFirstForEq("unit_id", km_spcd_unit.getId(), "slot", split1[1]);
                            KM_SPCD_PORT km_spcd_port1 = null;
                            if (!TextUtils.isEmpty(split1[2]) && split1[2].contains("-")){
                                String[] split2 = split1[2].split("-");
                                km_spcd_port1 = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", km_spcd_board.getId(), "no", split2[0], "direction", split2[1]);

                            }

                            //设置值
                            if (odfViewUnitPortBean == null){
                                odfViewUnitPortBean = new OdfViewUnitPortBean();
                            }
                            OdfViewUnitPortBean.OtherConnectUnit otherConnectUnit = new OdfViewUnitPortBean.OtherConnectUnit();
                            odfViewUnitPortBean.setOtherConnectUnit(otherConnectUnit);

                            if (!TextUtils.isEmpty(km_spcd_unit.getDescription())){
                                otherConnectUnit.setUnitName(km_spcd_unit.getDescription());
                            }else {
                                otherConnectUnit.setUnitName(km_spcd_unit.getIed_name());
                            }
                            if (!TextUtils.isEmpty(odfPortOhter)){
                                String[] split2 = odfPortOhter.split("\\.");
                                otherConnectUnit.setPort(split2[1]+"."+split2[2]);
                            }
                            otherConnectUnit.setKm_spcd_unit(km_spcd_unit);
                            otherConnectUnit.setKm_spcd_port(km_spcd_port1);

                            if (first == 1){
                                otherConnectUnit.setType(1);
                                otherConnectUnit.setIntcore(firstForEq0);
                                //如果直接找到了装置
                                otherConnectUnit.setKm_spcd_cable(km_spcd_cableOld);
                                otherConnectUnit.setKm_spcd_cubicle(km_spcd_cubicleOld);
                            }
                            if (first == 2){
                                otherConnectUnit.setType(2);
                                otherConnectUnit.setKm_spcd_cable(km_spcd_cableOld);
                                otherConnectUnit.setKm_spcd_cubicle(km_spcd_cubicleOld);
                            }
                        }else{
                            //直接找intcore没找到，去cable里面接着找
                            String cubicleB = km_spcd_region1.getName()+"." + km_spcd_cubicle1.getName();//如：R66.XLP1A
                            List<String> names = new ArrayList<String>(asList("station_id","cubicleB"));
                            List<String> names1 = new ArrayList<String>(asList("station_id","cubicleA"));
                            List<Object> value = new ArrayList<Object>(asList(km_spcd_substation.getId(),cubicleB));
                            List<KM_SPCD_CABLE> cables1 = cableDao.getListForEq(names, value);
                            List<KM_SPCD_CABLE> cables2 = cableDao.getListForEq(names1, value);
                            KM_SPCD_CORE km_spcd_core0 = null;
                            KM_SPCD_CABLE km_spcd_cable0 = null;
                            int type = 0;
                            int isGo = 0;
                            if (cables1!=null && cables1.size()!=0){
                                for (KM_SPCD_CABLE km_spcd_cable:cables1){
                                    km_spcd_core0 = (KM_SPCD_CORE) coreDao.getFirstForEq("cable_id", km_spcd_cable.getId(), "port_b", portOther);
                                    if (km_spcd_core0 != null && !cableCoreList.contains(km_spcd_cable.getName()+"/"+km_spcd_core0.getNo())){
                                        cableCoreList.add(km_spcd_cable.getName()+"/"+km_spcd_core0.getNo());
                                        type = 1;
                                        isGo = 1;
                                        km_spcd_cable0 = km_spcd_cable;
                                        break;
                                    }
                                }
                            }else if (cables2!=null && cables2.size()!=0 && isGo == 0){
                                for (KM_SPCD_CABLE km_spcd_cable:cables2){
                                    km_spcd_core0 = (KM_SPCD_CORE) coreDao.getFirstForEq("cable_id", km_spcd_cable.getId(), "port_a", portOther);
                                    if (km_spcd_core0 != null && !cableCoreList.contains(km_spcd_cable.getName()+"/"+km_spcd_core0.getNo())){
                                        cableCoreList.add(km_spcd_cable.getName()+"/"+km_spcd_core0.getNo());
                                        type = 2;
                                        km_spcd_cable0 = km_spcd_cable;
                                        break;
                                    }
                                }
                            }
                            if (km_spcd_core0 != null){//如果找到光缆尾缆连接到另外的光缆尾缆
                                String cubicleA = null;
                                String port_a = null;
                                if (type == 1){
                                    cubicleA = km_spcd_cable0.getCubicleA();
                                    port_a = km_spcd_core0.getPort_a();//对侧的连接
                                }else if (type == 2){
                                    cubicleA = km_spcd_cable0.getCubicleB();
                                    port_a = km_spcd_core0.getPort_b();//对侧的连接
                                }
                                //找到小室和屏柜
                                if (!TextUtils.isEmpty(cubicleA) && cubicleA.contains(".") && !TextUtils.isEmpty(port_a)){
                                    String[] split2 = cubicleA.split("\\.");
                                    KM_SPCD_REGION km_spcd_region2 = (KM_SPCD_REGION) regionDao.getFirstForEq("substation_id", 1, "name", split2[0]);
                                    if (km_spcd_region1!=null){
                                        KM_SPCD_CUBICLE km_spcd_cubicle2 = (KM_SPCD_CUBICLE)cubicleDao.getFirstForEq("substation_id", 1, "region_id", km_spcd_region2.getId(), "name", split2[1]);
                                        if (km_spcd_cubicle2!=null){
                                            String cubicleAOther = km_spcd_region2.getName()+"."+km_spcd_cubicle2.getName();
                                            setUnitPortOther(cableCoreList,port_a,cubicleAOther,km_spcd_port,map,2,km_spcd_cable0,km_spcd_cubicle2);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }catch (SQLException e){
            e.printStackTrace();
        }
    }

    private Integer getPortId(KM_SPCD_CUBICLE cubicle1, String odfPortOhter1) {
        String[] split = odfPortOhter1.split("\\.");
        try {
            String s = split[2];
            String[] split1 = s.split("-");
            KM_SPCD_UNIT km_spcd_unit = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", cubicle1.getId(), "name", split[0]);
            KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unit.getId(), "slot", split[1]);
            KM_SPCD_PORT km_spcd_port = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", km_spcd_board.getId(), "no", split1[0], "direction", split1[1]);
            return km_spcd_port.getId();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }

    public KM_SPCD_PORT getPort(KM_SPCD_UNIT km_spcd_unit, String odfPortThis) {
        if (!TextUtils.isEmpty(odfPortThis) && odfPortThis.contains(".")){
            String[] split = odfPortThis.split("\\.");
            String boardPort = split[2];
            try {
                KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD)boardDao.getFirstForEq("slot", split[1], "unit_id", km_spcd_unit.getId());
                if (!TextUtils.isEmpty(boardPort) && boardPort.contains("-")){
                    String[] split1 = boardPort.split("-");
                    KM_SPCD_PORT km_spcd_port = (KM_SPCD_PORT)portDao.getFirstForEq("board_id", km_spcd_board.getId(), "no",split1[0],"direction",split1[1]);
                    return km_spcd_port;
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    private void setUnitPort(List<String> cableCoreList, KM_SPCD_REGION km_spcd_region, KM_SPCD_CUBICLE km_spcd_cubicle, KM_SPCD_PORT km_spcd_port, String odfPort, Map<Integer, OdfViewUnitPortBean> map) {
        //根据odfPort查找该屏柜的连接装置
        String odfPortThis = null;
        try {
            KM_SPCD_INTCORE firstForEq1 = (KM_SPCD_INTCORE)intcoreDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "port_a", odfPort);
            KM_SPCD_INTCORE firstForEq2 = (KM_SPCD_INTCORE)intcoreDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "port_b", odfPort);
            if (firstForEq1 != null && firstForEq2 == null){
                odfPortThis = firstForEq1.getPort_b();//本侧连接的端口
            }else if (firstForEq1 == null && firstForEq2 != null){
                odfPortThis = firstForEq2.getPort_a();
            }
            //找该端口本侧的连接装置
            if (!TextUtils.isEmpty(odfPortThis)){
                //找odf连接的本侧装置
                String[] split = odfPortThis.split("\\.");
                String unitName = split[0];//如：4n
                KM_SPCD_UNIT km_spcd_unit = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", unitName);
                OdfViewUnitPortBean odfViewUnitPortBean = new OdfViewUnitPortBean();
                odfViewUnitPortBean.setUnitName(km_spcd_unit.getDescription());
                odfViewUnitPortBean.setType(1);
                odfViewUnitPortBean.setKm_spcd_unit(km_spcd_unit);
                odfViewUnitPortBean.setPort(odfPortThis.substring(unitName.length()+1));
                odfViewUnitPortBean.setKm_spcd_port(km_spcd_port);
                odfViewUnitPortBean.setKm_spcd_port1(getPort(km_spcd_unit,odfPortThis));
                if (!TextUtils.isEmpty(odfPortThis) && odfPortThis.contains("-")){
                    odfViewUnitPortBean.setPortIdLeftAndRight(getPortId(km_spcd_cubicle,odfPortThis));
                }
                map.put(km_spcd_port.getId(),odfViewUnitPortBean);
            }else {
                //直接找intcore没找到，去cable里面接着找
                String cubicleB = km_spcd_region.getName()+"." + km_spcd_cubicle.getName();//如：R66.XLP1A
                List<String> names = new ArrayList<String>(asList("station_id","cubicleB"));
                List<String> names1 = new ArrayList<String>(asList("station_id","cubicleA"));
                List<Object> value = new ArrayList<Object>(asList(km_spcd_substation.getId(),cubicleB));
                List<KM_SPCD_CABLE> cables1 = cableDao.getListForEq(names, value);
                List<KM_SPCD_CABLE> cables2 = cableDao.getListForEq(names1, value);
                KM_SPCD_CORE km_spcd_core0 = null;
                KM_SPCD_CABLE km_spcd_cable0 = null;
                int type = 0;
                if (cables1!=null && cables1.size()!=0){
                    for (KM_SPCD_CABLE km_spcd_cable:cables1){
                        km_spcd_core0 = (KM_SPCD_CORE) coreDao.getFirstForEq("cable_id", km_spcd_cable.getId(), "port_b", odfPort);
                        if (km_spcd_core0 != null && !cableCoreList.contains(km_spcd_cable.getName()+"/"+km_spcd_core0.getNo())){
                            cableCoreList.add(km_spcd_cable.getName()+"/"+km_spcd_core0.getNo());
                            type = 1;
                            km_spcd_cable0 = km_spcd_cable;
                            break;
                        }
                    }
                }else if (cables2!=null && cables2.size()!=0){
                    for (KM_SPCD_CABLE km_spcd_cable:cables2){
                        km_spcd_core0 = (KM_SPCD_CORE) coreDao.getFirstForEq("cable_id", km_spcd_cable.getId(), "port_a", odfPort);
                        if (km_spcd_core0 != null && !cableCoreList.contains(km_spcd_cable.getName()+"/"+km_spcd_core0.getNo())){
                            cableCoreList.add(km_spcd_cable.getName()+"/"+km_spcd_core0.getNo());
                            type = 2;
                            km_spcd_cable0 = km_spcd_cable;
                            break;
                        }
                    }
                }
                if (km_spcd_core0 != null){//如果找到光缆尾缆连接到另外的光缆尾缆
                    String cubicleA = null;
                    String port_a = null;
                    if (type == 1){
                        cubicleA = km_spcd_cable0.getCubicleA();
                        port_a = km_spcd_core0.getPort_a();//对侧的连接
                    }else if (type == 2){
                        cubicleA = km_spcd_cable0.getCubicleB();
                        port_a = km_spcd_core0.getPort_b();//对侧的连接
                    }
                    //找到小室和屏柜
                    if (!TextUtils.isEmpty(cubicleA) && cubicleA.contains(".") && !TextUtils.isEmpty(port_a)){
                        String[] split = cubicleA.split("\\.");
                        KM_SPCD_REGION km_spcd_region1 = (KM_SPCD_REGION) regionDao.getFirstForEq("substation_id", 1, "name", split[0]);
                        if (km_spcd_region1!=null){
                            KM_SPCD_CUBICLE km_spcd_cubicle1 = (KM_SPCD_CUBICLE)cubicleDao.getFirstForEq("substation_id", 1, "region_id", km_spcd_region1.getId(), "name", split[1]);
                            if (km_spcd_cubicle1!=null){
                                setUnitPort(cableCoreList, km_spcd_region1,km_spcd_cubicle1,km_spcd_port,port_a,map);
                            }
                        }
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

}
