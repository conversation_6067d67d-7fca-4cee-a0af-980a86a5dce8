package com.kemov.visual.spcd.spcdvisualandroidapp.utils;


public final class Config {
    public static final String PREF_KEY = "com.spcdVisual.android";
    public static final float GL_ODF_HEIGHT = 40;
    public static final float GL_ODF_WIDTH = 70;
    public static final float GL_ODF_WIDTH_WL = 90;
    public static final float GL_ODF_CONNECT_WIDTH = 35;
    public static final float GL_WIDTH = 250;
    public static final float GL_HEIGHT = 800;
    public static final float GL_TOP = 10;
    public static final float GL_LEFT = 10;
    //odf视图
    public static final float ODF_VIEW_LEFT = 20;
    public static final float ODF_VIEW_WIDETH = 140;
    public static final float ODF_VIEW_WIDETH_FINAL = 90;
    public static float ODF_VIEW_HEIGHT_FINAL = 40;
    public static final float ODF_VIEW_WIDETH_FINAL_TRANSVERSELY = 70;
    public static float ODF_VIEW_HEIGHT_FINAL_TRANSVERSELY = 50;
    public static float ODF_VIEW_HEIGHT = 60;
    public static final float ODF_GL_WIDTH = 180;
    public static final float ODF_PORT_WIDTH = 50;
    public static final float ODF_GQ_INTERVAL = 40;
    public static final float ODF_PORT_WIDTH_INTERVAL = 10;
    public static final float ODF_PORT_WIDTH_INTERVAL_LONG = 15;
    public static final float ODF_PORT_WIDTH_UNIT = 12;
    public static final float ODF_PORT_WIDTH_PORT = 20;
    public static final float ODF_PORT_WIDTH_PORT_INTERVAL = 5;
    public static final float ODF_CUBICLE_WIDTH = 100;
    public static final float ODF_CUBICLE_WIDTH_1 = 150;
    //尾缆图上面距离
    public static final float wl_param_all = 160;
    public static final float wl_param = 80;
    //标签图尺寸
    public static final float LABEL_LEFT = 10;
    public static final float LABEL_WIDTH = 200;
    public static final float LABEL_HEIGHT = 130;//中间框的高度
    public static final float LABEL_TOP_HEIGHT = 150;//上框的高度
    public static final float LABEL_INTERVAL = 130;//上下两个框的间隔距离
    //箭头宽高
    public static final float LABEL_ARROW_HEIGH = 25;//高度
    public static final float LABEL_ARROW_WIDTH = 18;//宽度
}
