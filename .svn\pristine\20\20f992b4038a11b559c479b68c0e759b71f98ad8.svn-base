package com.kemov.visual.spcd.spcdvisualandroidapp.visualview;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.Intent;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.RectF;
import android.os.Build;
import android.os.SystemClock;
import android.support.annotation.Nullable;
import android.text.Layout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;

import com.kemov.visual.spcd.spcdvisualandroidapp.R;
import com.kemov.visual.spcd.spcdvisualandroidapp.activity.optical_fiber_visual.OpticalFiberVisualActivity;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.OdfDrawBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.OdfViewDataNewBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.OdfViewUnitPortBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.TierBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.base.Port;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.cubicle_back_panel.CubicleBackPanelConstants;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.utils.DrawUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Config;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Constants;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.PrefReader;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.PrefWriter;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class OdfNewView extends View {
    private static final String TAG = "OdfView";
    private final Context mCtx;
    private float eventX, eventY;
    public OdfViewDataNewBean odfViewDataBean = OdfViewDataNewBean.getInstance();
    List<OdfDrawBean.PortBean> portBeans = new ArrayList<>();
    TierBean mTierBean = TierBean.getInstance();

    Paint mPaint = null;
    TextPaint txtPaint = null;
    private BaseDaoImp boardDao;
    int screenWidth;
    int screenHeight;

    public OdfNewView(Context context) {
        this(context,null);
    }

    public OdfNewView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }

    public OdfNewView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr,0);
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public OdfNewView(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        mCtx = context;
        init();
    }

    private void init() {
        txtPaint = new TextPaint();
        txtPaint.setAntiAlias(true);
        //txtPaint.setStrokeWidth(DisplayUtil.dip2sp(mCtx,2));
        txtPaint.setTextAlign(Paint.Align.CENTER);
        //txtPaint.setTextSize(DisplayUtil.dip2sp(mCtx,24));

        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        //mPaint.setColor(0x7aff0000);
        mPaint.setColor(0xff000000);//0x7a008080 IED_BG_COLOR
        mPaint.setStrokeWidth(2);
        //mPaint.setStyle(Paint.Style.FILL);
        mPaint.setStyle(Paint.Style.STROKE);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
//        List<KM_SPCD_PORT> ports = odfViewDataBean.getPorts();//中间端口
//        List<OdfViewDataNewBean.CableBean> cableBeans = odfViewDataBean.getCableBeans();//左侧光缆线
//        List<OdfViewDataNewBean.CableBean> cableRightBeans = odfViewDataBean.getCableRightBeans();//右侧光缆线

        if (mTierBean == null){
            return;
        }
        List<KM_SPCD_PORT> ports = mTierBean.getPorts();//中间端口
        List<OdfViewDataNewBean.CableBean> cableBeans = mTierBean.getCableBeans();//左侧光缆线
        List<OdfViewDataNewBean.CableBean> cableRightBeans = mTierBean.getCableRightBeans();//右侧光缆线

        if (ports == null){
            return;
        }

        screenWidth = Utils.getScreenWidth(getContext());
        screenHeight = Utils.getScreenHeight(getContext());
        int navigationBarHeight = Utils.getNavigationBarHeight(getContext());//获取虚拟键盘高度
//        float beforTop = (screenHeight - navigationBarHeight - ports.size()*Config.ODF_VIEW_HEIGHT)/2;
        float beforTop = 20;

//        float beforTop = 0;
//        int usable = screenHeight - navigationBarHeight;
//        if (ports.size()*Config.ODF_VIEW_HEIGHT > usable){
//            beforTop = 10;
//            Config.ODF_VIEW_HEIGHT = usable/ports.size();
//        }else {
//            beforTop = (screenHeight - navigationBarHeight - ports.size()*Config.ODF_VIEW_HEIGHT)/2;
//        }

//        float beforTop = 10;
        float left = (screenWidth - Config.ODF_VIEW_WIDETH) / 2;
        int count = 0;
        float width1 = 0;
        float width2 = 0;

        portBeans.clear();
        if (ports!=null && ports.size()!=0){
            //获取整个图形的高度
            cubicle_height = beforTop + Config.ODF_VIEW_HEIGHT * ports.size();

            //将端口连接的线分配给端口
            List<OdfViewDataNewBean.CableBean> cableBeanList1 = getCableBeanList(ports, cableBeans);
            List<OdfViewDataNewBean.CableBean> cableBeanList2 = getCableBeanList(ports, cableRightBeans);

            for (int x=0;x<ports.size();x++){
                KM_SPCD_PORT km_spcd_port = ports.get(x);
                Integer id = km_spcd_port.getId();

                float top = beforTop+Config.ODF_VIEW_HEIGHT * count;
                OdfDrawBean.PortBean odfDrawBean = new OdfDrawBean.PortBean();
                //中间的端口框
                RectF rectF = new RectF(left, top, left + Config.ODF_VIEW_WIDETH, top + Config.ODF_VIEW_HEIGHT);
                odfDrawBean.setRectF(rectF);
                odfDrawBean.setPortName(km_spcd_port.getUsage());
                odfDrawBean.setPortId(id);
                //左侧的连线
                if (cableBeans!=null && cableBeans.size()!=0){
                    measureDraw(cableBeanList1,1,rectF,id,odfDrawBean,ports);
                }
                //右侧的连线
                if (cableRightBeans!=null && cableRightBeans.size()!=0){
                    measureDraw(cableBeanList2,2,rectF,id,odfDrawBean, ports);
                }
                portBeans.add(odfDrawBean);
                count ++;
            }

            //算出总宽度
            if (cableBeans!=null && cableBeans.size()!=0){
                width1 = cableBeans.size()* Config.ODF_PORT_WIDTH + 150;
            }
            if (cableRightBeans!=null && cableRightBeans.size()!=0){
                width2 = cableRightBeans.size() * Config.ODF_PORT_WIDTH + 150;
            }
            cubicle_width = width1 + width2 + Config.ODF_VIEW_WIDETH;
        }
    }

    public List<OdfViewDataNewBean.CableBean> getCableBeanList(List<KM_SPCD_PORT> ports, List<OdfViewDataNewBean.CableBean> cableBeans){
        List<OdfViewDataNewBean.CableBean> cableBeanList = new ArrayList<>();
        for (int x=0;x<ports.size();x++){
            KM_SPCD_PORT km_spcd_port = ports.get(x);
            if (cableBeans!=null && cableBeans.size()!=0){
                for (OdfViewDataNewBean.CableBean cableBean:cableBeans){
                    if (cableBean.getCableCoreIds().contains(km_spcd_port.getId())){
                        if (!cableBeanList.contains(cableBean)){
                            cableBeanList.add(cableBean);
                            break;
                        }
                    }
                }
            }
        }
        return cableBeanList;
    }

    private float mWidth = 0;
    private float mHeight = 0;
    private float cubicle_width = 0f;
    private float cubicle_height = 0f;
    //横向图形显示完整
    public void RequestLayout_FillWidth(){
        scrollTo(maxLeft,0);
        mScale = mWidth/(cubicle_width+ CubicleBackPanelConstants.CUBICULE_LEFT_PADDING*2);
        mPreScale = mScale;
        this.totalOffX = 0;
        this.totalOffY = 0;
        invalidate();
    }
    //所有图形显示全局
    public void RequestLayout_FillWidthHeight(){
        scrollTo(maxLeft,0);
        float mScale_w = mWidth/(cubicle_width+CubicleBackPanelConstants.CUBICULE_LEFT_PADDING*2);
        float mScale_h = mHeight/(cubicle_height+CubicleBackPanelConstants.CUBICULE_TOP_PADDING*2);
        mScale = mScale_w<mScale_h ? mScale_w : mScale_h;
        mPreScale = mScale;
        this.totalOffX = 0;
        this.totalOffY = 0;
        invalidate();
    }
    //缩放
    int maxscaleCount = 0;
    int minscaleCount = 0;
    public void SetScale(float scale){
        this.mScale *= scale;
        if (mScale > mMaxScale) {
            mScale = mMaxScale;
            maxscaleCount++;
        }else {
            maxscaleCount = 0;
        }
        if (mScale < mMinScale) {
            mScale = mMinScale;
            minscaleCount++;
        }else {
            minscaleCount = 0;
        }
        mPreScale = mScale;

        //当缩小到固定的最小倍率或者放大到最大倍率超过一次就不请求重绘
        if (maxscaleCount>1 || minscaleCount>1) {
            return;
        }else {
            invalidate();
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        mWidth = w;
        mHeight = h;
    }


    private void measureDraw(List<OdfViewDataNewBean.CableBean> cableBeans, int type, RectF rectF, int id, OdfDrawBean.PortBean odfDrawBean, List<KM_SPCD_PORT> ports) {
        for (int x =0;x<cableBeans.size();x++){
            OdfViewDataNewBean.CableBean cableBean = cableBeans.get(x);
            KM_SPCD_CABLE km_spcd_cable = cableBean.getKm_spcd_cable();
            List<Integer> coreIds = cableBean.getCableCoreIds();
            int lineColor = cableBean.getLineColor();
            Map<Integer, OdfViewUnitPortBean> map = cableBean.getMap();

            Integer lineCount = getLineCount(coreIds, id);
//            Integer max = Collections.max(coreIds);
//            Integer min = Collections.min(coreIds);
            Integer max = getMaxPortId(ports,coreIds,1);
            Integer min = getMaxPortId(ports,coreIds,2);

            float fx = 0;
            float fy = 0;
            if (lineCount == 0){//左右侧无线

            }else if (lineCount == 1){//左、右侧有1条线
                if (type == 1){//左侧
                    Port port = new Port( rectF.left,(rectF.bottom + rectF.top) / 2, null);
                    odfDrawBean.setPortLeft1(port);
                    odfDrawBean.setLineCount(1);
                    odfDrawBean.setLeftColor(lineColor);
                    odfDrawBean.setCount(x+1);
                    //用sharedPrefrence纪录开始坐标，结束坐标
//                    fx = rectF.left - Config.ODF_PORT_WIDTH - Config.ODF_PORT_WIDTH_INTERVAL*(x);
                    fx = getmFx(fx, map, id, rectF, x,1,odfDrawBean,coreIds);
                    fy = (rectF.bottom + rectF.top) / 2;
                    if (max == id){//是集合当中最大的元素,存到大坐标
                        PrefWriter.getInstance(getContext()).put(km_spcd_cable.getName()+"max",fx+"+"+fy);
                    }else if (min == id){//是集合中小的元素，存小坐标
                        PrefWriter.getInstance(getContext()).put(km_spcd_cable.getName()+"min",fx+"+"+fy);
                    }
                }else if (type == 2){//右侧
                    Port port = new Port( rectF.right,(rectF.bottom + rectF.top) / 2, null);
                    odfDrawBean.setPortRight1(port);
                    odfDrawBean.setLineRightCount(1);
                    odfDrawBean.setRightColor(lineColor);
                    odfDrawBean.setCount(x+1);
                    //用sharedPrefrence纪录开始坐标，结束坐标
//                    fx = rectF.right + Config.ODF_PORT_WIDTH + Config.ODF_PORT_WIDTH_INTERVAL*(x);
                    fx = getmFx(fx, map, id, rectF, x,2, odfDrawBean, coreIds);
                    fy = (rectF.bottom + rectF.top) / 2;
                    if (max == id){//是集合当中最大的元素,存到大坐标
                        PrefWriter.getInstance(getContext()).put(km_spcd_cable.getName()+"maxRight",fx+"+"+fy);
                    }else if (min == id){//是集合中小的元素，存小坐标
                        PrefWriter.getInstance(getContext()).put(km_spcd_cable.getName()+"minRight",fx+"+"+fy);
                    }
                }
                break;
            }else if (lineCount == 2){//左、右侧有2条线
                if (type == 1) {//左侧
                    Port port1 = new Port(rectF.left, rectF.top + Config.ODF_VIEW_HEIGHT/3, null);
                    Port port2 = new Port( rectF.left,  rectF.top + Config.ODF_VIEW_HEIGHT/3* 2,null);
                    odfDrawBean.setPortLeft1(port1);
                    odfDrawBean.setPortLeft2(port2);
                    odfDrawBean.setLineCount(2);
                    odfDrawBean.setLeftColor(lineColor);
                    odfDrawBean.setCount(x+1);
                    //用sharedPrefrence纪录开始坐标，结束坐标
//                    fx = rectF.left - Config.ODF_PORT_WIDTH - Config.ODF_PORT_WIDTH_INTERVAL*(x);
                    fx = getmFx(fx, map, id, rectF, x,1, odfDrawBean, coreIds);
                    float fy1 = rectF.top + Config.ODF_VIEW_HEIGHT/3;
                    float fy2 = rectF.top + Config.ODF_VIEW_HEIGHT/3* 2;
                    if (max == min){//最大值和最小值想等，是同一条线
                        PrefWriter.getInstance(getContext()).put(km_spcd_cable.getName()+"max",fx+"+"+fy2);
                        PrefWriter.getInstance(getContext()).put(km_spcd_cable.getName()+"min",fx+"+"+fy1);
                    }
                    if (max == id){//是集合当中最大的元素,存到大坐标
                        PrefWriter.getInstance(getContext()).put(km_spcd_cable.getName()+"max",fx+"+"+fy2);
                    }else if (min == id){//是集合中小的元素，存小坐标
                        PrefWriter.getInstance(getContext()).put(km_spcd_cable.getName()+"min",fx+"+"+fy1);
                    }
                }else if (type == 2) {//右侧
                    Port port1 = new Port(rectF.right, rectF.top + Config.ODF_VIEW_HEIGHT/3, null);
                    Port port2 = new Port( rectF.right,  rectF.top + Config.ODF_VIEW_HEIGHT/3* 2,null);
                    odfDrawBean.setPortRight1(port1);
                    odfDrawBean.setPortRight2(port2);
                    odfDrawBean.setLineRightCount(2);
                    odfDrawBean.setRightColor(lineColor);
                    odfDrawBean.setCount(x+1);
                    //用sharedPrefrence纪录开始坐标，结束坐标
//                    fx = rectF.right + Config.ODF_PORT_WIDTH + Config.ODF_PORT_WIDTH_INTERVAL*(x);
                    fx = getmFx(fx, map, id, rectF, x,2, odfDrawBean, coreIds);
                    float fy1 = rectF.top + Config.ODF_VIEW_HEIGHT/3;
                    float fy2 = rectF.top + Config.ODF_VIEW_HEIGHT/3* 2;
                    if (max == min){//最大值和最小值想等，是同一条线
                        PrefWriter.getInstance(getContext()).put(km_spcd_cable.getName()+"maxRight",fx+"+"+fy2);
                        PrefWriter.getInstance(getContext()).put(km_spcd_cable.getName()+"minRight",fx+"+"+fy1);
                    }
                    if (max == id){//是集合当中最大的元素,存到大坐标
                        PrefWriter.getInstance(getContext()).put(km_spcd_cable.getName()+"maxRight",fx+"+"+fy2);
                    }else if (min == id){//是集合中小的元素，存小坐标
                        PrefWriter.getInstance(getContext()).put(km_spcd_cable.getName()+"minRight",fx+"+"+fy1);
                    }
                }
                break;
            }
        }
    }

    public Integer getMaxPortId(List<KM_SPCD_PORT> ports, List<Integer> coreIds,Integer type){
        List<Integer> portIdList = new ArrayList<>();
        for (KM_SPCD_PORT km_spcd_port:ports){
            portIdList.add(km_spcd_port.getId());
        }
        if (type == 1){//选最大的
            //将集合里面的元素倒叙排列
            for (int x=0;x<coreIds.size();x++){
                Integer coreId = coreIds.get(coreIds.size()-x-1);
                if (portIdList.contains(coreId)){
                    return coreId;
                }
            }
        }else{//选最小的
            for (Integer coreId:coreIds){
                if (portIdList.contains(coreId)){
                    return coreId;
                }
            }
        }
        return null;
    }

    public float getmFx(float fx, Map<Integer, OdfViewUnitPortBean> map, int id, RectF rectF, int x, int type, OdfDrawBean.PortBean odfDrawBean, List<Integer> coreIds) {
        if (map!=null && map.size()!=0){
            //找出这根缆中最长的一个名称
            int unitNameLonger = 0;
            for (Integer coreId:coreIds){
                OdfViewUnitPortBean odfViewUnitPortBean = map.get(coreId);
                if (odfViewUnitPortBean != null){
                    String unitName = odfViewUnitPortBean.getUnitName();
                    int longer = 0;
                    if (!TextUtils.isEmpty(unitName)){
                        longer = unitName.length();
                    }else {
                        longer = 0;
                    }
                    if (unitNameLonger < longer){
                        unitNameLonger = longer;
                    }
                }
            }
            if (type == 1){
                odfDrawBean.setTheBestLongerUnitName(unitNameLonger);
            }else if (type == 2){
                odfDrawBean.setTheBestLongerUnitNameRight(unitNameLonger);
            }

            OdfViewUnitPortBean odfViewUnitPortBean = map.get(id);
            if (odfViewUnitPortBean == null){
                if (type == 1){
//                    fx = rectF.left - Config.ODF_PORT_WIDTH - Config.ODF_PORT_WIDTH_UNIT*unitNameLonger;
                    fx = rectF.left - getLineLength(unitNameLonger);
                }else if (type == 2){
//                    fx = rectF.right + Config.ODF_PORT_WIDTH + Config.ODF_PORT_WIDTH_UNIT*unitNameLonger;
                    fx = rectF.right + getLineLength(unitNameLonger);
                }
                return fx;
            }
            String unitName = odfViewUnitPortBean.getUnitName();
            if (unitNameLonger != 0){
                if (type == 1){
//                    fx = rectF.left - Config.ODF_PORT_WIDTH - Config.ODF_PORT_WIDTH_UNIT*unitName.length();
//                    fx = rectF.left - Config.ODF_PORT_WIDTH - Config.ODF_PORT_WIDTH_UNIT*unitNameLonger;
                    fx = rectF.left -  getLineLength(unitNameLonger);
                    odfDrawBean.setUnitName(unitName);
                    odfDrawBean.setPort(odfViewUnitPortBean.getPort());
                    odfDrawBean.setType(odfViewUnitPortBean.getType());
                    odfDrawBean.setOdfViewUnitPortBeanLeft(odfViewUnitPortBean);
                }else if (type == 2){
//                    fx = rectF.right + Config.ODF_PORT_WIDTH + Config.ODF_PORT_WIDTH_UNIT*unitName.length();
//                    fx = rectF.right + Config.ODF_PORT_WIDTH + Config.ODF_PORT_WIDTH_UNIT*unitNameLonger;
                    fx = rectF.right +  getLineLength(unitNameLonger);
                    odfDrawBean.setUnitNameRight(unitName);
                    odfDrawBean.setPortRight(odfViewUnitPortBean.getPort());
                    odfDrawBean.setTypeRight(odfViewUnitPortBean.getType());
                    odfDrawBean.setOdfViewUnitPortBeanRigth(odfViewUnitPortBean);
                }
            }else {
                if (type == 1){
                    fx = rectF.left - Config.ODF_PORT_WIDTH - Config.ODF_PORT_WIDTH_INTERVAL*(x);
                }else if (type == 2){
                    fx = rectF.right + Config.ODF_PORT_WIDTH + Config.ODF_PORT_WIDTH_INTERVAL*(x);
                }
            }
        }else {
            if (type == 1){
                fx = rectF.left - Config.ODF_PORT_WIDTH - Config.ODF_PORT_WIDTH_INTERVAL*(x);
            }else if (type == 2){
                fx = rectF.right + Config.ODF_PORT_WIDTH + Config.ODF_PORT_WIDTH_INTERVAL*(x);
            }
        }
        return fx;
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
//        RequestLayout_FillWidth();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.scale(mScale, mScale);//缩放

        //画左侧屏柜
        if (portBeans!=null && portBeans.size()!=0){
            for (OdfDrawBean.PortBean portBean:portBeans){
                //画中间的框
                canvas.drawRect(portBean.getRectF(),mPaint);
                txtPaint.setTextSize(18);
                txtPaint.setColor(getResources().getColor(R.color.black));
                DrawUtils.textCenter(portBean.getPortName(),txtPaint,canvas,portBean.getRectF(),
                        Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);
                //画左侧的连线
                if (portBean.getPortLeft1()!=null){
                    Port portLeft1 = portBean.getPortLeft1();
                    Port portLeft2 = portBean.getPortLeft2();
                    drawLeftPort(canvas,portLeft1,portLeft2,1,portBean.getLeftColor(),portBean.getCount(),portBean);
                    //画线上的R,T,RX
                    drawLeftPortBean(canvas,portLeft1,portLeft2,portBean,1);
                }

                //画右侧的连线
                if (portBean.getPortRight1()!=null){
                    Port portRight1 = portBean.getPortRight1();
                    Port portRight2 = portBean.getPortRight2();
                    drawLeftPort(canvas,portRight1,portRight2,2, portBean.getRightColor(),portBean.getCount(),portBean);
                    //画线上的R,T,RX
                    drawLeftPortBean(canvas,portRight1,portRight2,portBean,2);
                }
            }
//            List<OdfViewDataNewBean.CableBean> cableBeans = odfViewDataBean.getCableBeans();//左侧光缆线
//            List<OdfViewDataNewBean.CableBean> cableRightBeans = odfViewDataBean.getCableRightBeans();//左侧光缆线

            List<OdfViewDataNewBean.CableBean> cableBeans = mTierBean.getCableBeans();//左侧光缆线
            List<OdfViewDataNewBean.CableBean> cableRightBeans = mTierBean.getCableRightBeans();//左侧光缆线
            drawGl(canvas,cableBeans,1);
            drawGl(canvas,cableRightBeans,2);
        }
    }

    private void drawGl(Canvas canvas, List<OdfViewDataNewBean.CableBean> cableBeans,int type) {
        if (cableBeans!=null && cableBeans.size()!=0){
            for (OdfViewDataNewBean.CableBean cableBean:cableBeans){
                int type0 = cableBean.getType();
                String name = null;
                KM_SPCD_CABLE km_spcd_cable = null;
                KM_SPCD_INTCORE km_spcd_intcore = null;
                if (type0 == 1){
                    km_spcd_intcore = cableBean.getKm_spcd_intcore();
                    name = cableBean.getKm_spcd_intcore().getName();
                    km_spcd_cable = cableBean.getKm_spcd_cable();
                }else {
                    km_spcd_cable = cableBean.getKm_spcd_cable();
                    name = km_spcd_cable.getName();
                }

                String max = null;
                String min = null;
                if (type == 1){
                    max = PrefReader.getInstance(getContext()).get(name + "max", null);
                    min = PrefReader.getInstance(getContext()).get(name + "min", null);
//                    PrefWriter.getInstance(getContext()).remove(name + "max");
//                    PrefWriter.getInstance(getContext()).remove(name + "min");
                }else if (type == 2){
                    max = PrefReader.getInstance(getContext()).get(name + "maxRight", null);
                    min = PrefReader.getInstance(getContext()).get(name + "minRight", null);
//                    PrefWriter.getInstance(getContext()).remove(name + "maxRight");
//                    PrefWriter.getInstance(getContext()).remove(name + "minRight");
                }
                if (!TextUtils.isEmpty(max) && !TextUtils.isEmpty(min)){
                    //画连接线
                    PointF tmA1,tmA2,tmA3 = null,tmA4 = null;
                    float x1 = Float.parseFloat(min.substring(0, min.indexOf("+")));
                    float y1 = Float.parseFloat(min.substring(min.indexOf("+")+1,min.length()));
                    tmA1= new PointF(x1,y1);
                    float x2 = Float.parseFloat(max.substring(0, max.indexOf("+")));
                    float y2 = Float.parseFloat(max.substring(max.indexOf("+")+1,max.length()));
                    tmA2= new PointF(x2,y2);
                    drawLines(cableBean.getLineColor(),canvas,tmA1,tmA2);
                    //画光纤
                    txtPaint.setColor(getResources().getColor(cableBean.getLineColor()));
                    if (type == 1){
                        tmA3= new PointF(x1,(y1+y2)/2);
                        tmA4= new PointF(x1 - Config.ODF_GL_WIDTH,(y1+y2)/2);
                        if (type0 == 1){
                            canvas.drawText(km_spcd_intcore.getName(),x1-Config.ODF_CUBICLE_WIDTH,(y1+y2)/2 - 5,txtPaint);
                            //画光纤下面的对侧屏柜名称
//                            canvas.drawText(km_spcd_intcore.getDescription(),x1-Config.ODF_CUBICLE_WIDTH,(y1+y2)/2 + 20,txtPaint);
                        }else{
                            canvas.drawText(km_spcd_cable.getName(),x1-Config.ODF_CUBICLE_WIDTH,(y1+y2)/2 - 5,txtPaint);
                            //画光纤下面的对侧屏柜名称
                            canvas.drawText(km_spcd_cable.getDescription(),x1-Config.ODF_CUBICLE_WIDTH,(y1+y2)/2 + 20,txtPaint);
                        }
                    }else if (type == 2){
                        tmA3= new PointF(x1,(y1+y2)/2);
                        tmA4= new PointF(x1 + Config.ODF_GL_WIDTH,(y1+y2)/2);
                        if (type0 == 1){
                            canvas.drawText(km_spcd_intcore.getName(),x1+Config.ODF_CUBICLE_WIDTH,(y1+y2)/2 - 5,txtPaint);
                        }else {
                            canvas.drawText(km_spcd_cable.getName(),x1+Config.ODF_CUBICLE_WIDTH,(y1+y2)/2 - 5,txtPaint);
                            //画光纤下面的对侧屏柜名称
                            canvas.drawText(km_spcd_cable.getDescription(),x1+Config.ODF_CUBICLE_WIDTH,(y1+y2)/2 + 20,txtPaint);
                        }
                    }
                    txtPaint.setTextAlign(Paint.Align.CENTER);
                    drawLines(cableBean.getLineColor(),canvas,tmA3,tmA4);
                }
            }
        }
    }


    private void drawLeftPortBean(Canvas canvas, Port portLeft1, Port portLeft2, OdfDrawBean.PortBean portBean,int type) {
        if (type == 1){
            if (portBean.getLineCount()!=null){
                if (portBean.getLineCount() == 1){
                    if (!TextUtils.isEmpty(portBean.getUnitName())){
                        txtPaint.setTextAlign(Paint.Align.CENTER);
                        canvas.drawText(portBean.getUnitName(),getPortStrFx(portLeft1,portBean,1),portLeft1.y-Config.ODF_PORT_WIDTH_PORT_INTERVAL,txtPaint);
                        canvas.drawText(portBean.getPort(),getPortStrFx(portLeft1,portBean,1),portLeft1.y + Config.ODF_PORT_WIDTH_PORT,txtPaint);
                        if (portBean.getType()!=null && portBean.getType() == 1 && portBean.getOdfViewUnitPortBeanLeft().getIntcore()!=null){
                            //画光纤的名字和屏柜
                            txtPaint.setTextAlign(Paint.Align.RIGHT);
                            canvas.drawText(portBean.getOdfViewUnitPortBeanLeft().getIntcore().getName(),getPortStrFx(portLeft1,portBean,1)- Config.ODF_CUBICLE_WIDTH_1,portLeft1.y-Config.ODF_PORT_WIDTH_PORT_INTERVAL,txtPaint);
                            canvas.drawText(portBean.getOdfViewUnitPortBeanLeft().getKm_spcd_cubicle().getDescription(),getPortStrFx(portLeft1,portBean,1) - Config.ODF_CUBICLE_WIDTH_1,portLeft1.y + Config.ODF_PORT_WIDTH_PORT,txtPaint);
                        }
                    }
                }else if (portBean.getLineCount() == 2){
                    canvas.drawText("R",portLeft1.x - Config.ODF_PORT_WIDTH/2,portLeft1.y,txtPaint);
                    canvas.drawText("T",portLeft2.x - Config.ODF_PORT_WIDTH/2,portLeft2.y,txtPaint);
                }
            }
        }else if (type == 2){
            if (portBean.getLineRightCount()!=null){
                if (portBean.getLineRightCount() == 1){
                    if (!TextUtils.isEmpty(portBean.getUnitNameRight())){
                        txtPaint.setTextAlign(Paint.Align.CENTER);
                        canvas.drawText(portBean.getUnitNameRight(),getPortStrFx(portLeft1,portBean,2),portLeft1.y-Config.ODF_PORT_WIDTH_PORT_INTERVAL,txtPaint);
                        canvas.drawText(portBean.getPortRight(),getPortStrFx(portLeft1,portBean,2),portLeft1.y + Config.ODF_PORT_WIDTH_PORT,txtPaint);
                        if (portBean.getTypeRight()!=null && portBean.getTypeRight() == 1 && portBean.getOdfViewUnitPortBeanRigth().getIntcore()!=null){
                            //画光纤的名字和屏柜
                            txtPaint.setTextAlign(Paint.Align.LEFT);
                            canvas.drawText(portBean.getOdfViewUnitPortBeanRigth().getIntcore().getName(),getPortStrFx(portLeft1,portBean,2) + Config.ODF_CUBICLE_WIDTH_1,portLeft1.y-Config.ODF_PORT_WIDTH_PORT_INTERVAL,txtPaint);
                            canvas.drawText(portBean.getOdfViewUnitPortBeanRigth().getKm_spcd_cubicle().getDescription(),getPortStrFx(portLeft1,portBean,2) + Config.ODF_CUBICLE_WIDTH_1,portLeft1.y + Config.ODF_PORT_WIDTH_PORT,txtPaint);
                        }
                    }
                }else if (portBean.getLineRightCount() == 2){
                    canvas.drawText("R",portLeft1.x + Config.ODF_PORT_WIDTH/2,portLeft1.y,txtPaint);
                    canvas.drawText("T",portLeft2.x + Config.ODF_PORT_WIDTH/2,portLeft2.y,txtPaint);
                }
            }
        }
    }

    public float getPortStrFx(Port portLeft1,OdfDrawBean.PortBean portBean,int type){
        float fx = 0;
        String unitName = portBean.getUnitName();
        if (!TextUtils.isEmpty(unitName)){
            if (type == 1){
                fx = portLeft1.x - (Config.ODF_PORT_WIDTH + Config.ODF_PORT_WIDTH_UNIT*unitName.length())/2;
            }else {
                fx = portLeft1.x + (Config.ODF_PORT_WIDTH + Config.ODF_PORT_WIDTH_UNIT*unitName.length())/2;
            }
        }else {
            if (type == 1){
                fx = portLeft1.x - Config.ODF_PORT_WIDTH/2;
            }else {
                fx = portLeft1.x + Config.ODF_PORT_WIDTH/2;
            }
        }
        return fx;
    }

    private void drawLeftPort(Canvas canvas, Port portLeft1, Port portLeft2, int type, int leftColor, int count,OdfDrawBean.PortBean portBean) {
        if (portLeft1!=null){
            usePointDrawLine(canvas,portLeft1,type,leftColor,count,portBean);
            if (portLeft2!=null){
                usePointDrawLine(canvas,portLeft2,type,leftColor,count,portBean);
            }
        }
    }

    public float getLineLength(int theBestLongerUnitName){
        float v = Config.ODF_PORT_WIDTH + Config.ODF_PORT_WIDTH_UNIT * theBestLongerUnitName;
        return v;
    }

    public float getLineLength1(int theBestLongerUnitName){
        float v = Config.ODF_PORT_WIDTH  - Config.ODF_PORT_WIDTH_INTERVAL*theBestLongerUnitName;
        return v;
    }

    int maxLeft = 0;
    private void usePointDrawLine(Canvas canvas, Port portLeft1, int type, int leftColor, int count,OdfDrawBean.PortBean portBean) {
        PointF tmA1 = null,tmA2 = null;
        int v = 0;
        Integer theBestLongerUnitName = portBean.getTheBestLongerUnitName();
        Integer theBestLongerUnitNameRight = portBean.getTheBestLongerUnitNameRight();
        if (type == 1){
            tmA1= new PointF(portLeft1.x,portLeft1.y);
            if (theBestLongerUnitName!=null && theBestLongerUnitName != 0){
//                tmA2= new PointF(portLeft1.x - Config.ODF_PORT_WIDTH - Config.ODF_PORT_WIDTH_UNIT*theBestLongerUnitName,portLeft1.y);
//                v = (int)(portLeft1.x - Config.ODF_PORT_WIDTH  - Config.ODF_PORT_WIDTH_INTERVAL*theBestLongerUnitName);
                if (portBean.getType() !=null && portBean.getType() ==1){//如果左侧是光纤，线要加长
                    OdfViewUnitPortBean odfViewUnitPortBeanLeft = portBean.getOdfViewUnitPortBeanLeft();
                    KM_SPCD_CUBICLE km_spcd_cubicle = odfViewUnitPortBeanLeft.getKm_spcd_cubicle();
                    if (km_spcd_cubicle!=null && !TextUtils.isEmpty(km_spcd_cubicle.getName())){
                        tmA2= new PointF(portLeft1.x - getLineLength(theBestLongerUnitName) - Config.ODF_PORT_WIDTH_UNIT * km_spcd_cubicle.getName().length()- Config.ODF_CUBICLE_WIDTH_1,portLeft1.y);
                    }else {
                        tmA2= new PointF(portLeft1.x - getLineLength(theBestLongerUnitName),portLeft1.y);
                    }
                }else {
                    tmA2= new PointF(portLeft1.x - getLineLength(theBestLongerUnitName),portLeft1.y);
                }
                v = (int)(portLeft1.x - getLineLength1(theBestLongerUnitName));
            }else {
                tmA2= new PointF(portLeft1.x - Config.ODF_PORT_WIDTH - Config.ODF_PORT_WIDTH_INTERVAL*(count-1),portLeft1.y);
                v = (int)(portLeft1.x - Config.ODF_PORT_WIDTH  - Config.ODF_PORT_WIDTH_INTERVAL*(count-1));
            }
        }else if (type == 2){
            tmA1= new PointF(portLeft1.x,portLeft1.y);
            if (theBestLongerUnitNameRight!=null && theBestLongerUnitNameRight != 0){
//                tmA2= new PointF(portLeft1.x + Config.ODF_PORT_WIDTH + Config.ODF_PORT_WIDTH_UNIT*theBestLongerUnitNameRight,portLeft1.y);
//                v = (int)(portLeft1.x + Config.ODF_PORT_WIDTH  + Config.ODF_PORT_WIDTH_UNIT*theBestLongerUnitNameRight);
                if (portBean.getTypeRight() !=null && portBean.getTypeRight() ==1) {//如果右侧是光纤，线要加长
                    OdfViewUnitPortBean odfViewUnitPortBeanRigth = portBean.getOdfViewUnitPortBeanRigth();
                    KM_SPCD_CUBICLE km_spcd_cubicle = odfViewUnitPortBeanRigth.getKm_spcd_cubicle();
                    if (km_spcd_cubicle!=null && !TextUtils.isEmpty(km_spcd_cubicle.getName())){
                        tmA2= new PointF(portLeft1.x + getLineLength(theBestLongerUnitNameRight) + Config.ODF_PORT_WIDTH_UNIT * km_spcd_cubicle.getName().length()+ Config.ODF_CUBICLE_WIDTH_1,portLeft1.y);
                    }else {
                        tmA2= new PointF(portLeft1.x + getLineLength(theBestLongerUnitNameRight),portLeft1.y);
                    }
                }else {
                    tmA2= new PointF(portLeft1.x + getLineLength(theBestLongerUnitNameRight),portLeft1.y);
                }
                v = (int)(portLeft1.x + getLineLength(theBestLongerUnitNameRight));
            }else{
                tmA2= new PointF(portLeft1.x + Config.ODF_PORT_WIDTH + Config.ODF_PORT_WIDTH_INTERVAL*(count-1),portLeft1.y);
                v = (int)(portLeft1.x + Config.ODF_PORT_WIDTH  + Config.ODF_PORT_WIDTH_INTERVAL*(count-1));
            }
        }
        if (v < maxLeft){
            maxLeft = v;
        }
        drawLines(leftColor,canvas,tmA1,tmA2);
    }

    private Integer getLineCount(List<Integer> coreIds, Integer id) {
        int count = 0;
        for (Integer coreId:coreIds){
            if (coreId.intValue() == id.intValue()){
                count ++;
            }
        }
        return count;
    }

    private void drawLines(int color,Canvas canvas, PointF... pointFs){
        if (pointFs.length<2) return;

        float [] floats = new float[(pointFs.length-1)*4];
        for (int i = 0; i < pointFs.length-1; i++) {
            PointF p1 = pointFs[i];
            PointF p2 = pointFs[i+1];
            floats[i*4] = p1.x;
            floats[i*4+1] = p1.y;
            floats[i*4+2] = p2.x;
            floats[i*4+3] = p2.y;
        }
        if (color!=0){
            mPaint.setColor(getResources().getColor(color));
        }
        canvas.drawLines(floats,mPaint);
        mPaint.setColor(getResources().getColor(R.color.black));
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        PointF curPointF = new PointF(event.getX(),event.getY());

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                /*eventX = (int) event.getX()-mWidth/2;
                eventY = (int) event.getY()-mHeight/2;*/
                eventX = (int) event.getX();
                eventY = (int) event.getY();
                Log.e("out for", "eventX,eventY="+eventX+" , "+eventY);
                try {
                    synchronized (event) {
                        /*for(ExtIedBean extIed : extIedBeans){
                            if(extIed.isOnTouch(eventX, eventY, totalOffX, totalOffY, mScale)){
                                //mPaint.setColor(Color.RED);
                                //双击触发 跳转操作
                                doubleClickChangeIED(extIed);
                                break;
                            }
                        }*/
                    }
                } catch (Exception e) {
                    Log.e(TAG, e.toString());
                }

                //当点击到插件上，双击弹出对话框
                doubleClickOnPlugSvg();

                break;

            default:
                break;
        }
        DargAndZoom(event);
        invalidate();
        return true;
        //return super.onTouchEvent(event);
    }


    //Dragging
    private static final int NONE = 0;
    private static final int DRAG = 1;
    private static final int ZOOM = 2;
    private int mode = NONE;

    private PointF startPoint = new PointF();
    private PointF middlePoint = new PointF();

    private float oriDis = 1f;//初始的俩指触点间距
    private float mScale = 1.0f;
    private float mPreScale = 1.0f;
    private float mMinScale = 0.2f;
    private float mMaxScale = 3.0f;

    private void DargAndZoom(MotionEvent event){
        //middlePoint = null;
        switch (event.getAction()&event.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
                startPoint.set(event.getX(), event.getY());
                mode = DRAG;
                break;
            case MotionEvent.ACTION_POINTER_DOWN:
                oriDis = DrawUtils.distance(event);
                if (oriDis > 10f) {
                    middlePoint = DrawUtils.mid(event,null);
                    mode = ZOOM;
                }

                break;
            case MotionEvent.ACTION_POINTER_UP:
                mPreScale = mScale;
                //if (event.getPointerCount()<2) mode = DRAG;
                break;

            case MotionEvent.ACTION_MOVE:

                if (mode == DRAG) {
                    onDrag(event);
                } else if((mode == ZOOM)){
                    onZoom(event);
                }
                if (mode == DRAG) {
                    startPoint.set(event.getX(), event.getY());
                }
                getParent().requestDisallowInterceptTouchEvent(true);
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                mode = NONE;
                break;
            default:
                break;
        }
    }

    private float totalOffX = 0;
    private float totalOffY = 0;
    float offX = 0;
    float offY = 0;
    private void onDrag(MotionEvent event) {
        //边界处理：(以中心IED的中心不超过View边界为参考)
        offX = event.getX() - startPoint.x;
        offY = event.getY() - startPoint.y;

        scrollBy(-(int)offX, -(int)offY);
        totalOffX += offX;
        totalOffY += offY;
        //mMatrix.setTranslate(totalOffX,totalOffY);
    }

    /**
     * 把View上的点的坐标转化为Canvas上的坐标点 for拾取
     * @return
     */
    //float newDist = 0;
    private void onZoom(MotionEvent event) {
        float newDist = DrawUtils.distance(event);
        if (newDist > 10f) {
            float scale = newDist/oriDis;

            mScale = scale * mPreScale;
            if (mScale > mMaxScale) {
                mScale = mMaxScale;
            }
            if (mScale < mMinScale) {
                mScale = mMinScale;
            }
        }
    }


    //存储时间的数组
    long[] mHitsPlugSvg = new long[2];
    private void doubleClickOnPlugSvg() {
        System.arraycopy(mHitsPlugSvg, 1, mHitsPlugSvg, 0, mHitsPlugSvg.length - 1);
        mHitsPlugSvg[mHitsPlugSvg.length - 1] = SystemClock.uptimeMillis();
        //双击事件的时间间隔500ms
        if (mHitsPlugSvg[0] >= (SystemClock.uptimeMillis() - Constants.CONSTANT_DOUBLE_CLICK_INTERVAL_MS)) {
            for (OdfDrawBean.PortBean portBean : portBeans) {
                boolean isOnTouch = portBean.isOnTouch(eventX, eventY, totalOffX, totalOffY, mScale);
                if (isOnTouch){
                    Intent intent = new Intent();
                    intent.setClass(mCtx, OpticalFiberVisualActivity.class);
                    intent.putExtra("portID",portBean.getPortId());
                    intent.putExtra("type",4);
                    mCtx.startActivity(intent);
                }
            }
        }
    }

}
