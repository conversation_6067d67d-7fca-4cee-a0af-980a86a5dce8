package com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool;

import android.content.Context;
import android.graphics.PointF;
import android.graphics.RectF;

import com.kemov.visual.spcd.spcdvisualandroidapp.activity.ied_whole_circuit.HandleForWholeCircuits;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.base.Port;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit.CircuitConstants;

import java.util.ArrayList;
import java.util.List;

public class IEDsWholeCircuitDataPool {
    KM_SPCD_CUBICLE cubicle1;
    KM_SPCD_CUBICLE cubicle2;
    KM_SPCD_UNIT ied1;
    KM_SPCD_UNIT ied2;
    KM_SPCD_UNIT odf1;
    KM_SPCD_UNIT odf2;

    public static final int TYPE_1IED_IN_1CUBICULE_BY_ODF = 1;//不同屏柜通过odf相连
    public static final int TYPE_2IED_IN_1CUBICULE = 2;//屏柜内两个ied直连
    public static final int TYPE_1IED_IN_1CUBICULE_STRAIT_THR = 3;//不同屏柜直连
    int link_type =TYPE_1IED_IN_1CUBICULE_BY_ODF;

    List<CubicleGroupBaseBean> cubicles = new ArrayList<>();
    List<String> glNames = new ArrayList<>();
    List<String> txNames = new ArrayList<>();//跳纤(ied-odf)
    List<String> ied2ied_TxName = new ArrayList<>();//跳纤（1个屏柜中的两个装置直接相连）
    List<String> ied2ied_WLName = new ArrayList<>();//尾缆（两个屏柜中的两个装置直接相连）


    private static IEDsWholeCircuitDataPool sInstance = null;

    private IEDsWholeCircuitDataPool() {
    }

    public synchronized static IEDsWholeCircuitDataPool getInstance() {
        if (sInstance == null) {
            sInstance = new IEDsWholeCircuitDataPool();
        }
        return sInstance;
    }

    public void setPortId(Context mCtx, String dbName, int portId){
        //HandleForWholeCircuits handleForWholeCircuits = new HandleForWholeCircuits(mCtx,dbName);
        //handleForWholeCircuits.getPortData(portId,3);

        cubicles.clear();
        glNames.clear();
        txNames.clear();
        ied2ied_TxName.clear();
        link_type = TYPE_1IED_IN_1CUBICULE_BY_ODF;
        setIEDs_TYPE_1IED_IN_1CUBICULE_BY_ODF();
    }

    synchronized public void setIEDs(KM_SPCD_UNIT ied1, KM_SPCD_UNIT ied2){
        this.ied1 = ied1;
        this.ied2 = ied2;

        cubicles.clear();
        glNames.clear();
        txNames.clear();
        ied2ied_TxName.clear();

        //todo:一系列的操作...(找到两个ied之间的关系)
        link_type = TYPE_1IED_IN_1CUBICULE_BY_ODF;
        //ylink_type = TYPE_2IED_IN_1CUBICULE;
        //link_type = TYPE_1IED_IN_1CUBICULE_STRAIT_THR;

        switch (link_type){
            case TYPE_1IED_IN_1CUBICULE_BY_ODF:
                setIEDs_TYPE_1IED_IN_1CUBICULE_BY_ODF_Test();//测试
                setIEDs_TYPE_1IED_IN_1CUBICULE_BY_ODF();
                break;
            case TYPE_2IED_IN_1CUBICULE:
                setIEDs_TYPE_2IED_IN_1CUBICULE_Test();//测试
                setIEDs_TYPE_2IED_IN_1CUBICULE();
                break;
            case TYPE_1IED_IN_1CUBICULE_STRAIT_THR:
                setIEDs_TYPE_1IED_IN_1CUBICULE_STRAIT_THR_Test();//测试
                setIEDs_TYPE_1IED_IN_1CUBICULE_STRAIT_THR();
                break;
            default:
                break;
        }

        //TODO 传入参数后，数据校验！
    }

    private void setIEDs_TYPE_1IED_IN_1CUBICULE_STRAIT_THR() {
        List<IED> ieds1 = new ArrayList<>();
        IED e1 = new IED();
        e1.iedName = "装置1";
        e1.ied_portNo = "1-A";
        ieds1.add(e1);
        List<IED> ieds2 = new ArrayList<>();//ieds2 为空！
        IED e2 = new IED();
        e2.iedName = "装置2";
        e2.ied_portNo = "2-A";
        ieds1.add(e2);
        CubicleGroupBaseBean cubicleGroupBean1 = new CubicleGroupBeanDoubleIEDsDirect(
                "屏柜1",ieds1,CubicleLocation.DOUBLE_FROM);
        CubicleGroupBaseBean cubicleGroupBean3 = new CubicleGroupBeanDoubleIEDsDirect(
                "屏柜3",ieds2,CubicleLocation.DOUBLE_TO);
        cubicleGroupBean1.addIed(e1);
        cubicleGroupBean3.addIed(e2);

        cubicles.add(cubicleGroupBean1);
        cubicles.add(cubicleGroupBean3);
        ied2ied_WLName.add("WL101(12-1)");
    }

    private void setIEDs_TYPE_2IED_IN_1CUBICULE() {
        List<IED> ieds1 = new ArrayList<>();
        IED e1 = new IED();
        e1.iedName = "装置1";
        e1.ied_portNo = "1-A";
        ieds1.add(e1);
        List<IED> ieds2 = new ArrayList<>();
        IED e2 = new IED();
        e2.iedName = "装置2";
        e2.ied_portNo = "2-A";
        ieds1.add(e2);
        CubicleGroupBaseBean cubicleGroupBean1 = new CubicleGroupBeanSingle(
                "屏柜1",ieds1);

        /*cubicleGroupBean1.addIed(e1);
        cubicleGroupBean1.addIed(e2);*/

        cubicles.add(cubicleGroupBean1);

        ied2ied_TxName.add("ied1-ied2_TEST");
    }

    private void setIEDs_TYPE_1IED_IN_1CUBICULE_BY_ODF() {
        List<IED> ieds1 = new ArrayList<>();
        IED e1 = new IED();
        e1.iedName = "装置1";
        e1.ied_portNo = "1-A";
        ieds1.add(e1);
        List<IED> ieds2 = new ArrayList<>();//ieds2 为空！
        IED e2 = new IED();
        e2.iedName = "装置2";
        e2.ied_portNo = "2-A";
        ieds1.add(e2);
        CubicleGroupBaseBean cubicleGroupBean1 = new CubicleGroupBeanMore(
                "屏柜1",ieds1,CubicleLocation.FROM,"ODF1","A01","A02");
        CubicleGroupBaseBean cubicleGroupBean2 = new CubicleGroupBeanMore(
                "屏柜2",ieds2,CubicleLocation.PASS,"ODF2","B01","B02");
        CubicleGroupBaseBean cubicleGroupBean3 = new CubicleGroupBeanMore(
                "屏柜3",ieds2,CubicleLocation.TO,"ODF3","C01","C02");
        cubicleGroupBean1.addIed(e1);
        cubicleGroupBean3.addIed(e2);

        cubicles.add(cubicleGroupBean1);
        cubicles.add(cubicleGroupBean2);
        cubicles.add(cubicleGroupBean2);
        cubicles.add(cubicleGroupBean2);
        cubicles.add(cubicleGroupBean3);
        glNames.add("GL101(12-1)");
        glNames.add("GL102(12-2)");
        glNames.add("GL103(12-3)");
        glNames.add("GL104(12-4)");
        txNames.add("TX007(01-02)");
        txNames.add("TX003(03-04)");
    }

    @Deprecated
    private void setIEDs_TYPE_1IED_IN_1CUBICULE_STRAIT_THR_Test() {
        List<IED> ieds1 = new ArrayList<>();
        IED e1 = new IED();
        e1.iedName = "装置1";
        e1.ied_portNo = "1-A";
        ieds1.add(e1);
        List<IED> ieds2 = new ArrayList<>();//ieds2 为空！
        IED e2 = new IED();
        e2.iedName = "装置2";
        e2.ied_portNo = "2-A";
        ieds1.add(e2);
        CubicleGroupBaseBean cubicleGroupBean1 = new CubicleGroupBeanDoubleIEDsDirect(
                "屏柜1",ieds1,CubicleLocation.DOUBLE_FROM);
        CubicleGroupBaseBean cubicleGroupBean3 = new CubicleGroupBeanDoubleIEDsDirect(
                "屏柜3",ieds2,CubicleLocation.DOUBLE_TO);
        cubicleGroupBean1.addIed(e1);
        cubicleGroupBean3.addIed(e2);

        cubicles.add(cubicleGroupBean1);
        cubicles.add(cubicleGroupBean3);
        ied2ied_WLName.add("WL101(12-1)");
    }

    @Deprecated
    private void setIEDs_TYPE_2IED_IN_1CUBICULE_Test() {
        List<IED> ieds1 = new ArrayList<>();
        IED e1 = new IED();
        e1.iedName = "装置1";
        e1.ied_portNo = "1-A";
        ieds1.add(e1);
        List<IED> ieds2 = new ArrayList<>();
        IED e2 = new IED();
        e2.iedName = "装置2";
        e2.ied_portNo = "2-A";
        ieds1.add(e2);
        CubicleGroupBaseBean cubicleGroupBean1 = new CubicleGroupBeanSingle(
                "屏柜1",ieds1);

        /*cubicleGroupBean1.addIed(e1);
        cubicleGroupBean1.addIed(e2);*/

        cubicles.add(cubicleGroupBean1);

        ied2ied_TxName.add("ied1-ied2_TEST");
    }

    @Deprecated
    private void setIEDs_TYPE_1IED_IN_1CUBICULE_BY_ODF_Test() {
        List<IED> ieds1 = new ArrayList<>();
        IED e1 = new IED();
        e1.iedName = "装置1";
        e1.ied_portNo = "1-A";
        ieds1.add(e1);
        List<IED> ieds2 = new ArrayList<>();//ieds2 为空！
        IED e2 = new IED();
        e2.iedName = "装置2";
        e2.ied_portNo = "2-A";
        ieds1.add(e2);
        CubicleGroupBaseBean cubicleGroupBean1 = new CubicleGroupBeanMore(
                "屏柜1",ieds1,CubicleLocation.FROM,"ODF1","A01","A02");
        CubicleGroupBaseBean cubicleGroupBean2 = new CubicleGroupBeanMore(
                "屏柜2",ieds2,CubicleLocation.PASS,"ODF2","B01","B02");
        CubicleGroupBaseBean cubicleGroupBean3 = new CubicleGroupBeanMore(
                "屏柜3",ieds2,CubicleLocation.TO,"ODF3","C01","C02");
        cubicleGroupBean1.addIed(e1);
        cubicleGroupBean3.addIed(e2);

        cubicles.add(cubicleGroupBean1);
        cubicles.add(cubicleGroupBean2);
        cubicles.add(cubicleGroupBean2);
        cubicles.add(cubicleGroupBean2);
        cubicles.add(cubicleGroupBean3);
        glNames.add("GL101(12-1)");
        glNames.add("GL102(12-2)");
        glNames.add("GL103(12-3)");
        glNames.add("GL104(12-4)");
        txNames.add("TX007(01-02)");
        txNames.add("TX003(03-04)");
    }

    public int getLink_type() {
        return link_type;
    }

    public List<CubicleGroupBaseBean> getCubicles(){
        return cubicles;
    }
    public List<String> getGLs(){
        return glNames;
    }

    public List<String> getTXs(){
        return txNames;
    }

    public List<String> getIed2ied_TxName() {
        return ied2ied_TxName;
    }

    public List<String> getIed2ied_WLName() {
        return ied2ied_WLName;
    }

    public RectF getCubicleRectFByIndex(int index){
        if (index<0) throw new IndexOutOfBoundsException("index 越界");
        if (index==0){
            return new RectF(CircuitConstants.CUBICULE_PADDING_LEFT, CircuitConstants.CUBICULE_PADDING_TOP,
                    CircuitConstants.CUBICULE_PADDING_LEFT + CircuitConstants.CUBICULE_WIDTH, CircuitConstants.CUBICULE_PADDING_TOP+CircuitConstants.CUBICULE_HEIGHT);
        }
        RectF pre = getCubicleRectFByIndex(index-1);
        RectF rectF = new RectF(pre.right+CircuitConstants.CUBICULE_SPACING_HORIZON,
                pre.top,
                pre.right+CircuitConstants.CUBICULE_SPACING_HORIZON+CircuitConstants.CUBICULE_WIDTH,
                pre.bottom);
        return rectF;
    }

    public RectF getCubicleIedRectByIndex(int index_cubicle, int index_ied){
        if (index_ied<0) throw new IndexOutOfBoundsException("index_ied 越界");
        RectF cubicleRect = getCubicleRectFByIndex(index_cubicle);
        if (index_ied==0){
            return new RectF(cubicleRect.left+CircuitConstants.DEVICE_PADDING_LEFT_OR_RIGHT,
                    cubicleRect.top+CircuitConstants.CUBICULE_NAME_HEIGHT+CircuitConstants.DEVICE_PADDING_TOP,
                    cubicleRect.right-CircuitConstants.DEVICE_PADDING_LEFT_OR_RIGHT,
                    cubicleRect.top+CircuitConstants.CUBICULE_NAME_HEIGHT+CircuitConstants.DEVICE_PADDING_TOP +CircuitConstants.CUBICULE_DEVICE_HEIGHT
                    );
        }
        RectF pre = getCubicleIedRectByIndex(index_cubicle,index_ied-1);
        RectF rectF = new RectF(pre.left,
                pre.bottom + CircuitConstants.DEVICEs_SPACING_VERTICAL,
                pre.right,
                pre.bottom + CircuitConstants.DEVICEs_SPACING_VERTICAL + CircuitConstants.CUBICULE_DEVICE_HEIGHT);
        return rectF;
    }

    public Port getCubicleIedPortByIndex(int index_cubicle, int index_ied){
        if (index_ied<0) throw new IndexOutOfBoundsException("index_ied 越界");
        RectF ied_rect = getCubicleIedRectByIndex(index_cubicle,index_ied);
        return new Port(
                new PointF(ied_rect.centerX()+ied_rect.width()/5,ied_rect.centerY()),
                cubicles.get(index_cubicle).getIeds().get(index_ied).ied_portNo);
    }

    //要被三种类型的屏柜所继承：1单个的；2 多个的；3两个屏柜的ied直接连接的。
    public static class CubicleGroupBaseBean {
        String cubicleName;
        List<IED> ieds;
        CubicleLocation locationOf;

        String odf_name;
        String odf_portToNo;
        String odf_portFrNo;

        public CubicleGroupBaseBean(){
        }

        public CubicleGroupBaseBean(String cubicleName, List<IED> ieds, CubicleLocation locationOf) {
            this.cubicleName = cubicleName;
            this.ieds = ieds;
            this.locationOf = locationOf;
        }

        public String getCubicleName() {
            return cubicleName;
        }

        public List<IED> getIeds() {
            return ieds;
        }
        public void addIed(IED ied){
            this.ieds.add(ied);
        }

        public CubicleLocation getLocationOf() {
            return locationOf;
        }

        public void setCubicleName(String cubicleName) {
            this.cubicleName = cubicleName;
        }

        public void setIeds(List<IED> ieds) {
            this.ieds = ieds;
        }

        public void setLocationOf(CubicleLocation locationOf) {
            this.locationOf = locationOf;
        }


        public String getOdf_name() {
            return odf_name;
        }

        public void setOdf_name(String odf_name) {
            this.odf_name = odf_name;
        }

        public String getOdf_portToNo() {
            return odf_portToNo;
        }

        public void setOdf_portToNo(String odf_portToNo) {
            this.odf_portToNo = odf_portToNo;
        }

        public String getOdf_portFrNo() {
            return odf_portFrNo;
        }

        public void setOdf_portFrNo(String odf_portFrNo) {
            this.odf_portFrNo = odf_portFrNo;
        }
    }
    public static class CubicleGroupBeanMore extends CubicleGroupBaseBean {
        String odf_name;
        String odf_portToNo;
        String odf_portFrNo;

        public CubicleGroupBeanMore(){
        }

        public CubicleGroupBeanMore(String cubicleName, List<IED> ieds, CubicleLocation locationOf, String odf_name, String odf_portToNo, String odf_portFrNo) {
            super(cubicleName, ieds, locationOf);
            this.odf_name = odf_name;
            this.odf_portToNo = odf_portToNo;
            this.odf_portFrNo = odf_portFrNo;
        }

        public String getOdf_name() {
            return odf_name;
        }

        public String getOdf_portToNo() {
            return odf_portToNo;
        }

        public String getOdf_portFrNo() {
            return odf_portFrNo;
        }

        public void setOdf_name(String odf_name) {
            this.odf_name = odf_name;
        }

        public void setOdf_portToNo(String odf_portToNo) {
            this.odf_portToNo = odf_portToNo;
        }

        public void setOdf_portFrNo(String odf_portFrNo) {
            this.odf_portFrNo = odf_portFrNo;
        }

    }

    public class CubicleGroupBeanSingle extends CubicleGroupBaseBean {
        public CubicleGroupBeanSingle(String cubicleName, List<IED> ieds/*, CubicleLocation locationOf*/) {
            super(cubicleName, ieds, CubicleLocation.SINGLE);
        }
    }

    public class CubicleGroupBeanDoubleIEDsDirect extends CubicleGroupBaseBean {
        public CubicleGroupBeanDoubleIEDsDirect(String cubicleName, List<IED> ieds, CubicleLocation locationOf) {
            super(cubicleName, ieds, locationOf);
        }
    }

    public static class IED{
        public String iedName;
        public String ied_portNo;
        public Integer type;//1.是ied  2.odf 3.交换机

        public IED() {
        }

        public IED(String iedName, String ied_portNo) {
            this.iedName = iedName;
            this.ied_portNo = ied_portNo;
        }

        public void setIedName(String iedName) {
            this.iedName = iedName;
        }

        public void setIed_portNo(String ied_portNo) {
            this.ied_portNo = ied_portNo;
        }
    }
    //跳纤
    public class TX{

    }
    //光缆
    public class GL{

    }

    public enum CubicleLocation{
        FROM,
        PASS,
        TO,
        SINGLE,//单独一个屏柜中有两个ied
        DOUBLE_FROM,
        DOUBLE_TO
    }

    public void setLink_type(int link_type) {
        this.link_type = link_type;
    }

    public void setCubicles(List<CubicleGroupBaseBean> cubicles) {
        this.cubicles = cubicles;
    }

    public List<String> getGlNames() {
        return glNames;
    }

    public void setGlNames(List<String> glNames) {
        this.glNames = glNames;
    }

    public List<String> getTxNames() {
        return txNames;
    }

    public void setTxNames(List<String> txNames) {
        this.txNames = txNames;
    }

    public void setIed2ied_TxName(List<String> ied2ied_TxName) {
        this.ied2ied_TxName = ied2ied_TxName;
    }

    public void setIed2ied_WLName(List<String> ied2ied_WLName) {
        this.ied2ied_WLName = ied2ied_WLName;
    }

    public static void clear(){
        if (sInstance!=null){
            sInstance = null;
        }
    }
}
