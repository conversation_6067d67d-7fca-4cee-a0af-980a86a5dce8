/*
 * Copyright (c) 2010-2011, The MiCode Open Source Community (www.micode.net)
 * 
 * This file is part of FileExplorer.
 * 
 * FileExplorer is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * 
 * FileExplorer is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with SwiFTP. If not, see <http://www.gnu.org/licenses/>.
 */

package com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.device_files;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.kemov.visual.spcd.spcdvisualandroidapp.R;

import java.io.File;

public class IntentBuilder {

    public static void viewFile(final Context context, final String filePath) {
        String type = getMimeType(filePath);

        if (!TextUtils.isEmpty(type) && !TextUtils.equals(type, "*/*")) {
            /* 设置intent的file与MimeType */
            Intent intent = new Intent();
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.setAction(Intent.ACTION_VIEW);
            intent.setDataAndType(Uri.fromFile(new File(filePath)), type);
            context.startActivity(intent);
        } else {
            // unknown MimeType
            Log.i("lei", "le");
            AlertDialog.Builder dialogBuilder = new AlertDialog.Builder(context);
            dialogBuilder.setTitle("选择文件类型");

             CharSequence[] menuItemArray = new CharSequence[] {
             "文本","音频","视频","图片","*/*" };
             
   
            //CharSequence[] menuItemArray = new CharSequence[] {"文本", "音频", "视频", "图像"};
            dialogBuilder.setItems(menuItemArray, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    String selectType = "*/*";
                    switch (which) {
                        case 0:
                            selectType = "text/plain";
                            break;
                        case 1:
                            selectType = "audio/*";
                            break;
                        case 2:
                            selectType = "video/*";
                            break;
                        case 3:
                            selectType = "image/*";
                            break;
                        case 4:
                            selectType = "*/*";
                            break;
                    }
                    Intent intent = new Intent();
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    intent.setAction(Intent.ACTION_VIEW);
                    intent.setDataAndType(Uri.fromFile(new File(filePath)), selectType);
                    context.startActivity(intent);
                }
            });
            dialogBuilder.show();
        }
    }

    private static String getMimeType(String filePath) {
        int dotPosition = filePath.lastIndexOf('.');
        if (dotPosition == -1)
            return "*/*";

        String ext = filePath.substring(dotPosition + 1, filePath.length()).toLowerCase();
        String mimeType = MimeUtils.guessMimeTypeFromExtension(ext);
        if (ext.equals("mtz")) {
            mimeType = "application/miui-mtz";
        }

        return mimeType != null ? mimeType : "*/*";
    }
}
