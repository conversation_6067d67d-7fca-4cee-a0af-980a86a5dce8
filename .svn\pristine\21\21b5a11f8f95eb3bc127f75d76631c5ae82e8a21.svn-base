package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.cubicle_back_panel;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.text.Layout;
import android.text.TextPaint;
import android.util.Log;

import com.kemov.visual.spcd.spcdvisualandroidapp.R;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.utils.DrawUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.utils.ResourceUtils;

import static com.kemov.visual.spcd.spcdvisualandroidapp.visualview.CubicleBackPanelView.NA;

public class PlugIedSvgBean extends  PlugBaseSvgBean{

    public PlugIedSvgBean(Context mCtx) {
        super(mCtx);
    }

    @Override
    public void initSVG_WH() {
        this.svg_w = (int) CubicleBackPanelConstants.IED_BOARD_PLUGIN_IMG_WIDTH;
        this.svg_h = (int) CubicleBackPanelConstants.IED_BOARD_PLUGIN_IMG_HEIGHT;
    }

    public void setRectF(RectF rectF){
        this.rectF = rectF;
        svgRect = new RectF(rectF.left,rectF.bottom-svg_h,
                rectF.left+svg_w,rectF.bottom);

        //svg:w/h = 43/51

        // port-num : w/h=13/51
        port_no_rect = new RectF(svgRect.left,svgRect.top,svgRect.left+svg_w*(13f/43f),svgRect.bottom);
        cubicle_board_info_rect = new RectF(rectF.right-CubicleBackPanelConstants.IED_BOARD_PLUGIN_TEXT_WIDTH,
                rectF.top,
                rectF.right,
                rectF.top+CubicleBackPanelConstants.IED_BOARD_PLUGIN_TEXT_HEIGHT
                );
        two_arrow_rect = new RectF(svgRect.right-10,
                rectF.bottom-svgRect.height()*2/3,
                rectF.right-cubicle_board_info_rect.width(),
                rectF.bottom-svgRect.height()*1/3
        );
    }

    public boolean isOnTouch(float x, float y, float totalOffX, float totalOffY, float mSclTot){
        Log.e("doubleClickOnCtrlBlock", "isOnTouch: rectF="+ String.format("%s",rectF.toString()));
        Log.e("doubleClickOnCtrlBlock", "isOnTouch: "+String.format("%f,%f,%f,%f,%f",x,y,totalOffX,totalOffY,mSclTot) );
        x -= totalOffX;
        y -= totalOffY;
        if(x>=rectF.left*mSclTot && x<=rectF.right*mSclTot
                && y>=rectF.top*mSclTot && y<= rectF.bottom*mSclTot)
            return true;
        else {
            return false;
        }
    }

    public void draw(Canvas canvas, float scale, Paint paint, TextPaint centerTxtPaint){
        //绘制svg 部分
        Bitmap bitmap = this.getPlugIconBySvgType(mCtx, svgType,svg_w,svg_h, scale);
        Rect src = new Rect(0,0, (int) (svg_w*scale),(int) (svg_w*scale));

        canvas.drawBitmap(bitmap, src, svgRect, paint);
        //canvas.drawRect(svgRect,paint);

        Paint ss = new Paint(paint);
        ss.setStyle(Paint.Style.FILL);
        ss.setColor(0x7fff0000);
        canvas.drawRect(port_no_rect,ss);
        Log.d("GGGGGGGG", "draw: port_no_rect="+port_no_rect.toString() );
        //绘制port_no部分
        DrawUtils.textCenter(port_no,centerTxtPaint,canvas,port_no_rect,
                Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);

        //绘制RxTx光纤+传输方向箭头
        if (svgType == null) return;

        if (svgType==SvgType.RJ45_T || svgType==SvgType.RJ45_R){
            //绘制带箭头的线段
            canvas.drawLine(two_arrow_rect.left,two_arrow_rect.centerY(),two_arrow_rect.right,two_arrow_rect.centerY(),paint);
            Path arrow1 = getArrowPath(two_arrow_rect.left+two_arrow_rect.width()/2,two_arrow_rect.centerY(),svgType==SvgType.RJ45_T?ArrowDirect.ToRight:ArrowDirect.ToLeft);
            canvas.drawPath(arrow1,paint);
            //绘制光缆
            canvas.drawLine(two_arrow_rect.left,two_arrow_rect.top+two_arrow_rect.height()/2,
                    two_arrow_rect.left+CubicleBackPanelConstants.IED_BOARD_PLUGIN_GL_LENGTH,two_arrow_rect.top+two_arrow_rect.height()/2,paint);
        }
        else if (svgType==SvgType.ST_T || svgType==SvgType.ST_R){
            //绘制带箭头的线段
            canvas.drawLine(two_arrow_rect.left,two_arrow_rect.centerY(),two_arrow_rect.right,two_arrow_rect.centerY(),paint);
            Path arrow1 = getArrowPath(two_arrow_rect.left+two_arrow_rect.width()/2,two_arrow_rect.centerY(),svgType==SvgType.ST_T?ArrowDirect.ToRight:ArrowDirect.ToLeft);
            canvas.drawPath(arrow1,paint);
            //绘制光缆
            canvas.drawLine(two_arrow_rect.left,two_arrow_rect.top+two_arrow_rect.height()/2,
                    two_arrow_rect.left+CubicleBackPanelConstants.IED_BOARD_PLUGIN_GL_LENGTH,two_arrow_rect.top+two_arrow_rect.height()/2,paint);
        }
        else {
            canvas.drawLine(two_arrow_rect.left,two_arrow_rect.top,two_arrow_rect.right,two_arrow_rect.top,paint);
            canvas.drawLine(two_arrow_rect.left,two_arrow_rect.bottom,two_arrow_rect.right,two_arrow_rect.bottom,paint);
            Path arrow1 = getArrowPath(two_arrow_rect.left+two_arrow_rect.width()/2,two_arrow_rect.top,ArrowDirect.ToLeft);
            Path arrow2 = getArrowPath(two_arrow_rect.left+two_arrow_rect.width()/2,two_arrow_rect.bottom,ArrowDirect.ToRight);
            canvas.drawPath(arrow1,paint);
            canvas.drawPath(arrow2,paint);

            //绘制竖线
            canvas.drawLine(two_arrow_rect.right,two_arrow_rect.top,
                    two_arrow_rect.right,two_arrow_rect.bottom,paint);
            //绘制光缆
            canvas.drawLine(two_arrow_rect.right,two_arrow_rect.top+two_arrow_rect.height()/2,
                    two_arrow_rect.right+CubicleBackPanelConstants.IED_BOARD_PLUGIN_GL_LENGTH,two_arrow_rect.top+two_arrow_rect.height()/2,paint);
        }



        StringBuilder portTo = new StringBuilder(oppositeUnitDesc);
        if (board_type!=null){
            if(!board_type.isEmpty() && !board_type.equals(NA)){
                portTo.append("[")
                        .append(board_type)
                        .append("]");
            }
        }

        float txt_size = centerTxtPaint.getTextSize();
        centerTxtPaint.setTextSize(txt_size-2);
        DrawUtils.textCenter(portTo.toString(),centerTxtPaint,canvas,this.cubicle_board_info_rect,
                Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);
        centerTxtPaint.setTextSize(txt_size);

        //绘制cubicle_desc+board_type部分
        DrawUtils.textCenter(port_no,centerTxtPaint,canvas,port_no_rect,
                Layout.Alignment.ALIGN_NORMAL,1.3f,0f,false);
    }


    @Override
    public Bitmap getPlugIconBySvgType(Context context,SvgType svgType,int dstWidth,int dstHeight,float scale){

        int resId = 0;
        if (svgType == null) {
            //throw new RuntimeException("svgType is null");
            resId = R.drawable.ied_na;
            Bitmap bitmap= ResourceUtils.getBitmapFromDrawable(
                    context, resId, (int) (dstWidth*scale),(int) (dstHeight*scale));
            return bitmap;
        }

        switch (svgType){
            case LC:
                resId = R.drawable.ied_lc_rt;
                break;
            case LC_TR:
                resId = R.drawable.ied_lc_tr;
                break;
            case LC_T:
                resId = R.drawable.ied_lc_t;
                break;
            case LC_R:
                resId = R.drawable.ied_lc_r;
                break;
            case ST:
                resId = R.drawable.ied_st_rt;
                break;
            case ST_TR:
                resId = R.drawable.ied_st_tr;
                break;
            case ST_T:
                resId = R.drawable.ied_pair_t;
                break;
            case ST_R:
                resId = R.drawable.ied_pair_r;
                break;
            case SC:
                resId = R.drawable.ied_sc_rt;
                break;
            case SC_TR:
                resId = R.drawable.ied_sc_tr;
                break;
            case SC_T:
                resId = R.drawable.ied_pair_t;
                break;
            case SC_R:
                resId = R.drawable.ied_pair_r;
                break;
            case FC:
                resId = R.drawable.ied_fc_open_rt;
                break;
            case FC_TR:
                resId = R.drawable.ied_fc_open_tr;
                break;
            case FC_T:
                resId = R.drawable.ied_pair_t;
                break;
            case FC_R:
                resId = R.drawable.ied_pair_r;
                break;
            case RJ45:
                resId = R.drawable.ied_rj45;
                break;
            case RJ45_T:
                resId = R.drawable.ied_pair_t;
                break;
            case RJ45_R:
                resId = R.drawable.ied_pair_r;
                break;
        }
        Bitmap bitmap= ResourceUtils.getBitmapFromDrawable(
                context, resId, (int) (dstWidth*scale),(int) (dstHeight*scale));
        return bitmap;
    }

}
