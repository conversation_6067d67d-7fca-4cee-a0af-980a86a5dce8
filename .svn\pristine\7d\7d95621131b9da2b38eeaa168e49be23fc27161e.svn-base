package com.kemov.visual.spcd.spcdvisualandroidapp.activity.ied_whole_circuit;

import android.app.Activity;
import android.text.TextUtils;
import android.util.Log;

import com.kemov.parsescl.KIEDModel;
import com.kemov.parsescl.KSclModel;
import com.kemov.sclaata.common.app.PubUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.OdfSwitchBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.OdfSwitchPassBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.PassDataBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.PortConnectBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.WholeCircuitBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_REGION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_SUBSTATION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDao;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.SpcdUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Utils;
import com.share.mycustomviewlib.bean.XshlBean;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool.CubicleLocation.DOUBLE_FROM;
import static com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool.CubicleLocation.DOUBLE_TO;
import static com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool.CubicleLocation.FROM;
import static com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool.CubicleLocation.PASS;
import static com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool.CubicleLocation.SINGLE;
import static com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool.CubicleLocation.TO;
import static java.util.Arrays.asList;

/**
 * 传入端口，找到中间经过的装置，及目的装置（找到交换机会接着找，直到找到ied装置）
 */
public class HandleForWholeCircuitsFix4 {
    static KIEDModel kiedModel;
    static KSclModel mKSclModel;
    private static final String TAG = "HandleForWholeCircuits";

    public static final String UNIT_TYPE_IED = "IED";
    public static final String UNIT_TYPE_SWITCH = "SWITCH";
    public static final String UNIT_TYPE_ODF = "ODF";

    static Activity mCtx;
    Integer mType;

    private String dbName;
    public PubUtils mPubUtils = null;

    static BaseDao cableDao = null;
    static BaseDao coreDao = null;
    static BaseDaoImp unitDao = null;
    static BaseDaoImp boardDao = null;
    static BaseDaoImp portDao = null;
    static BaseDaoImp cubicleDao = null;
    static BaseDaoImp regionDao = null;
    BaseDaoImp substationDao =null;
    static BaseDaoImp intcoreDao=null;
    private static KM_SPCD_UNIT km_spcd_unit;
    private static KM_SPCD_CUBICLE km_spcd_cubicle;
    private static KM_SPCD_SUBSTATION km_spcd_substation;
    private static KM_SPCD_REGION km_spcd_region;
    List<KM_SPCD_INTCORE> bIntcores = new ArrayList<>();//portB="4n.1.A-Rx"
    List<KM_SPCD_INTCORE> aIntcores = new ArrayList<>();//portA="4n.1.C-Tx"
    List<PortConnectBean.IedConnectBean> iedList  = new ArrayList<>();
    static List<String> switchIntCores  = new ArrayList<>();
    PortConnectBean portConnectBean;
    public static IEDsWholeCircuitDataPool instance;

    static List<IEDsWholeCircuitDataPool.CubicleGroupBaseBean> cubicles = new ArrayList<>();
    static List<String> glNames = new ArrayList<>();
    List<String> txNames = new ArrayList<>();//跳纤(ied-odf)
    List<String> ied2ied_TxName = new ArrayList<>();//跳纤（1个屏柜中的两个装置直接相连）
    List<String> ied2ied_WLName = new ArrayList<>();//尾缆（两个屏柜中的两个装置直接相连）
    KM_SPCD_PORT port = null;//全回路被点击的端口号
    KM_SPCD_BOARD board = null;//被点击端口的板卡
    String port0 = null;//被点击端口的拼接端口，如：portA="1n.1.A-Tx"
    static List<IEDsWholeCircuitDataPool.IED> ieds1 = null;//装第一个屏柜的ied
    IEDsWholeCircuitDataPool.IED ied1 = null;//第一个拼柜的ied
    static IEDsWholeCircuitDataPool.CubicleGroupBaseBean cubicleGroupBaseBean1;//1.第一个屏柜的数据

    //第一个屏柜，第一个屏柜的装置
    WholeCircuitBean.CubicleWholeBean cubicleWholeBean1;
    WholeCircuitBean.UnitWholeBean unitWholeBean1;
    List<WholeCircuitBean.UnitWholeBean> unitWholeBeanList1;

    public WholeCircuitBean wholeCircuitBeanOriginal;//画物理全回路图所需数据

    KM_SPCD_INTCORE intcore = null;
    public OdfSwitchBean wholeCircuitBean0;
    static List<KM_SPCD_PORT> highlightPorts = new ArrayList<>();

    private Integer isOdf = 0;//0不是odf，1是odf

    List<KM_SPCD_UNIT> odfList = new ArrayList<>();
    List<KM_SPCD_UNIT> switchList = new ArrayList<>();
    public PassDataBean passDataBean;
    OdfSwitchBean odfSwitchBean;
    public List<OdfSwitchPassBean> odfSwitchBeanList = new ArrayList<>();//中间装装置过渡集合
    public List<OdfSwitchPassBean> odfSwitchBeanList0 = new ArrayList<>();//最终路线集合
    public List<List<OdfSwitchPassBean>> odfSwitchBeanList2 = new ArrayList<>();//所有连到目的装置的路线集合

//    public List<List<OdfSwitchPassBean>> odfSwitchBeanList3 = new ArrayList<>();//所有连到目的装置的路线集合,不过滤的
//    public Map<String,List<List<OdfSwitchPassBean>>> map = new HashMap<>();//所有连到目的装置的路线集合,不过滤的
    public Map<String,List<WholeCircuitBean>> map1 = new HashMap<>();//所有连到目的装置的路线集合,不过滤的
    public List<Map<String,List<List<OdfSwitchPassBean>>>> listAll = new ArrayList<>();//所有连到目的装置的路线集合,不过滤的
    public List<Map<String,List<List<OdfSwitchPassBean>>>> listAll2 = new ArrayList<>();

    public List<Map<String,List<OdfSwitchBean>>> listMap1 = new ArrayList<>();
    public List<Map<String,List<OdfSwitchBean>>> listMap2 = new ArrayList<>();

    String iedName = null;//目的装置名

    WholeCircuitBean wholeCircuitBean;


    static List<String> odfCores  = new ArrayList<>();//记录同一次寻找中找过的光缆纤芯不要再找了

    XshlBean xshlBean;

    public HandleForWholeCircuitsFix4(Activity mCtx, String dbName, Integer portId, Integer type,String goalUnitIedName) {
        this.mCtx = mCtx;
        this.dbName = dbName;
        this.iedName = goalUnitIedName;
        //查询传入端口经过装置
        wholeCircuitBean0 = init(portId, type);
        passDataBean = new PassDataBean();
        //找到所有经过的odf和交换机路线之后；跟虚实回路该端口连接的对侧ied做比对，挑选出其中一条正确的路线
//        XshlBean xshlBean = XshlUtils.getXshlData(mCtx,dbName, km_spcd_unit.getId(), 1);
        xshlBean = XshlBean.getInstance();
//        getRightOdfSwitch(xshlBean);
        getRightOdfSwitch1(wholeCircuitBean0,listMap1,listAll);
        int i = odfSwitchBeanList0 == null ? 0 : odfSwitchBeanList0.size();

        //获取对侧端口经过装置
        getTwoPortData();
        Log.e("找到经过的odf和交换机",i+"");
    }

    private void getTwoPortData() {
        //找另外一侧端口
        KM_SPCD_PORT portOther = null;
        if (port != null){
            String txRx = getTxRx(port.getDirection());
            try {
                portOther = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", board.getId(), "no", port.getNo(), "direction", txRx);
                if (portOther == null) return;
                OdfSwitchBean wholeCircuitBean1 = init(portOther.getId(), 3);
                setodfSwitchBeanList0();
                //将两测端口的数据合并
                getRightOdfSwitch1(wholeCircuitBean1,listMap2,listAll2);
                //数据整合
                if (listAll!=null && listAll.size()!=0 && listAll2!=null && listAll2.size()!=0){
                    for (Map<String,List<List<OdfSwitchPassBean>>> map:listAll){
                        for(Map.Entry<String,List<List<OdfSwitchPassBean>>> entry : map.entrySet()){
                            String mapKey = entry.getKey();
                            List<List<OdfSwitchPassBean>> value1 = entry.getValue();
                            //另外一个装置的数据
                            List<List<OdfSwitchPassBean>> value2 = null;
                            for (Map<String,List<List<OdfSwitchPassBean>>> map1:listAll2){
                                List<List<OdfSwitchPassBean>> value3 = map1.get(mapKey);
                                if (value3 != null){
                                    value2 = value3;
                                    break;
                                }
                            }
                            setOtherPortData(value1,value2);
                        }
                    }
                }else if (listAll == null || listAll.size() == 0){
                    if (listAll2!=null && listAll2.size()!=0){
                        listAll.addAll(listAll2);
                    }
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }

    }

    private void setOtherPortData(List<List<OdfSwitchPassBean>> value1, List<List<OdfSwitchPassBean>> value2) {
        if (value2!=null && value2.size()!=0){
            for (int x = 0;x<value1.size();x++){
                List<OdfSwitchPassBean> odfSwitchPassBean1 = value1.get(x);
                if (x<value2.size()){
                    List<OdfSwitchPassBean> odfSwitchPassBean2 = value2.get(x);
                    if (odfSwitchPassBean1!=null && odfSwitchPassBean1.size()!=0){
                        for (int y = 0;y<odfSwitchPassBean1.size();y++){
                            OdfSwitchPassBean odfSwitchPass1 = odfSwitchPassBean1.get(y);
                            OdfSwitchPassBean odfSwitchPass2 = odfSwitchPassBean2.get(y);
                            odfSwitchPass1.setUnitWholeBean1(odfSwitchPass2.getUnitWholeBean());
                        }
                    }
                }
            }
        }
    }

    public List<String> iedNameList = null;

    public void getIedNameList(XshlBean xshlBean){
        //找到端口最终发到哪个ied了
        iedNameList = new ArrayList<>();
        if (xshlBean != null && TextUtils.isEmpty(iedName)){
            kiedModel = getKIEDMode(km_spcd_unit.getIed_name());//ML2201A
            if (kiedModel != null){
                if(kiedModel.getVIEDs()!=null)
                for (KIEDModel.HLP_VircIED vIED : kiedModel.getVIEDs()) {
                    iedNameList.add(vIED.sIedName);
                }
            }
        }else if (!TextUtils.isEmpty(iedName)){
            iedNameList.add(iedName);
        }
        Log.e(TAG, "(传入的端口装置)km_spcd_unit.getIed_name() : " + km_spcd_unit.getIed_name());
        Log.e(TAG, "xshlBean!=null : " + (xshlBean!=null));
        Log.e(TAG, "iedName : " + iedName);
        Log.e(TAG, "kiedModel != null : " + (kiedModel != null));
        Log.e(TAG, "iedNameList.size() : " + iedNameList.size());
    }

    private void getRightOdfSwitch1(OdfSwitchBean wholeCircuitBean0,List<Map<String,List<OdfSwitchBean>>> listMap,List<Map<String,List<List<OdfSwitchPassBean>>>> listAll) {
        getIedNameList(xshlBean);

        if (wholeCircuitBean0 != null){
            List<OdfSwitchBean> odfAndSwiList = new ArrayList<>();
            odfAndSwiList.add(wholeCircuitBean0);
            List<OdfSwitchBean> odfAndSwitch = wholeCircuitBean0.getConnectedBeans();
            if (odfAndSwitch == null || odfAndSwitch.size() == 0){
                if (wholeCircuitBean0.getKm_spcd_unit()!=null){
                    Map<String,List<OdfSwitchBean>> map = new HashMap<>();
                    map.put(wholeCircuitBean0.getKm_spcd_unit().getIed_name(),odfAndSwiList);
                    if (iedNameList!=null && iedNameList.size()!=0 && iedNameList.contains(wholeCircuitBean0.getKm_spcd_unit().getIed_name()) && wholeCircuitBean0.getKm_spcd_unit().getDev_class().equals("IED")){
                        listMap.add(map);
                    }else if (iedNameList == null || iedNameList.size() == 0){
                        if (wholeCircuitBean0.getKm_spcd_unit().getDev_class().equals("IED")){
                            listMap.add(map);
                        }
                    }
                }
            }else {
                setList(odfAndSwiList,odfAndSwitch,listMap);
            }
        }

        //将发送到相同ied的路线归纳在一起
        if (listMap!=null && listMap.size()!=0){
            for(Map<String,List<OdfSwitchBean>> map:listMap){

                for(Map.Entry<String, List<OdfSwitchBean>> entry : map.entrySet()){
                    String mapKey = entry.getKey();
                    List<OdfSwitchBean> value = entry.getValue();

                    //获取集合是否已经存过该装置了
                    List<List<OdfSwitchPassBean>> odfSwitchPassBeanList = getOdfSwitchPassBeanList(mapKey,listAll);

                    List<OdfSwitchPassBean> value1 = new ArrayList<>();
                    //将OdfSwitchBean对象装换OdfSwitchPassBean
                    if (value!=null && value.size()!=0){
                        OdfSwitchBean odfSwitchBeanF = value.get(0);//获取第一个装置
                        OdfSwitchBean odfSwitchBeanL = value.get(value.size()-1);//获取最后一个装置
                        boolean isHaveSwi = false;//是否经过交换机


                        for (OdfSwitchBean odfSwitchBean1:value){
                            OdfSwitchPassBean odfSwitchPassBean = new OdfSwitchPassBean();
                            odfSwitchPassBean.setKm_spcd_unit(odfSwitchBean1.getKm_spcd_unit());
                            odfSwitchPassBean.setKm_spcd_intcore(odfSwitchBean1.getKm_spcd_intcore());
                            odfSwitchPassBean.setKm_spcd_core(odfSwitchBean1.getKm_spcd_core());
                            odfSwitchPassBean.setKm_spcd_cable(odfSwitchBean1.getKm_spcd_cable());
                            odfSwitchPassBean.setPortA(odfSwitchBean1.getPortRx());
                            odfSwitchPassBean.setType(odfSwitchBean1.getType());
                            odfSwitchPassBean.setPortB(odfSwitchBean1.getPortTx());
                            odfSwitchPassBean.setHighlightPorts(odfSwitchBean1.getHighlightPorts());
                            //构建画图对象---重点
                            odfSwitchPassBean.setKm_spcd_cubicle(odfSwitchBean1.getKm_spcd_cubicle());

                            WholeCircuitBean.UnitWholeBean unitWholeBean = new WholeCircuitBean.UnitWholeBean();
                            odfSwitchPassBean.setUnitWholeBean(unitWholeBean);
                            setUnitWholeBean(odfSwitchBean1,unitWholeBean);
                            value1.add(odfSwitchPassBean);

                            if (odfSwitchBean1.getKm_spcd_unit().getDev_class().equals("SWITCH")){
                                isHaveSwi = true;
                            }
                        }

                        String ied_nameF = odfSwitchBeanF.getKm_spcd_unit().getIed_name();
                        String ied_nameL = odfSwitchBeanL.getKm_spcd_unit().getIed_name();
                        if(isHaveSwi){
                            if (ied_nameF.startsWith("P") && ied_nameL.startsWith("P")){//如果是P开头，并且经过交换机，只会跟P开头的结束装置相连
                                odfSwitchPassBeanList.add(value1);
                            }else if (ied_nameF.startsWith("C")){
                                odfSwitchPassBeanList.add(value1);
                            }
                        }else {
                            odfSwitchPassBeanList.add(value1);
                        }
                    }

                    if (odfSwitchPassBeanList.size() == 1){
                        Map<String, List<List<OdfSwitchPassBean>>> stringListMap = new HashMap<>();
                        stringListMap.put(mapKey,odfSwitchPassBeanList);
                        listAll.add(stringListMap);
                    }
                }
            }
        }

    }

    public void setodfSwitchBeanList0(){
        //全回路图所需数据
        if (!TextUtils.isEmpty(iedName) && listAll!=null && listAll.size()!=0){
            //取所有经过的装置中，线路最短的一条
            int len = 0;//记录最短的
            Map<String, List<List<OdfSwitchPassBean>>> stringListMap = listAll.get(0);
            List<List<OdfSwitchPassBean>> lists = stringListMap.get(iedName);

            for (int x=0;x<lists.size();x++ ){
                List<OdfSwitchPassBean> odfSwitchPassBeans = lists.get(x);
                if (x == 0){
                    len = odfSwitchPassBeans.size();
                    odfSwitchBeanList0.addAll(odfSwitchPassBeans);
                }else {
                    if (len > odfSwitchPassBeans.size()){
                        len = odfSwitchPassBeans.size();
                        odfSwitchBeanList0.clear();
                        odfSwitchBeanList0.addAll(odfSwitchPassBeans);
                    }
                }
            }
        }
    }

    private List<List<OdfSwitchPassBean>> getOdfSwitchPassBeanList(String mapKey,List<Map<String,List<List<OdfSwitchPassBean>>>> listAll) {
        List<List<OdfSwitchPassBean>> lists = null;
        if (listAll!=null && listAll.size()!=0){
            for (Map<String,List<List<OdfSwitchPassBean>>> map : listAll){
                lists = map.get(mapKey);
                if (lists != null){
                    return lists;
                }
            }
        }
        if (lists == null ){
            lists = new ArrayList<>();
        }
        return lists;
    }

    private void setUnitWholeBean(OdfSwitchBean odfSwitchBean1, WholeCircuitBean.UnitWholeBean unitWholeBean) {
        KM_SPCD_UNIT km_spcd_unit = odfSwitchBean1.getKm_spcd_unit();
        String dev_class = km_spcd_unit.getDev_class();

        unitWholeBean.setKm_spcd_unit(km_spcd_unit);
        unitWholeBean.setKm_spcd_intcoreA(odfSwitchBean1.getKm_spcd_intcore());
        unitWholeBean.setKm_spcd_coreA(odfSwitchBean1.getKm_spcd_core());
        unitWholeBean.setPortADesc(odfSwitchBean1.getPortAdsc());
        unitWholeBean.setPortAdscInt(odfSwitchBean1.getPortAdscInt());
        unitWholeBean.setBoard(odfSwitchBean1.getBoard());
        unitWholeBean.setBoardPort(odfSwitchBean1.getBoardPort());


        String portA = odfSwitchBean1.getPortRx();
        String portB = odfSwitchBean1.getPortTx();
        if (!TextUtils.isEmpty(portA)){
            String[] split = portA.split("\\.");
            String nodir = split[2];

            unitWholeBean.setBoard(split[1]);
            if (!TextUtils.isEmpty(nodir) && nodir.contains("-")){
                String[] split1 = nodir.split("-");
                unitWholeBean.setBoardPort(split[1]+"-"+split1[0]);
                unitWholeBean.setPort(split1[0]);
                unitWholeBean.setPortADirection(split1[1]);
            }
        }

        if (!TextUtils.isEmpty(portB)){
            String[] split = portB.split("\\.");
            String nodir = split[2];

            unitWholeBean.setBoard1(split[1]);
            if (!TextUtils.isEmpty(nodir) && nodir.contains("-")){
                String[] split1 = nodir.split("-");
                unitWholeBean.setPort1(split1[0]);
                unitWholeBean.setPortA1Direction(split1[1]);
            }
        }
    }

    private void setList(List<OdfSwitchBean> odfAndSwiList, List<OdfSwitchBean> odfAndSwitchList,List<Map<String,List<OdfSwitchBean>>> listMap) {

        for (OdfSwitchBean odfSwitchBean1 : odfAndSwitchList){
            List<OdfSwitchBean> odfAndSwitch = odfSwitchBean1.getConnectedBeans();

            List<OdfSwitchBean> odfAndSwiList1 = new ArrayList<>();
            odfAndSwiList1.addAll(odfAndSwiList);

            //判断这个屏柜是否已经连接过
            boolean isHave = getIsHave(odfAndSwiList1,odfSwitchBean1);
            if (isHave){
                odfAndSwiList1.add(odfSwitchBean1);

                if (odfAndSwitch == null || odfAndSwitch.size() == 0){//找到最后一个装置了
                    if (odfSwitchBean1.getKm_spcd_unit()!=null){
                        Map<String,List<OdfSwitchBean>> map = new HashMap<>();
                        map.put(odfSwitchBean1.getKm_spcd_unit().getIed_name(),odfAndSwiList1);
                        if (iedNameList!=null && iedNameList.size()!=0 && iedNameList.contains(odfSwitchBean1.getKm_spcd_unit().getIed_name()) && odfSwitchBean1.getKm_spcd_unit().getDev_class().equals("IED")){
                            listMap.add(map);
                        }else if (iedNameList == null || iedNameList.size() == 0){
                            if (odfSwitchBean1.getKm_spcd_unit().getDev_class().equals("IED")){
                                listMap.add(map);
                            }
                        }
                    }
                }else {
                    setList(odfAndSwiList1, odfAndSwitch,listMap);
                }
            }
        }

    }

    private boolean getIsHave(List<OdfSwitchBean> odfAndSwiList1, OdfSwitchBean odfSwitchBean1) {
        if (odfAndSwiList1!=null && odfAndSwiList1.size()!=0){
            int count = 0;
            for (OdfSwitchBean odfSwitchBean : odfAndSwiList1){
                if (odfSwitchBean.getKm_spcd_cubicle().getId() == odfSwitchBean1.getKm_spcd_cubicle().getId()){
                    count ++;
                }
                if (count >2){
                    return false;
                }
            }
        }
        return true;
    }

    private void getRightOdfSwitch(XshlBean xshlBean) {
        //将上一次的数据集清掉
        odfSwitchBeanList0.clear();

        //拼装发出去的端口信息
        String boardA1 = board .getSlot();
        String portA1 = port.getNo() +"-" + port.getDirection();
        boolean isGo = false;

        //找到端口最终发到哪个ied了
        List<String> iedNameList = new ArrayList<>();
        if (xshlBean != null && TextUtils.isEmpty(iedName)){
//            List<XshlBean.RightUnitConnect> list = xshlBean.getList();
//            if (list!=null && list.size()!=0){
//                for (XshlBean.RightUnitConnect rightUnitConnect:list){
//                    if (isGo){
//                        break;
//                    }
//                    List<XshlBean.ConnectBean> connectBeans = rightUnitConnect.getConnectBean();
//                    if (connectBeans!=null && connectBeans.size()!=0){
//                        for (XshlBean.ConnectBean connectBean:connectBeans){
//                            if (connectBean == null || connectBean.getBoardA() ==null || connectBean.getPortA()==null){
//                                continue;
//                            }
//                            if (connectBean.getBoardA().equals(boardA1) && connectBean.getPortA().equals(portA1)){
//                                iedName = rightUnitConnect.getIedName();
//                                isGo = true;
//                                break;
//                            }
//                        }
//                    }
//                }
//            }

            kiedModel = getKIEDMode(km_spcd_unit.getIed_name());//ML2201A
            if (kiedModel != null){
                for (KIEDModel.HLP_VircIED vIED : kiedModel.getVIEDs()) {
                    iedNameList.add(vIED.sIedName);
                }
            }
        }
        //去所有的连接里面找最终连着的那根线
        if (!TextUtils.isEmpty(iedName)){
//            if (wholeCircuitBean0 != null){
//                List<OdfSwitchBean> odfAndSwitchs = wholeCircuitBean0.getOdfAndSwitch();
//                //装第一个装置
//                OdfSwitchPassBean odfSwitchPassBean = new OdfSwitchPassBean();
//                odfSwitchPassBean.setKm_spcd_unit(wholeCircuitBean0.getKm_spcd_unit());
//                odfSwitchPassBean.setKm_spcd_intcore(wholeCircuitBean0.getKm_spcd_intcore());
//                odfSwitchPassBean.setKm_spcd_core(wholeCircuitBean0.getKm_spcd_core());
//                odfSwitchPassBean.setKm_spcd_cable(wholeCircuitBean0.getKm_spcd_cable());
//                odfSwitchPassBean.setPortA(wholeCircuitBean0.getPortA());
//                odfSwitchPassBean.setType(wholeCircuitBean0.getType());
//                odfSwitchPassBean.setKm_spcd_cubicle(wholeCircuitBean0.getKm_spcd_cubicle());
//                odfSwitchPassBean.setUnitWholeBean(wholeCircuitBean0.getUnitWholeBean());
//                odfSwitchBeanList.add(odfSwitchPassBean);
//
//                //取所有经过的装置中，线路最短的一条
//                if (odfAndSwitchs !=null && odfAndSwitchs.size()!=0){
//                    setOdfSwitchData(iedName,odfAndSwitchs,odfSwitchBeanList);
//                    if (odfSwitchBeanList2!=null && odfSwitchBeanList2.size()!=0){
//                        int len = 0;//记录最短的
//                        for (int x = 0;x<odfSwitchBeanList2.size();x++){
//                            List<OdfSwitchPassBean> odfSwitchPassBeans = odfSwitchBeanList2.get(x);
//                            if (x == 0){
//                                len = odfSwitchPassBeans.size();
//                                odfSwitchBeanList0.addAll(odfSwitchPassBeans);
//                            }else {
//                                if (len > odfSwitchPassBeans.size()){
//                                    len = odfSwitchPassBeans.size();
//                                    odfSwitchBeanList0.clear();
//                                    odfSwitchBeanList0.addAll(odfSwitchPassBeans);
//                                }
//                            }
//                        }
//                    }
//                }else {
//                    odfSwitchBeanList0 = odfSwitchBeanList;
//                }
//            }

            setResult(1,iedName);
        }else if (iedNameList!=null && iedNameList.size()!=0){//经过虚实回路过滤一次
            for (String str:iedNameList){
                setResult(2,str);
            }
        }else {//没有虚实回路，返回所有路线

        }

//        //将数据转成 物理全回路数据
//        if (listAll!=null && listAll.size()!=0){
//
//            for (Map<String,List<List<OdfSwitchPassBean>>> map:listAll){
//                for(Map.Entry<String, List<List<OdfSwitchPassBean>>> entry : map.entrySet()){
//                    String key = entry.getKey();
//                    List<List<OdfSwitchPassBean>> list = entry.getValue();
//
//                    List<WholeCircuitBean> wholeList = new ArrayList<>();
//                    map1.put(key,wholeList);
//
//                    if (list!=null && list.size()!=0){
//                        for (List<OdfSwitchPassBean> list1:list){
//                            if (list1!=null && list1.size()!=0){
//
//
//                                WholeCircuitBean wholeCircuitBean = new WholeCircuitBean();
//                                wholeList.add(wholeCircuitBean);
//                                List<WholeCircuitBean.CubicleWholeBean> cubicleWholeBeans = new ArrayList<>();
//                                wholeCircuitBean.setCubicleWholeBeans(cubicleWholeBeans);
//                                for (int x = 0;x<list1.size();x++){
//                                    OdfSwitchPassBean odfSwitchPassBean = list1.get(x);
//                                    if (x == 0){
//                                        wholeCircuitBean.setHighlightPorts(wholeCircuitBean.getHighlightPorts());
//                                        wholeCircuitBean.setType(2);
//
//                                        WholeCircuitBean.CubicleWholeBean cubicleWholeBean = new WholeCircuitBean.CubicleWholeBean();
//                                        cubicleWholeBean.setKm_spcd_cubicle(odfSwitchPassBean.getKm_spcd_cubicle());
//                                        List<WholeCircuitBean.UnitWholeBean> unitWholeBeans = new ArrayList<>();
//                                        cubicleWholeBean.setUnitWholeBeans(unitWholeBeans);
//                                        unitWholeBeans.add(odfSwitchPassBean.getUnitWholeBean());
//                                        cubicleWholeBeans.add(cubicleWholeBean);
//                                    }else if (x > 0){
//                                        OdfSwitchPassBean odfSwitchPassBean1 = list1.get(x - 1);
//                                        if (odfSwitchPassBean1.getKm_spcd_cubicle().getId() == odfSwitchPassBean.getKm_spcd_cubicle().getId()){
//                                            WholeCircuitBean.CubicleWholeBean cubicleWholeBean = cubicleWholeBeans.get(cubicleWholeBeans.size() - 1);
//                                            List<WholeCircuitBean.UnitWholeBean> unitWholeBeans = cubicleWholeBean.getUnitWholeBeans();
//                                            unitWholeBeans.add(odfSwitchPassBean.getUnitWholeBean());
//                                        }else {
//                                            WholeCircuitBean.CubicleWholeBean cubicleWholeBean = new WholeCircuitBean.CubicleWholeBean();
//                                            cubicleWholeBean.setKm_spcd_cubicle(odfSwitchPassBean.getKm_spcd_cubicle());
//                                            List<WholeCircuitBean.UnitWholeBean> unitWholeBeans = new ArrayList<>();
//                                            cubicleWholeBean.setUnitWholeBeans(unitWholeBeans);
//                                            unitWholeBeans.add(odfSwitchPassBean.getUnitWholeBean());
//                                            cubicleWholeBeans.add(cubicleWholeBean);
//                                        }
//                                    }
//                                }
//
//
//                            }
//                        }
//                    }
//                }
//            }
//
//        }
    }

    public void setResult(Integer type,String iedName){
        odfSwitchBeanList2.clear();
//        odfSwitchBeanList3.clear();

        if (wholeCircuitBean0 != null){
//            Map<String,List<List<OdfSwitchPassBean>>> map = new HashMap<>();//所有连到目的装置的路线集合,不过滤的

            List<OdfSwitchBean> odfAndSwitchs = wholeCircuitBean0.getConnectedBeans();
            if (odfSwitchBeanList == null || odfSwitchBeanList.size()==0){
                //装第一个装置
                OdfSwitchPassBean odfSwitchPassBean = new OdfSwitchPassBean();
                odfSwitchPassBean.setKm_spcd_unit(wholeCircuitBean0.getKm_spcd_unit());
                odfSwitchPassBean.setKm_spcd_intcore(wholeCircuitBean0.getKm_spcd_intcore());
                odfSwitchPassBean.setKm_spcd_core(wholeCircuitBean0.getKm_spcd_core());
                odfSwitchPassBean.setKm_spcd_cable(wholeCircuitBean0.getKm_spcd_cable());
                odfSwitchPassBean.setPortA(wholeCircuitBean0.getPortRx());
                odfSwitchPassBean.setType(wholeCircuitBean0.getType());
                odfSwitchPassBean.setKm_spcd_cubicle(wholeCircuitBean0.getKm_spcd_cubicle());
                odfSwitchPassBean.setUnitWholeBean(wholeCircuitBean0.getUnitWholeBean());
                odfSwitchPassBean.setPortB(wholeCircuitBean0.getPortTx());
                odfSwitchBeanList.add(odfSwitchPassBean);
            }

            if (odfAndSwitchs !=null && odfAndSwitchs.size()!=0){
                setOdfSwitchData(iedName,odfAndSwitchs,odfSwitchBeanList);
                if (odfSwitchBeanList2!=null && odfSwitchBeanList2.size()!=0){
                    //取所有经过的装置中，线路最短的一条
                    if (type == 1){
                        int len = 0;//记录最短的
                        for (int x = 0;x<odfSwitchBeanList2.size();x++){
                            List<OdfSwitchPassBean> odfSwitchPassBeans = odfSwitchBeanList2.get(x);
                            if (x == 0){
                                len = odfSwitchPassBeans.size();
                                odfSwitchBeanList0.addAll(odfSwitchPassBeans);
                            }else {
                                if (len > odfSwitchPassBeans.size()){
                                    len = odfSwitchPassBeans.size();
                                    odfSwitchBeanList0.clear();
                                    odfSwitchBeanList0.addAll(odfSwitchPassBeans);
                                }
                            }
                        }
                    }else if (type == 2){//取所有经过的装置中
                        Map<String,List<List<OdfSwitchPassBean>>> map = new HashMap<>();//所有连到目的装置的路线集合,不过滤的
                        List<List<OdfSwitchPassBean>> odfSwitchBeanList3 = new ArrayList<>();
                        odfSwitchBeanList3.addAll(odfSwitchBeanList2);
                        map.put(iedName,odfSwitchBeanList3);
                        listAll.add(map);
                    }
                }
            }else {
                if (type == 1){
                    odfSwitchBeanList0 = odfSwitchBeanList;
                }else if (type == 2){
//                    odfSwitchBeanList3.add(odfSwitchBeanList);
//                    map.put(iedName,odfSwitchBeanList3);
//                    listAll.add(map);
                }
            }
        }
    }


    private static KIEDModel getKIEDMode(String name){
        PubUtils mPubUtils = PubUtils.getPubUtils();
        mKSclModel = mPubUtils.getSclModel();
        if (mKSclModel == null){
            return null;
        }else {
            return mKSclModel.getKIEDInfo(name);
        }
    }

    public void setOdfSwitchData(String iedName,List<OdfSwitchBean> odfAndSwitchs,List<OdfSwitchPassBean> odfSwitchBeanList){
        for (int x = 0;x<odfAndSwitchs.size();x++){
            OdfSwitchBean odfSwitchBean = odfAndSwitchs.get(x);
            //有多少个循环就复制多少个上一个层级的集合
            List<OdfSwitchPassBean> odfSwitchBeanList1 = new ArrayList<>();
            odfSwitchBeanList1.addAll(odfSwitchBeanList);

            List<OdfSwitchBean> odfAndSwitch = odfSwitchBean.getConnectedBeans();
            KM_SPCD_UNIT km_spcd_unit = odfSwitchBean.getKm_spcd_unit();
            //新建对象
            OdfSwitchPassBean odfSwitchPassBean = new OdfSwitchPassBean();
            odfSwitchPassBean.setKm_spcd_unit(odfSwitchBean.getKm_spcd_unit());
            odfSwitchPassBean.setKm_spcd_intcore(odfSwitchBean.getKm_spcd_intcore());
            odfSwitchPassBean.setKm_spcd_core(odfSwitchBean.getKm_spcd_core());
            odfSwitchPassBean.setKm_spcd_cable(odfSwitchBean.getKm_spcd_cable());
            odfSwitchPassBean.setPortA(odfSwitchBean.getPortRx());
            odfSwitchPassBean.setType(odfSwitchBean.getType());
            odfSwitchPassBean.setKm_spcd_cubicle(odfSwitchBean.getKm_spcd_cubicle());
            odfSwitchPassBean.setUnitWholeBean(odfSwitchBean.getUnitWholeBean());
            odfSwitchPassBean.setPortB(odfSwitchBean.getPortTx());
            //添加到集合
            odfSwitchBeanList1.add(odfSwitchPassBean);
            if (odfAndSwitch!=null && odfAndSwitch.size()!=0){
                setOdfSwitchData(iedName,odfAndSwitch,odfSwitchBeanList1);
            }else {
                if (km_spcd_unit != null && km_spcd_unit.getIed_name().equals(iedName)){
                    odfSwitchBeanList2.add(odfSwitchBeanList1);
                }else {
                    //最后一个装置不是目的装置，之前记录的中间装置清空
                    odfSwitchBeanList1.clear();
                }
            }
        }
    }

    private OdfSwitchBean init(Integer id,Integer type) {
        initDao();
        findBaseData(id,type,1);

        //是odf点击进来，找到本屏柜连接该odf的ied
        OdfSwitchBean data = null;
        if ("ODF".equals(km_spcd_unit.getDev_class())){
            KM_SPCD_PORT portByPortA = null;
            if (type == 2){
                intcore = (KM_SPCD_INTCORE) intcoreDao.getById(id);
                portByPortA = getPortByPortA(km_spcd_cubicle, intcore.getPort_b());
                //设置高亮
                KM_SPCD_PORT portByPortHig = getPortByPortA(km_spcd_cubicle, intcore.getPort_b());
                highlightPorts.add(portByPortHig);
            }else if (type == 3 || type == 4){
                List<KM_SPCD_INTCORE> km_spcd_intcores  = (List<KM_SPCD_INTCORE>) intcoreDao.getListForEq("cubicle_id", km_spcd_cubicle.getId());
                KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD)boardDao.getById(port.getBoard_id());
                //查询另外一个端口
                String portAB1 = km_spcd_unit.getName() +"." + km_spcd_board .getSlot() + "." + port.getNo() +"-" + port.getDirection();//如：1n.1.A-Tx
                if (km_spcd_intcores!=null && km_spcd_intcores.size()!=0){
                    for (KM_SPCD_INTCORE km_spcd_intcore : km_spcd_intcores){
                        if (km_spcd_intcore.getPort_b().equals(portAB1)){
                            portByPortA = getPortByPortA(km_spcd_cubicle,km_spcd_intcore.getPort_a());
                            //设置高亮
                            KM_SPCD_PORT portByPortHig = getPortByPortA(km_spcd_cubicle, km_spcd_intcore.getPort_a());
                            highlightPorts.add(portByPortHig);
                            break;
                        }
                        if (km_spcd_intcore.getPort_a().equals(portAB1)){
                            portByPortA = getPortByPortA(km_spcd_cubicle,km_spcd_intcore.getPort_b());
                            //设置高亮
                            KM_SPCD_PORT portByPortHig = getPortByPortA(km_spcd_cubicle, km_spcd_intcore.getPort_b());
                            highlightPorts.add(portByPortHig);
                            break;
                        }
                    }
                }
            }
            if (portByPortA != null){
                if (type == 2){
                    type =4;
                }
                findBaseData(portByPortA.getId(),type,2);//是odf，将第一个unit全部置换成新的对象
                data = getData(portByPortA.getId(), type,2);
            }
        }else {
            data = getData(id,type,1);
        }
        return data;
    }


    private OdfSwitchBean getData(Integer id,Integer type,Integer count) {
        //获取点击端口的数据
        OdfSwitchBean odfSwitchBean = getPortData(id, type,count);
        return odfSwitchBean;
//        WholeCircuitBean wholeCircuitBean1 = getPortData(id, type,count);
//        return wholeCircuitBean1;
//        //获取另外一根的数据
//        WholeCircuitBean wholeCircuitBean2 = null;
//        KM_SPCD_PORT atherPort = findAtherPort();
//        if (atherPort!=null){
//            findBaseData(atherPort.getId(),4,2);//切换成该线的id
//            wholeCircuitBean2 = getPortData(atherPort.getId(), type,2);
//        }
//        //把两边数据整理到一块
//        if (wholeCircuitBean2 != null){
//            if (wholeCircuitBean1 != null){
//                List<WholeCircuitBean.CubicleWholeBean> cubicleWholeBeans1 = wholeCircuitBean1.getCubicleWholeBeans();
//                List<WholeCircuitBean.CubicleWholeBean> cubicleWholeBeans2 = wholeCircuitBean2.getCubicleWholeBeans();
//                if (cubicleWholeBeans1!=null && cubicleWholeBeans1.size()!=0){
//                    WholeCircuitBean.CubicleWholeBean cubicleWholeBean1 = cubicleWholeBeans1.get(0);
//                    List<WholeCircuitBean.UnitWholeBean> unitWholeBeans1 = cubicleWholeBean1.getUnitWholeBeans();
//
//                    WholeCircuitBean.CubicleWholeBean cubicleWholeBean2 = cubicleWholeBeans2.get(0);
//                    List<WholeCircuitBean.UnitWholeBean> unitWholeBeans2 = cubicleWholeBean2.getUnitWholeBeans();
//
//                    if (unitWholeBeans1!=null && unitWholeBeans1.size()!=0){
//                        //是Tx的放在前面
//                        if (unitWholeBeans1.get(0).getPortA().contains("T")){
//                            circulation(cubicleWholeBeans1,cubicleWholeBeans2);
//                            return wholeCircuitBean1;
//                        }else if (unitWholeBeans2.get(0).getPortA().contains("T")){
//                            circulation(cubicleWholeBeans2,cubicleWholeBeans1);
//                            return wholeCircuitBean2;
//                        }
//                    }
//                }
//                return null;
//            }else {
//                return null;
//            }
//        }else {
//            return wholeCircuitBean1;
//        }
    }

    private void circulation(List<WholeCircuitBean.CubicleWholeBean> cubicleWholeBeans1, List<WholeCircuitBean.CubicleWholeBean> cubicleWholeBeans2){
        //A的循环,循环屏柜
        for (WholeCircuitBean.CubicleWholeBean cubicleWholeBea1:cubicleWholeBeans1){
            KM_SPCD_CUBICLE km_spcd_cubicle1 = cubicleWholeBea1.getKm_spcd_cubicle();
            List<WholeCircuitBean.UnitWholeBean> unitWholeBeans11 = cubicleWholeBea1.getUnitWholeBeans();
            //B的循环,循环屏柜
            for (WholeCircuitBean.CubicleWholeBean cubicleWholeBea2:cubicleWholeBeans2){
                List<WholeCircuitBean.UnitWholeBean> unitWholeBeans22 = cubicleWholeBea2.getUnitWholeBeans();
                //A,B是同一屏柜
                if (km_spcd_cubicle1.getId().intValue() == cubicleWholeBea2.getKm_spcd_cubicle().getId().intValue()){
                    if (unitWholeBeans11!=null && unitWholeBeans11.size()!=0){
                        //循环A的装置
                        for (WholeCircuitBean.UnitWholeBean unitWholeBean1:unitWholeBeans11){
                            if (unitWholeBeans22!=null && unitWholeBeans22.size()!=0){
                                //循环B的装置
                                for (WholeCircuitBean.UnitWholeBean unitWholeBean2:unitWholeBeans22){
                                    //A,B是同一装置
                                    if (unitWholeBean1.getKm_spcd_unit().getId().intValue() == unitWholeBean2.getKm_spcd_unit().getId().intValue()){
                                        mergeData(unitWholeBean1,unitWholeBean2);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private void mergeData(WholeCircuitBean.UnitWholeBean unitWholeBean1, WholeCircuitBean.UnitWholeBean unitWholeBean2) {
        //设置端口
        if (!TextUtils.isEmpty(unitWholeBean1.getPortADirection()) && TextUtils.isEmpty(unitWholeBean1.getPortADesc())){
            unitWholeBean1.setPortADesc("");
        }
        if (!TextUtils.isEmpty(unitWholeBean1.getPortCDirection()) && TextUtils.isEmpty(unitWholeBean1.getPortCDesc())){
            unitWholeBean1.setPortCDesc("");
        }

        unitWholeBean1.setPortBDirection(unitWholeBean2.getPortADirection());
        if (!TextUtils.isEmpty(unitWholeBean1.getPortBDirection()) && TextUtils.isEmpty(unitWholeBean2.getPortADesc())){
            unitWholeBean1.setPortBDesc("");
        }else {
            unitWholeBean1.setPortBDesc(unitWholeBean2.getPortADesc());
        }
        unitWholeBean1.setPortDDirection(unitWholeBean2.getPortCDirection());
        if (!TextUtils.isEmpty(unitWholeBean1.getPortDDirection()) && TextUtils.isEmpty(unitWholeBean2.getPortCDesc())){
            unitWholeBean1.setPortDDesc("");
        }else {
            unitWholeBean1.setPortDDesc(unitWholeBean2.getPortCDesc());
        }

        //设置线
        unitWholeBean1.setKm_spcd_intcoreB(unitWholeBean2.getKm_spcd_intcoreA());
        unitWholeBean1.setKm_spcd_coreB(unitWholeBean2.getKm_spcd_coreA());
        unitWholeBean1.setKm_spcd_intcore2(unitWholeBean2.getKm_spcd_intcore1());
    }

    private void initDao() {
        unitDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_UNIT.class, dbName);
        boardDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_BOARD.class, dbName);
        portDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_PORT.class, dbName);
        cableDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_CABLE.class, dbName);
        coreDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_CORE.class, dbName);
        cubicleDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_CUBICLE.class, dbName);
        regionDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_REGION.class, dbName);
        substationDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_SUBSTATION.class, dbName);
        intcoreDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_INTCORE.class, dbName);
        //清理掉上一个图形的所有数据
        WholeCircuitBean instance = WholeCircuitBean.getInstance();
        instance.clear();
        highlightPorts.clear();
        odfCores.clear();
    }

    private void findBaseData(Integer id,Integer type,Integer count) {
        //第一版、第二版以前的数据
        aIntcores.clear();
        bIntcores.clear();
        iedList.clear();
        switchIntCores.clear();

        portConnectBean = new PortConnectBean();
        instance = IEDsWholeCircuitDataPool.getInstance();

        if (type == 1){//传进来的是装置unitid
            km_spcd_unit = (KM_SPCD_UNIT)unitDao.getById(id);//装置
            portConnectBean.setType(1);
        }else if (type == 2){//传进来的是装置IntCoreId
            intcore = (KM_SPCD_INTCORE) intcoreDao.getById(id);
            String port_a = intcore.getPort_a();
            km_spcd_unit = SpcdUtils.getUnitByIntcoreId(mCtx, dbName, intcore);
            portConnectBean.setType(2);

            String[] split = port_a.split("\\.");
            try {
                board = (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unit.getId(), "slot", split[1]);
                String no = getNoDes(port_a, 1);
                String des = getNoDes(port_a, 2);
                port = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", board.getId(), "no", no, "direction", des);
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }else if (type == 3 || type == 4){//传进来的是装置PortId
            port = (KM_SPCD_PORT) portDao.getById(id);
            if (port == null){
                return;
            }
            board = (KM_SPCD_BOARD)boardDao.getById(port.getBoard_id());
            km_spcd_unit = SpcdUtils.getUnitByPortId(mCtx, dbName, port);
            portConnectBean.setType(3);
        }

        if (km_spcd_unit == null){
            return;
        }
        km_spcd_cubicle = (KM_SPCD_CUBICLE)cubicleDao.getById(km_spcd_unit.getCubicle_id());//屏柜
        km_spcd_region = (KM_SPCD_REGION)regionDao.getById(km_spcd_cubicle.getRegion_id());//小室
        km_spcd_substation = (KM_SPCD_SUBSTATION)substationDao.getById(km_spcd_cubicle.getSubstation_id());//厂站
        //设置高亮
        if (count == 1){//第一次进来才设置，避免重复添加数据重复
            highlightPorts.clear();
            highlightPorts.add(port);
        }
    }

    private KM_SPCD_PORT findAtherPort() {
        //找出点击进来端口的匹配的另一光纤端口
        if (port != null){
            KM_SPCD_PORT km_spcd_portOther = null;
            List<KM_SPCD_PORT> ports = portDao.getListForEq("board_id", board.getId(), "no", port.getNo());
            if (ports!=null && ports.size()!=0){
                for (KM_SPCD_PORT km_spcd_port:ports){
                    if (!km_spcd_port.getDirection().equals(port.getDirection())){
                        km_spcd_portOther = km_spcd_port;
                        return km_spcd_portOther;
                    }
                }
            }
        }
        return null;
    }

    public OdfSwitchBean getPortData(Integer id,Integer type,Integer count) {
        mType = type;
        //新建改造数据
        wholeCircuitBean = new WholeCircuitBean();
        List<WholeCircuitBean.CubicleWholeBean> list = new ArrayList<>();
        wholeCircuitBean.setHighlightPorts(highlightPorts);

        //设置本ied
        portConnectBean.setKm_spcd_unit(km_spcd_unit);
        //改造数据,设置第一个屏柜，屏柜里面的第一个ied装置
        cubicleWholeBean1 = new WholeCircuitBean.CubicleWholeBean();
        unitWholeBeanList1 = new ArrayList<>();
        unitWholeBean1 = new WholeCircuitBean.UnitWholeBean();
        unitWholeBeanList1.add(unitWholeBean1);
        list.add(cubicleWholeBean1);

        cubicleWholeBean1.setUnitWholeBeans(unitWholeBeanList1);
//        wholeCircuitBean.setCubicleWholeBeans(list);

        cubicleWholeBean1.setKm_spcd_cubicle(km_spcd_cubicle);
        unitWholeBean1.setKm_spcd_unit(km_spcd_unit);

        //1.查询IntCore中，portB、portA分别是该装置的时候
        List<KM_SPCD_INTCORE> km_spcd_intcores  = (List<KM_SPCD_INTCORE>) intcoreDao.getListForEq("cubicle_id", km_spcd_cubicle.getId());
        if (type == 1){
            if (km_spcd_intcores!=null && km_spcd_intcores.size()!=0){
                for (KM_SPCD_INTCORE km_spcd_intcore : km_spcd_intcores){
                    if (km_spcd_intcore.getPort_b().startsWith(km_spcd_unit.getName())){
                        bIntcores.add(km_spcd_intcore);
                    }
                    if (km_spcd_intcore.getPort_a().startsWith(km_spcd_unit.getName())){
                        aIntcores.add(km_spcd_intcore);
                    }
                }
            }
        }else if (type == 2){
            //如果是光纤或者odf跳进来；只有单边的连线
//            wholeCircuitBean.setType(2);
            if (intcore!=null){
                aIntcores.add(intcore);

                //设置高亮
                if (count == 1){
                    KM_SPCD_PORT portByPortA = getPortByPortA(km_spcd_cubicle, intcore.getPort_b());
                    highlightPorts.add(portByPortA);
                }

                //改造数据
                if (port!=null && board!=null){
                    unitWholeBean1.setBoardPort(board.getSlot()+"-"+port.getNo());
                    Utils.setBoardPort(unitWholeBean1,board.getSlot(),port.getNo());
                    unitWholeBean1.setPortADirection(port.getDirection());
//                    unitWholeBean1.setKm_spcd_portA(port);
                    String portAB1 = km_spcd_unit.getName() +"." + board .getSlot() + "." + port.getNo() +"-" + port.getDirection();//如：1n.1.A-Tx
                    if (km_spcd_intcores!=null && km_spcd_intcores.size()!=0){
                        for (KM_SPCD_INTCORE km_spcd_intcore : km_spcd_intcores){
                            if (km_spcd_intcore.getPort_b().equals(portAB1)){
                                unitWholeBean1.setKm_spcd_intcoreA(km_spcd_intcore);
                            }
                            if (km_spcd_intcore.getPort_a().equals(portAB1)){
                                unitWholeBean1.setKm_spcd_intcoreA(km_spcd_intcore);
                            }
                        }
                    }
                }
            }
        }else if (type == 3 || type == 4){
//            wholeCircuitBean.setType(type);
            KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD)boardDao.getById(port.getBoard_id());

            unitWholeBean1.setBoardPort(board.getSlot()+"-"+port.getNo());
            Utils.setBoardPort(unitWholeBean1,board.getSlot(),port.getNo());
//            if (type == 3){//4，如果是odf，不设置BoardPort
//                unitWholeBean1.setBoardPort(board.getSlot()+"-"+port.getNo());
//            }
            //查询另外一个端口
            String portAB1 = km_spcd_unit.getName() +"." + km_spcd_board .getSlot() + "." + port.getNo() +"-" + port.getDirection();//如：1n.1.A-Tx
            unitWholeBean1.setPortADirection(port.getDirection());
            if (type == 4){
                unitWholeBean1.setPortADesc(board.getDescription()+ "-" + port.getNo());
                unitWholeBean1.setPortAdscInt(board.getSlot()+ "-" + port.getNo());
            }
            if (km_spcd_intcores!=null && km_spcd_intcores.size()!=0){
                for (KM_SPCD_INTCORE km_spcd_intcore : km_spcd_intcores){
                    if (km_spcd_intcore.getPort_b().equals(portAB1)){
                        bIntcores.add(km_spcd_intcore);
                        unitWholeBean1.setKm_spcd_intcoreA(km_spcd_intcore);
                    }
                    if (km_spcd_intcore.getPort_a().equals(portAB1)){
                        aIntcores.add(km_spcd_intcore);
                        unitWholeBean1.setKm_spcd_intcoreA(km_spcd_intcore);
                    }
                }
                //设置高亮
                if (count == 1 && aIntcores!=null && aIntcores.size()!=0){
                    String port_b = aIntcores.get(0).getPort_b();
                    KM_SPCD_PORT portByPortA = getPortByPortA(km_spcd_cubicle, port_b);
                    highlightPorts.add(portByPortA);
                }else if (count == 1 && bIntcores!=null && bIntcores.size()!=0){
                    String port_a = bIntcores.get(0).getPort_a();
                    KM_SPCD_PORT portByPortA = getPortByPortA(km_spcd_cubicle, port_a);
                    highlightPorts.add(portByPortA);
                }
            }
        }

        //清除上一个数据集
        instance.clear();
        //1.拼装该端口
        port0 = km_spcd_unit.getName()+"."+board.getSlot()+"."+port.getNo()+"-"+port.getDirection();
        //2.设置第一个屏柜的数据
        cubicleGroupBaseBean1 = new IEDsWholeCircuitDataPool.CubicleGroupBaseBean();

        ieds1 = new ArrayList<>();
        ied1 = new IEDsWholeCircuitDataPool.IED();
        String dev_class = km_spcd_unit.getDev_class();
        if ("ODF".equals(dev_class)){
            //如果是odf，在下面代码中设置第一个屏柜该odf与之相连的装置
        }else {
            ied1.setIedName(buildUnitNameDesc(km_spcd_unit));
            ied1.setIed_portNo(port.getNo()+"-"+port.getDirection());
            ieds1.add(ied1);
            cubicleGroupBaseBean1.setCubicleName(buildCubiculeNameDesc(km_spcd_cubicle));
            cubicleGroupBaseBean1.setIeds(ieds1);
        }

        //改造3.0
        odfSwitchBean = new OdfSwitchBean();
        odfSwitchBean.setHighlightPorts(highlightPorts);
        //查询本屏柜尾缆的连接
//        setWLconnect(list);
//        setWLconnect(list,km_spcd_region,km_spcd_cubicle,port0,"",odfSwitchBean);

        List<Integer> cubicleName = new ArrayList<>();
        KM_SPCD_INTCORE km_spcd_intcore = new KM_SPCD_INTCORE();
        km_spcd_intcore.setPort_a(port0);
        km_spcd_intcore.setPort_b(getOtherPort1(port0));

        OdfSwitchBean odfSwitchBean1 = setOdfSwitchBeanFirst(this.odfSwitchBean, km_spcd_intcore, 1);
        odfSwitchBean.getConnectedBeans().clear();
        setWLconnectFinal(km_spcd_region,km_spcd_cubicle,km_spcd_intcore,port0,null,cubicleWholeBean1,unitWholeBeanList1,list,odfSwitchBean.getConnectedBeans(),odfSwitchBean,getOtherPort1(port0),cubicleName);

        if (instance.getLink_type() != IEDsWholeCircuitDataPool.TYPE_1IED_IN_1CUBICULE_STRAIT_THR){//不同屏柜直连
            //查询本屏柜连接的装置
            if (aIntcores!=null && aIntcores.size()!=0){
                setConnectBean(aIntcores,1,list);
            }
            if (bIntcores!=null && bIntcores.size()!=0){
                setConnectBean(bIntcores,2,list);
            }
        }
        instance.setGlNames(glNames);
        instance.setCubicles(cubicles);
//        return instance;
        return odfSwitchBean;
    }

    private String getOtherPort1(String port0) {
        if (!TextUtils.isEmpty(port0)){
            String txRx = getTxRx(port0);
            String[] split = port0.split("\\.");
            String nodir = split[2];
            if (!TextUtils.isEmpty(port0)){
                String[] split1 = nodir.split("-");
                String port1 = split[0]+"."+split[1]+"."+split1[0]+"-"+txRx;
                return port1;
            }
        }
        return null;
    }

    private void setConnectBean(List<KM_SPCD_INTCORE> intcores,int type,List<WholeCircuitBean.CubicleWholeBean> list) {
        if (intcores!=null && intcores.size()!=0){
            for (KM_SPCD_INTCORE km_spcd_intcore:intcores){
                String dev_class = km_spcd_unit.getDev_class();
                List<Integer> cubicleName = new ArrayList<>();
                if ("ODF".equals(dev_class)){//如果是点击的odf端口进来的
                    String port_a = km_spcd_intcore.getPort_a();
                    String port_b = km_spcd_intcore.getPort_b();
                    if (port_a.startsWith(km_spcd_unit.getName())){
                        //查找odf连接的本侧屏柜装置
                        setCubicleGroupBaseBean1(port_b);

                        String[] split = port_a.split("\\.");
                        String unitName = split[0];//unit的name，如：4n
                        OdfSwitchBean odfSwitchBean1 = setOdfSwitchBeanFirst(this.odfSwitchBean, km_spcd_intcore, 1);
                        //改造
                        KM_SPCD_UNIT otherUnit = getOtherUnit(km_spcd_cubicle, unitName);
                        if (otherUnit == null){
                            return;
                        }
                        setodfSwitchBean1Data(odfSwitchBean1,km_spcd_intcore,port_b,km_spcd_cubicle, otherUnit);
                        setWLconnectFinal(km_spcd_region,km_spcd_cubicle,km_spcd_intcore,port_a,null,cubicleWholeBean1,unitWholeBeanList1,list,odfSwitchBean.getConnectedBeans(),odfSwitchBean1,port_b,cubicleName);
                        setOtherUnit(km_spcd_intcore,unitName,null,2,km_spcd_cubicle, null,cubicleWholeBean1,unitWholeBeanList1,list, odfSwitchBean1,odfSwitchBean.getConnectedBeans());
                    }else if (port_b.startsWith(km_spcd_unit.getName())){
                        //查找odf连接的本侧屏柜装置
                        setCubicleGroupBaseBean1(port_a);

                        String[] split = port_b.split("\\.");
                        String unitName = split[0];//unit的name，如：4n

                        OdfSwitchBean odfSwitchBean1 = setOdfSwitchBeanFirst(this.odfSwitchBean, km_spcd_intcore, 2);

                        KM_SPCD_UNIT otherUnit = getOtherUnit(km_spcd_cubicle, unitName);
                        if (otherUnit == null){
                            return;
                        }
                        setodfSwitchBean1Data(odfSwitchBean1,km_spcd_intcore,port_a,km_spcd_cubicle, otherUnit);
                        setWLconnectFinal(km_spcd_region,km_spcd_cubicle,km_spcd_intcore,port_b,null,cubicleWholeBean1,unitWholeBeanList1,list,odfSwitchBean.getConnectedBeans(),odfSwitchBean1,port_a,cubicleName);
                        setOtherUnit(km_spcd_intcore,unitName,null,1,km_spcd_cubicle, null,cubicleWholeBean1,unitWholeBeanList1,list,odfSwitchBean1,odfSwitchBean.getConnectedBeans());
                    }
                }else{
                    if (type == 1){
                        String port_b = km_spcd_intcore.getPort_b();
                        if (!TextUtils.isEmpty(port_b) && port_b.contains("-")){
                            String[] split = port_b.split("\\.");
                            String unitName = split[0];//unit的name，如：4n
                            //改造，查找与之相匹配的另一个intcore
//                            String port_a = km_spcd_intcore.getPort_a();
//                            KM_SPCD_INTCORE otherIntcore = getOtherIntcore(port_a,1);
                            //根据unitName查询该装置
                            OdfSwitchBean odfSwitchBean1 = setOdfSwitchBeanFirst(this.odfSwitchBean, km_spcd_intcore, 1);

                            KM_SPCD_UNIT otherUnit = getOtherUnit(km_spcd_cubicle, unitName);
                            if (otherUnit == null){
                                return;
                            }
                            setodfSwitchBean1Data(odfSwitchBean1,km_spcd_intcore,port_b,km_spcd_cubicle,otherUnit);
                            setWLconnectFinal(km_spcd_region,km_spcd_cubicle,km_spcd_intcore,port_b,null,cubicleWholeBean1,unitWholeBeanList1,list,odfSwitchBean.getConnectedBeans(),odfSwitchBean1,port_b,cubicleName);
                            setOtherUnit(km_spcd_intcore,unitName,null,type,km_spcd_cubicle, null,cubicleWholeBean1,unitWholeBeanList1,list,odfSwitchBean1,odfSwitchBean.getConnectedBeans());
                        }
                    }else if (type == 2){
                        String port_a = km_spcd_intcore.getPort_a();
                        if (!TextUtils.isEmpty(port_a) && port_a.contains("-")){
                            String[] split = port_a.split("\\.");
                            String unitName = split[0];//unit的name，如：4n
                            //改造，查找与之相匹配的另一个intcore
//                            String port_b = km_spcd_intcore.getPort_a();
//                            KM_SPCD_INTCORE otherIntcore = getOtherIntcore(port_b,2);
                            //根据unitName查询该装置
                            OdfSwitchBean odfSwitchBean1 = setOdfSwitchBeanFirst(this.odfSwitchBean, km_spcd_intcore, 2);

                            KM_SPCD_UNIT otherUnit = getOtherUnit(km_spcd_cubicle, unitName);
                            if (otherUnit == null){
                                return;
                            }
                            setodfSwitchBean1Data(odfSwitchBean1,km_spcd_intcore,port_a,km_spcd_cubicle, otherUnit);
                            setWLconnectFinal(km_spcd_region,km_spcd_cubicle,km_spcd_intcore,port_a,null,cubicleWholeBean1,unitWholeBeanList1,list,odfSwitchBean.getConnectedBeans(),odfSwitchBean1,port_a,cubicleName);
                            setOtherUnit(km_spcd_intcore,unitName,null,type,km_spcd_cubicle, null,cubicleWholeBean1,unitWholeBeanList1,list,odfSwitchBean1,odfSwitchBean.getConnectedBeans());
                        }
                    }
                }
            }
            portConnectBean.setIedList(iedList);
        }
        Log.e("打印出来的端口连接关系",portConnectBean.toString());
    }

    public KM_SPCD_UNIT getOtherUnit(KM_SPCD_CUBICLE km_spcd_cubicle,String unitName){
        KM_SPCD_UNIT unit1 = null;
        try {
            unit1 = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", unitName);
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return unit1;
    }

    private static void setodfSwitchBean1Data(OdfSwitchBean odfSwitchBean1, KM_SPCD_INTCORE km_spcd_intcore, String port_b, KM_SPCD_CUBICLE km_spcd_cubicle, KM_SPCD_UNIT otherUnit) {
        odfSwitchBean1.setKm_spcd_unit(otherUnit);
        odfSwitchBean1.setKm_spcd_intcore(km_spcd_intcore);
        odfSwitchBean1.setType(1);
        odfSwitchBean1.setPortRx(port_b);
        //记录经过数据，物理全回路图
        setWholeCircuitBean(odfSwitchBean1,km_spcd_cubicle,3,km_spcd_unit,null,null,null,null,null);
        WholeCircuitBean.UnitWholeBean unitWholeBean2 = odfSwitchBean1.getUnitWholeBean();

        String[] split2 = port_b.split("\\.");
        String  noDir = split2[2];
        if (!TextUtils.isEmpty(noDir) && noDir.contains("-")){
            String[] split3 = noDir.split("-");
            unitWholeBean2.setBoardPort(split2[1]+"-"+split3[0]);
            unitWholeBean2.setBoard(split2[1]);
            unitWholeBean2.setPort(split3[0]);
            unitWholeBean2.setPortADirection(split3[1]);
        }

        List<OdfSwitchBean> odfAndSwitch = new ArrayList<>();
        odfSwitchBean1.setConnectedBeans(odfAndSwitch);
    }

    public OdfSwitchBean setOdfSwitchBeanFirst(OdfSwitchBean odfSwitchBean,KM_SPCD_INTCORE km_spcd_intcore,Integer type){
        odfSwitchBean.setKm_spcd_intcore(km_spcd_intcore);
        odfSwitchBean.setType(type);
        odfSwitchBean.setKm_spcd_unit(km_spcd_unit);
        String port_1 = null;
        if (type == 1 && km_spcd_intcore!=null){
            port_1 = km_spcd_intcore.getPort_a();
            odfSwitchBean.setPortRx(km_spcd_intcore.getPort_a());
        }else if (type == 2 && km_spcd_intcore!=null){
            port_1 = km_spcd_intcore.getPort_b();
            odfSwitchBean.setPortRx(km_spcd_intcore.getPort_b());
        }

        List<OdfSwitchBean> odfAndSwitch1 = new ArrayList<>();
        odfSwitchBean.setConnectedBeans(odfAndSwitch1);
        OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();//设置装置
        odfAndSwitch1.add(odfSwitchBean1);

        //记录经过数据，物理全回路图
        setWholeCircuitBean(odfSwitchBean,km_spcd_cubicle,1,km_spcd_unit,null,null,null,null,km_spcd_intcore);
        if (!TextUtils.isEmpty(port_1)){
            String[] split2 = port_1.split("\\.");
            String  noDir = split2[2];
            String[] split3 = noDir.split("-");

            WholeCircuitBean.UnitWholeBean unitWholeBean = odfSwitchBean.getUnitWholeBean();
            unitWholeBean.setBoardPort(split2[1]+"-"+split3[0]);
            unitWholeBean.setBoard(split2[1]);
            unitWholeBean.setPort(split3[0]);
            unitWholeBean.setPortADirection(split3[1]);
        }
       return odfSwitchBean1;
    }

    public void setCubicleGroupBaseBean1(String port_b){
        //查找odf连接的本侧屏柜装置
        String[] split0 = port_b.split("\\.");
        String unitName0 = split0[0];//unit的name，如：4n
        try {
            KM_SPCD_UNIT km_spcd_unit0 = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", unitName0);

            ied1.setIedName(buildUnitNameDesc(km_spcd_unit0));
            ied1.setIed_portNo(split0[2]);
            ieds1.add(ied1);
            cubicleGroupBaseBean1.setCubicleName(buildCubiculeNameDesc(km_spcd_cubicle));
            cubicleGroupBaseBean1.setIeds(ieds1);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private static int type0 = 1;//用来记录第一次进来，原始发点在哪一侧
    private static void setOtherUnit(KM_SPCD_INTCORE km_spcd_intcore, String unitName, KM_SPCD_INTCORE km_spcd_intcore0, int type, KM_SPCD_CUBICLE cubicle, IEDsWholeCircuitDataPool.CubicleGroupBaseBean secondWholeCubicle,
                                     WholeCircuitBean.CubicleWholeBean cubicleWholeBean, List<WholeCircuitBean.UnitWholeBean> unitWholeBeanList, List<WholeCircuitBean.CubicleWholeBean> list, OdfSwitchBean odfSwitchBeanReceive
                                    ,List<OdfSwitchBean> odfSwitchBeanList) {
        if (km_spcd_intcore!=null && km_spcd_intcore0 ==null){
            type0 = type;
            switchIntCores.clear();//将上一个查找的标记不循环的清空，重新循环
        }
        KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getById(cubicle.getRegion_id());

        //参数：1.第一次循环的原始intcore   2.对侧unitName,如：4n   3.多次循环传进来的   4.
        try {
            //本侧portA
            String portA = null;

            KM_SPCD_UNIT km_spcd_unit = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", cubicle.getId(), "name", unitName);
            if (km_spcd_unit == null){
//                Toast.makeText(mCtx, "未找到装置信息", Toast.LENGTH_LONG).show();
                return;
            }
            String dev_class = km_spcd_unit.getDev_class();//获取连接装置的类型，如：ied,odf,switch
            String port_1 = null;//对测端口
            if (km_spcd_intcore0 == null){
                if (type == 1){
                    portA = km_spcd_intcore.getPort_a();
                    port_1 = km_spcd_intcore.getPort_b();
                }else if (type == 2){
                    portA = km_spcd_intcore.getPort_b();
                    port_1 = km_spcd_intcore.getPort_a();
                }
            }else {
                if (type == 1){
                    portA = km_spcd_intcore0.getPort_a();
                    port_1 = km_spcd_intcore0.getPort_b();
                }else if (type == 2){
                    portA = km_spcd_intcore0.getPort_b();
                    port_1 = km_spcd_intcore0.getPort_a();
                }
            }
            String[] split2 = port_1.split("\\.");
            String  noDir = split2[2];
            if ("IED".equals(dev_class)){//已经找到装置，结束
                //改造
                WholeCircuitBean.UnitWholeBean unitWholeBean = new WholeCircuitBean.UnitWholeBean();//收尾装置，同一屏柜
                unitWholeBean.setKm_spcd_unit(km_spcd_unit);
                String[] split3 = null;
                if (!TextUtils.isEmpty(noDir) && noDir.contains("-")){
                    split3 = noDir.split("-");
                    unitWholeBean.setBoardPort(split2[1]+"-"+split3[0]);
                    Utils.setBoardPort(unitWholeBean,split2[1],split3[0]);
                    unitWholeBean.setPortADirection(split3[1]);
                }
                unitWholeBeanList.add(unitWholeBean);

                IEDsWholeCircuitDataPool.IED ied2 = new IEDsWholeCircuitDataPool.IED();
                ied2.setIedName(buildUnitNameDesc(km_spcd_unit));
                ied2.setIed_portNo(split2[2]);
                if(km_spcd_intcore0 == null){//一个循环就找到装置
                    //设置对侧装置
                    ieds1.add(ied2);
                    cubicleGroupBaseBean1.setIeds(ieds1);
                    //设置类型
                    cubicleGroupBaseBean1.setLocationOf(SINGLE);
                    //设置连接类型
                    instance.setLink_type(IEDsWholeCircuitDataPool.TYPE_2IED_IN_1CUBICULE);
                    //3.添加第一个屏柜
                    cubicles.add(cubicleGroupBaseBean1);

                    unitWholeBean.setKm_spcd_intcoreA(km_spcd_intcore);
                    //改造3.0
                    odfSwitchBeanReceive.setKm_spcd_intcore(km_spcd_intcore);

                    //记录经过数据，物理全回路图
                    setWholeCircuitBean(odfSwitchBeanReceive,cubicle,1,km_spcd_unit,split2[1]+"-"+split3[0],split2[1],split3[0],split3[1],km_spcd_intcore);
                }else {
                    List<IEDsWholeCircuitDataPool.IED> ieds2 = new ArrayList<>();
                    ieds2.add(ied2);
                    if (secondWholeCubicle != null){
                        secondWholeCubicle.setIeds(ieds2);
                        cubicles.add(secondWholeCubicle);
                    }

                    unitWholeBean.setKm_spcd_intcoreA(km_spcd_intcore0);
                    //改造3.0
                    odfSwitchBeanReceive.setKm_spcd_intcore(km_spcd_intcore0);

                    //记录经过数据，物理全回路图
                    setWholeCircuitBean(odfSwitchBeanReceive,cubicle,1,km_spcd_unit,split2[1]+"-"+split3[0],split2[1],split3[0],split3[1],km_spcd_intcore0);
                }
                odfSwitchBeanReceive.setType(1);
                odfSwitchBeanReceive.setKm_spcd_unit(km_spcd_unit);
                odfSwitchBeanReceive.setPortRx(port_1);
            }else if ("SWITCH".equals(dev_class)){//连接的是交换机，接着找装置
                IEDsWholeCircuitDataPool.IED ied2 = new IEDsWholeCircuitDataPool.IED();
                ied2.setIedName(buildUnitNameDesc(km_spcd_unit));
                ied2.setIed_portNo(split2[2]);

                //改造3.0
                odfSwitchBeanReceive.setKm_spcd_unit(km_spcd_unit);//设置第一个交换机

                if(km_spcd_intcore0 == null) {//一个循环就找到交换机
                    ieds1.add(ied2);
                    cubicleGroupBaseBean1.setIeds(ieds1);
                    cubicleGroupBaseBean1.setLocationOf(SINGLE);
                    instance.setLink_type(IEDsWholeCircuitDataPool.TYPE_2IED_IN_1CUBICULE);
                    //3.添加第一个屏柜
                    cubicles.add(cubicleGroupBaseBean1);
                    //改造3.0
                    odfSwitchBeanReceive.setKm_spcd_intcore(km_spcd_intcore);
                }else {
                    List<IEDsWholeCircuitDataPool.IED> ieds2 = new ArrayList<>();
                    ieds2.add(ied2);
                    if (secondWholeCubicle != null){
                        secondWholeCubicle.setIeds(ieds2);
                        cubicles.add(secondWholeCubicle);
                    }
                    //改造3.0
                    odfSwitchBeanReceive.setKm_spcd_intcore(km_spcd_intcore0);
                }
                odfSwitchBeanReceive.setType(1);
                odfSwitchBeanReceive.setPortRx(port_1);

                WholeCircuitBean.UnitWholeBean unitWholeBean = new WholeCircuitBean.UnitWholeBean();//收尾装置，同一屏柜
                unitWholeBean.setKm_spcd_unit(km_spcd_unit);
                if (!TextUtils.isEmpty(noDir) && noDir.contains("-")){
                    String[] split3 = noDir.split("-");
                    unitWholeBean.setBoardPort(split2[1]+"-"+split3[0]);
                    Utils.setBoardPort(unitWholeBean,split2[1],split3[0]);
                    unitWholeBean.setPortADirection(split3[1]);
                }
                String[] split4 = portA.split("\\.");
                String s1 = split4[2];
                unitWholeBeanList.add(unitWholeBean);


                //记录经过数据，物理全回路图
                setWholeCircuitBean(odfSwitchBeanReceive,cubicle,3,km_spcd_unit,null,null,null,null,null);
                WholeCircuitBean.UnitWholeBean unitWholeBean2 = odfSwitchBeanReceive.getUnitWholeBean();
                if (!TextUtils.isEmpty(noDir) && noDir.contains("-")){
                    String[] split3 = noDir.split("-");
                    unitWholeBean2.setBoardPort(split2[1]+"-"+split3[0]);
                    unitWholeBean2.setBoard(split2[1]);
                    unitWholeBean2.setPort(split3[0]);
                    unitWholeBean2.setPortADirection(split3[1]);
                }

                //先查找尾缆
                List<OdfSwitchBean> odfAndSwitchList = new ArrayList<>();
                odfSwitchBeanReceive.setConnectedBeans(odfAndSwitchList);

//                //设置第二个交换机
//                OdfSwitchBean odfSwitchBean11 = new OdfSwitchBean();
////                setSwitchPort1(unitWholeBean2,port_a, odfSwitchBean11);
//                setOdfSwitchBeanData(odfSwitchBean11,odfSwitchBeanReceive);
////                odfSwitchBeanList.add(odfSwitchBean11);
//
//                List<OdfSwitchBean> odfAndSwitchList11 = new ArrayList<>();
//                odfSwitchBean11.setOdfAndSwitch(odfAndSwitchList11);
////                //设置第三个装置
////                OdfSwitchBean odfSwitchBean22 = new OdfSwitchBean();
////                List<OdfSwitchBean> odfAndSwitch22 = new ArrayList<>();
////                odfSwitchBean22.setOdfAndSwitch(odfAndSwitch22);
////                odfAndSwitchList11.add(odfSwitchBean22);
//                setWLconnectFinal(km_spcd_region,cubicle,km_spcd_intcore,unitName,null,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBeanList,odfSwitchBean11,port_1);

                //找到交换机再循环直到找到装置
                if (!switchIntCores.contains(km_spcd_intcore.getName())){//如果是交换机，加入到集合，后面不再遍历该条数据
                    switchIntCores.add(km_spcd_intcore.getName());
                }
                if (km_spcd_intcore0!=null && !switchIntCores.contains(km_spcd_intcore0.getName())){
                    switchIntCores.add(km_spcd_intcore0.getName());
                }
                //先查找尾缆
                if (km_spcd_intcore0 == null){
                    if (km_spcd_intcore.getPort_a().startsWith(unitName)){
//                        setWLconnect(km_spcd_intcore.getPort_a());
                    }else if (km_spcd_intcore.getPort_b().startsWith(unitName)){
//                        setWLconnect(km_spcd_intcore.getPort_b());
                    }
                }else {
                    if (km_spcd_intcore0.getPort_a().startsWith(unitName)){
//                        setWLconnect(km_spcd_intcore0.getPort_a());
                    }else if (km_spcd_intcore0.getPort_b().startsWith(unitName)){
//                        setWLconnect(km_spcd_intcore0.getPort_b());
                    }
                }

                //如果进来的装置是Rx,出去的装置只能是Tx;反之亦然
                String txRx = getTxRx(port_1);

                String switchName = km_spcd_unit.getName();//交换机名字，如：4n----这个是portA为交换机的时候
                //查询与switchName交换机相连的ied
                List<KM_SPCD_INTCORE> switchIntcores = ( List<KM_SPCD_INTCORE>)intcoreDao.getListForEq("cubicle_id", km_spcd_unit.getCubicle_id());
                List<KM_SPCD_INTCORE> switchIntcoresNo = new ArrayList<>();//与交换机相连的装置，有可能是ied，switch，odf
                if (switchIntcores!=null && switchIntcores.size()!=0){
                    for (KM_SPCD_INTCORE km_spcd_intcore1 : switchIntcores){
                        if (!switchIntCores.contains(km_spcd_intcore1.getName())){//已经匹配过得交换机不添加到集合
                            if (km_spcd_intcore1.getPort_a().startsWith(switchName) && km_spcd_intcore1.getPort_a().contains(txRx)){//Port_a是交换机的
                                switchIntcoresNo.add(km_spcd_intcore1);
                            }else if (km_spcd_intcore1.getPort_b().startsWith(switchName)  && km_spcd_intcore1.getPort_b().contains(txRx)){//port_b是交换机的
                                switchIntcoresNo.add(km_spcd_intcore1);
                            }
                        }
                    }
                }

                if (switchIntcoresNo!=null && switchIntcoresNo.size()!=0){
                    for (KM_SPCD_INTCORE km_spcd_intcore2: switchIntcoresNo){
                        String port_a = km_spcd_intcore2.getPort_a();//如：4n.1.C-Tx
                        String port_b = km_spcd_intcore2.getPort_b();//如：13n.1.C-Tx
                        if (!TextUtils.isEmpty(port_a) && port_a.startsWith(switchName) && port_a.endsWith(txRx)){//如果portA="4n.1.C-Tx" 是交换机
                            if (!TextUtils.isEmpty(port_b)){
                                String[] split = port_b.split("\\.");
                                String unitNmae1 = split[0];//对侧unitNmae
                                switchIntCores.add(km_spcd_intcore2.getName());//防止死循环

                                //记录经过数据，物理全回路图
                                odfSwitchBeanReceive.setPortTx(port_a);

                                //设置第二个交换机
                                OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();
                                setSwitchPort1(unitWholeBean2,port_a, odfSwitchBean1);
                                setOdfSwitchBeanData(odfSwitchBean1,odfSwitchBeanReceive);
                                odfSwitchBeanList.add(odfSwitchBean1);

                                List<OdfSwitchBean> odfAndSwitchList1 = new ArrayList<>();
                                odfSwitchBean1.setConnectedBeans(odfAndSwitchList1);
                                //设置第三个装置
                                OdfSwitchBean odfSwitchBean2 = new OdfSwitchBean();
                                List<OdfSwitchBean> odfAndSwitch2 = new ArrayList<>();
                                odfSwitchBean2.setConnectedBeans(odfAndSwitch2);
                                odfAndSwitchList1.add(odfSwitchBean2);

                                setOtherUnit(km_spcd_intcore,unitNmae1,km_spcd_intcore2, 1,cubicle,null,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBean2,odfAndSwitchList1);
                            }
                        }else if (!TextUtils.isEmpty(port_b) && port_b.startsWith(switchName) && port_b.endsWith(txRx)){//如果portB="4n.1.C-Tx" 是交换机
                            if (!TextUtils.isEmpty(port_a)){
                                String[] split = port_a.split("\\.");
                                String unitNmae1 = split[0];//对侧unitNmae
                                switchIntCores.add(km_spcd_intcore2.getName());//防止死循环

                                //记录经过数据，物理全回路图
                                odfSwitchBeanReceive.setPortTx(port_b);

                                //设置第二个交换机
                                OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();
                                setSwitchPort1(unitWholeBean2,port_a, odfSwitchBean1);
                                setOdfSwitchBeanData(odfSwitchBean1,odfSwitchBeanReceive);
                                odfSwitchBeanList.add(odfSwitchBean1);

                                List<OdfSwitchBean> odfAndSwitchList1 = new ArrayList<>();
                                odfSwitchBean1.setConnectedBeans(odfAndSwitchList1);
                                //设置第三个装置
                                OdfSwitchBean odfSwitchBean2 = new OdfSwitchBean();
                                List<OdfSwitchBean> odfAndSwitch2 = new ArrayList<>();
                                odfSwitchBean2.setConnectedBeans(odfAndSwitch2);
                                odfAndSwitchList1.add(odfSwitchBean2);

                                setOtherUnit(km_spcd_intcore,unitNmae1,km_spcd_intcore2, 2,cubicle,null,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBean2,odfAndSwitchList1);
                            }
                        }
//                        odfAndSwitch.add(odfSwitchBean);
                    }
                }
            }else if ("ODF".equals(dev_class)){//连接的是odf,接着找对侧屏柜的装置
                String port_a = null;
                String port_b = null;
                odfSwitchBeanReceive.setKm_spcd_unit(km_spcd_unit);
                if (km_spcd_intcore0 == null){
                    port_a = km_spcd_intcore.getPort_a();
                    port_b = km_spcd_intcore.getPort_b();
                    //给第一个屏柜设置odf数据(unit.name-unit.desc)
                    StringBuffer stringBuffer = new StringBuffer(" ");
                    stringBuffer.append(km_spcd_unit.getName())
                            .append("-")
                            .append(km_spcd_unit.getDescription());
                    cubicleGroupBaseBean1.setOdf_name(stringBuffer.toString());
                    cubicleGroupBaseBean1.setOdf_portFrNo(split2[2]);
                    cubicleGroupBaseBean1.setLocationOf(FROM);
                    instance.setLink_type(IEDsWholeCircuitDataPool.TYPE_1IED_IN_1CUBICULE_BY_ODF);
                    cubicles.add(cubicleGroupBaseBean1);

                    //改造3.0
                    odfSwitchBeanReceive.setKm_spcd_intcore(km_spcd_intcore);
                }else {
                    port_a = km_spcd_intcore0.getPort_a();
                    port_b = km_spcd_intcore0.getPort_b();
                    //改造3.0
                    odfSwitchBeanReceive.setKm_spcd_intcore(km_spcd_intcore0);
                }
                odfSwitchBeanReceive.setType(1);
                odfSwitchBeanReceive.setPortRx(port_1);
                //继续查找
                String portOdf = null;
                if (type == 2 && port_a.startsWith(unitName)){//port_a 是odf
                    portOdf = port_a;
                }else if (type == 1 && port_b.startsWith(unitName)){//port_b 是odf
                    portOdf = port_b;
                }

                //改造如果是odf ----可先在本屏柜找是否有多个odf（待做）
                WholeCircuitBean.UnitWholeBean unitWholeBean = new WholeCircuitBean.UnitWholeBean();
                unitWholeBean.setKm_spcd_unit(km_spcd_unit);
                String[] noDirSp = null;
                if (noDir!=null && noDir.contains("-")){
                    noDirSp = noDir.split("-");
                    unitWholeBean.setPortADirection(noDirSp[1]);
                    //查询该odf板卡
                    KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) boardDao.getFirstForEq("unit_id", km_spcd_unit.getId(), "slot", split2[1]);
                    List<KM_SPCD_PORT> ports = portDao.getListForEq("board_id", km_spcd_board.getId(), "no", noDirSp[0]);
                    unitWholeBean.setPortADesc(km_spcd_board.getDescription()+"-"+noDirSp[0]);
                    unitWholeBean.setPortAdscInt(km_spcd_board.getSlot()+"-"+noDirSp[0]);

                    //新版全回路补全
                    odfSwitchBeanReceive.setPortAdsc(km_spcd_board.getDescription()+"-"+noDirSp[0]);
                    odfSwitchBeanReceive.setPortAdscInt(km_spcd_board.getSlot()+"-"+noDirSp[0]);

                    //记录经过数据，物理全回路图
                    setWholeCircuitBean(odfSwitchBeanReceive,cubicle,2,km_spcd_unit,split2[1]+"-"+noDirSp[0],split2[1],split2[1],noDirSp[1],null);
                    WholeCircuitBean.UnitWholeBean unitWholeBean2 = odfSwitchBeanReceive.getUnitWholeBean();
                    unitWholeBean2.setPortADesc(km_spcd_board.getDescription()+"-"+noDirSp[0]);
                    unitWholeBean2.setPortAdscInt(km_spcd_board.getSlot()+"-"+noDirSp[0]);
                    if (km_spcd_intcore0 == null){
                        unitWholeBean2.setKm_spcd_intcoreA(km_spcd_intcore);
                    }else {
                        unitWholeBean2.setKm_spcd_intcoreA(km_spcd_intcore0);
                    }
                }
                unitWholeBeanList.add(unitWholeBean);

                //根据odf去查找Cable连接
                String cubicleC = km_spcd_region.getName() +"." + cubicle.getName();//如：cubicleA="R66.XLKZ1A"
                //查找与portOdf相连的屏柜
                List<String> keys = new ArrayList<String>(asList("station_id", "cubicleA"));
                List<Object> value = new ArrayList<Object>(asList(km_spcd_substation.getId(),cubicleC));
                List<KM_SPCD_CABLE> cablesA = (List<KM_SPCD_CABLE>) cableDao.getListForEq(keys, value);//cubicleA是当前屏柜

                List<String> keys0 = new ArrayList<String>(asList("station_id", "cubicleB"));
                List<KM_SPCD_CABLE> cablesB = (List<KM_SPCD_CABLE>) cableDao.getListForEq(keys0, value);//cubicleA是当前屏柜

                analysisCables(cablesA,portOdf,km_spcd_intcore,1,list,1,odfSwitchBeanReceive);
                analysisCables(cablesB,portOdf, km_spcd_intcore, 2,list,1,odfSwitchBeanReceive);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private static void setOdfSwitchBeanData(OdfSwitchBean odfSwitchBean2, OdfSwitchBean odfSwitchBeanReceive) {
        odfSwitchBean2.setType(odfSwitchBeanReceive.getType());
        odfSwitchBean2.setKm_spcd_unit(odfSwitchBeanReceive.getKm_spcd_unit());
        odfSwitchBean2.setKm_spcd_intcore(odfSwitchBeanReceive.getKm_spcd_intcore());
        odfSwitchBean2.setKm_spcd_core(odfSwitchBeanReceive.getKm_spcd_core());
        odfSwitchBean2.setKm_spcd_cable(odfSwitchBeanReceive.getKm_spcd_cable());
        odfSwitchBean2.setPortRx(odfSwitchBeanReceive.getPortRx());
        odfSwitchBean2.setPortTx(odfSwitchBeanReceive.getPortTx());
        odfSwitchBean2.setConnectedBeans(odfSwitchBeanReceive.getConnectedBeans());
        odfSwitchBean2.setPortAdsc(odfSwitchBeanReceive.getPortAdsc());
        odfSwitchBean2.setPortAdscInt(odfSwitchBeanReceive.getPortAdscInt());
        odfSwitchBean2.setBoardPort(odfSwitchBeanReceive.getBoardPort());
        odfSwitchBean2.setBoard(odfSwitchBeanReceive.getBoard());
        odfSwitchBean2.setPort(odfSwitchBeanReceive.getPort());
        odfSwitchBean2.setKm_spcd_cubicle(odfSwitchBeanReceive.getKm_spcd_cubicle());
        odfSwitchBean2.setUnitWholeBean(odfSwitchBeanReceive.getUnitWholeBean());
        odfSwitchBean2.setHighlightPorts(odfSwitchBeanReceive.getHighlightPorts());

    }

    public static void setSwitchPort1(WholeCircuitBean.UnitWholeBean unitWholeBean2, String port_a, OdfSwitchBean odfSwitchBean0){
        if (!TextUtils.isEmpty(port_a)){
            odfSwitchBean0.setPortTx(port_a);

            String[] split1 = port_a.split("\\.");
            String noDir = split1[2];
            if (!TextUtils.isEmpty(port_a)){
                String[] split = noDir.split("-");

                unitWholeBean2.setPort1(split[0]);
                unitWholeBean2.setPortA1Direction(split[1]);
            }
            unitWholeBean2.setBoard1(split1[1]);
        }
    }

    public static void setWholeCircuitBean(OdfSwitchBean odfSwitchBean, KM_SPCD_CUBICLE km_spcd_cubicle, Integer type, KM_SPCD_UNIT km_spcd_unit, String boardPort,
                                           String board, String port,String PortA,KM_SPCD_INTCORE km_spcd_intcoreA){
        odfSwitchBean.setKm_spcd_cubicle(km_spcd_cubicle);

        WholeCircuitBean.UnitWholeBean unitWholeBean = odfSwitchBean.getUnitWholeBean();
        if (unitWholeBean == null){
            unitWholeBean = new WholeCircuitBean.UnitWholeBean();
            odfSwitchBean.setUnitWholeBean(unitWholeBean);
        }
        //设置数据
        if (km_spcd_unit!=null){
            unitWholeBean.setKm_spcd_unit(km_spcd_unit);
        }
        if (boardPort!=null){
            unitWholeBean.setBoardPort(boardPort);
        }
        if (board!=null){
            unitWholeBean.setBoard(board);
        }
        if (port!=null){
            unitWholeBean.setPort(port);
        }
        if (PortA!=null){
            unitWholeBean.setPortADirection(PortA);
        }
        if (km_spcd_intcoreA != null){
            unitWholeBean.setKm_spcd_intcoreA(km_spcd_intcoreA);
        }
    }

    private static void setWLconnectFinal(KM_SPCD_REGION km_spcd_region, KM_SPCD_CUBICLE km_spcd_cubicle, KM_SPCD_INTCORE km_spcd_intcore, String unitName, IEDsWholeCircuitDataPool.CubicleGroupBaseBean secondWholeCubicle,
                                           WholeCircuitBean.CubicleWholeBean cubicleWholeBean, List<WholeCircuitBean.UnitWholeBean> unitWholeBeanList, List<WholeCircuitBean.CubicleWholeBean> list,
                                           List<OdfSwitchBean> odfAndSwitch, OdfSwitchBean odfSwitchBeanReceive,String port_1,List<Integer> cubicleName) {
        //记录中间找过的屏柜
        cubicleName.add(km_spcd_cubicle.getId());

        //拼装当前装置的尾缆cubicleA="R66.XLKZ1A"
        String cubicleC= km_spcd_region.getName()+"."+km_spcd_cubicle.getName();
        List<String> keys1 = new ArrayList<String>(asList("station_id", "cubicleA"));
        List<String> keys2 = new ArrayList<String>(asList("station_id", "cubicleB"));
        List<Object> value = new ArrayList<Object>(asList(km_spcd_substation.getId(),cubicleC));
        try {
            List<KM_SPCD_CABLE> cables = new ArrayList<>();
            List<KM_SPCD_CABLE> cables1 = (List<KM_SPCD_CABLE>)cableDao.getListForEq(keys1, value);
            List<KM_SPCD_CABLE> cables2 = (List<KM_SPCD_CABLE>)cableDao.getListForEq(keys2, value);
            if (cables1!=null && cables1.size()!=0){
                cables.addAll(cables1);
            }
            if (cables2!=null && cables2.size()!=0){
                cables.addAll(cables2);
            }
            if (cables.size()!=0){
                for (KM_SPCD_CABLE km_spcd_cable:cables){
                    String cubicleA = km_spcd_cable.getCubicleA();
                    String cubicleB = km_spcd_cable.getCubicleB();
                    if (cubicleA.equals(cubicleC)){//cubicleB
                        coresWlFinal(km_spcd_cable,cubicleA,cubicleB,1,unitName,km_spcd_intcore,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfAndSwitch,odfSwitchBeanReceive,port_1,cubicleName);
                    }else if (cubicleB.equals(cubicleC)){
                        coresWlFinal(km_spcd_cable,cubicleB,cubicleA,2,unitName,km_spcd_intcore,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfAndSwitch,odfSwitchBeanReceive,port_1,cubicleName);
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public static String getTxRx(String port){
        String port1 = null;
        if (port.endsWith("Tx")){
            port1 = "Rx";
        }else if (port.endsWith("Rx")){
            port1 = "Tx";
        }else if (port.endsWith("RT")){
            port1 = "RT";
        }
        return port1;
    }

    private static void coresWlFinal(KM_SPCD_CABLE km_spcd_cable, String cubicle1,String cubicle0, int type, String unitName1, KM_SPCD_INTCORE km_spcd_intcore,IEDsWholeCircuitDataPool.CubicleGroupBaseBean secondWholeCubicle,
                                     WholeCircuitBean.CubicleWholeBean cubicleWholeBean, List<WholeCircuitBean.UnitWholeBean> unitWholeBeanList,List<WholeCircuitBean.CubicleWholeBean> list,
                                     List<OdfSwitchBean> odfAndSwitch0,OdfSwitchBean odfSwitchBeanReceive,String port1,List<Integer> cubicleName){
        //判断交换机连出去的口应该是Rx或Tx
        String txRx = getTxRx(port1);

        List<KM_SPCD_CORE> cores = coreDao.getListForEq("cable_id", km_spcd_cable.getId());
        if (cores!=null && cores.size()!=0){
            for (KM_SPCD_CORE km_spcd_core:cores){
                String port_a = km_spcd_core.getPort_a();
                String port_b = km_spcd_core.getPort_b();
                if (type == 1){//port_a是本屏柜装置，port_b是对侧屏柜装置
                    if (!TextUtils.isEmpty(unitName1)){
                        String[] split1 = unitName1.split("\\.");
                        String unitName = split1[0];

                        KM_SPCD_UNIT unit0 = null;
                        if(!TextUtils.isEmpty(port_a)){
                            unit0 = getUnit(km_spcd_cable.getStation_id(), cubicle1, port_a);//本侧装置
                        }else {
                            continue;
                        }
                        KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicle0, port_b);
                        KM_SPCD_CUBICLE cubicle = getCubicle(1, cubicle0);//对侧屏柜
                        KM_SPCD_REGION region = getRegion(cubicle);//对侧小室
                        if (cubicle == null){
                            return;
                        }

                        int isGoOn = 0;
                        OdfSwitchBean odfSwitchBean1 = null;
                        List<OdfSwitchBean> odfAndSwitchList1 = null;
                        if (port_a.equals(unitName1) && !odfCores.contains(km_spcd_cable.getName()+"/"+km_spcd_core.getNo())
                                && !cubicleName.contains(cubicle.getId())){

                            if (unit0.getDev_class().equals("IED") || unit0.getDev_class().equals("ODF")){
                                odfSwitchBean1 = odfSwitchBeanReceive;
//                            setOdfSwitchBeanData(odfSwitchBean1,odfSwitchBeanReceive);
                                odfAndSwitchList1 = odfSwitchBeanReceive.getConnectedBeans();
                                if (odfAndSwitchList1 == null){
                                    odfAndSwitchList1 = new ArrayList<>();
                                    odfSwitchBeanReceive.setConnectedBeans(odfAndSwitchList1);
                                }

                                isGoOn = 1;
                            }
                        }
                        if (unit0.getDev_class().equals("SWITCH") && port_a.startsWith(unitName) && port_a.endsWith(txRx) && !odfCores.contains(km_spcd_cable.getName()+"/"+km_spcd_core.getNo())
                                && !cubicleName.contains(cubicle.getId())){

                            odfSwitchBean1 = new OdfSwitchBean();
                            setOdfSwitchBeanData(odfSwitchBean1,odfSwitchBeanReceive);
                            odfAndSwitch0.add(odfSwitchBean1);
                            odfAndSwitchList1 = new ArrayList<>();
                            odfSwitchBean1.setConnectedBeans(odfAndSwitchList1);

                            isGoOn = 1;
                        }

//                        if(unit0.getDev_class().equals("SWITCH") && port_a.startsWith(unitName) && port_a.endsWith(txRx) && !odfCores.contains(km_spcd_cable.getName()+"/"+km_spcd_core.getNo())
//                                && !cubicleName.contains(cubicle.getId())){
                        if(isGoOn == 1){
//                            OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();
//                            setOdfSwitchBeanData(odfSwitchBean1,odfSwitchBeanReceive);
//                            odfAndSwitch0.add(odfSwitchBean1);
//                            List<OdfSwitchBean> odfAndSwitchList1 = new ArrayList<>();
//                            odfSwitchBean1.setOdfAndSwitch(odfAndSwitchList1);

                            //记录找过的缆纤芯不要再找了
                            odfCores.add(km_spcd_cable.getName()+"/"+km_spcd_core.getNo());

                            List<KM_SPCD_INTCORE> intcores = intcoreDao.getListForEq("cubicle_id", cubicle.getId());
                            if (unit.getDev_class().equals("IED")){//对侧装置，是否要区分交换机、odf、ied
                                odfSwitchBean1.setPortTx(port_a);

                                OdfSwitchBean odfSwitchBean = new OdfSwitchBean();//设置第二个交换机
                                odfAndSwitchList1.add(odfSwitchBean);

                                //记录经过数据，物理全回路图
                                setWholeCircuitBean(odfSwitchBean,cubicle,1,unit,getBoardPort(port_b),getBoard(port_b),getPort(port_b,1),getPort(port_b,2),null);
                                odfSwitchBean.setType(1);
                                odfSwitchBean.setKm_spcd_unit(unit);
                                odfSwitchBean.setPortRx(port_b);
                            }else if (unit.getDev_class().equals("ODF")){
                                int isGo = -1;
                                cubicleName.add(cubicle.getId());
                                for (KM_SPCD_INTCORE intcore:intcores){
                                    if (intcore.getPort_a().equals(port_b)){
                                        //装odf
                                        OdfSwitchBean odfSwitchBean2 = new OdfSwitchBean();
                                        List<OdfSwitchBean> OdfSwitchBeanList2 = new ArrayList<>();
                                        odfSwitchBean2.setConnectedBeans(OdfSwitchBeanList2);

                                        odfAndSwitchList1.add(odfSwitchBean2);
                                        setOdfSwitchBeanVlue(odfSwitchBean2,cubicle,unit,port_b,getBoardPort(port_b),getBoard(port_b),cubicle0);
                                        setOdfUnit(port_b,odfSwitchBean2);
                                        //对侧装置
                                        String port_b1 = intcore.getPort_b();
                                        String[] split = port_b1.split("\\.");
                                        //改造3.0
                                        OdfSwitchBean odfSwitchBean = new OdfSwitchBean();//设置第二个交换机
                                        OdfSwitchBeanList2.add(odfSwitchBean);

                                        setOtherUnit(km_spcd_intcore,split[0],intcore,1,cubicle,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBean,odfAndSwitchList1);

                                        isGo = 1;//找到之后结束外面的循环标志
                                    }else if (intcore.getPort_b().equals(port_b)){
                                        //装odf
                                        OdfSwitchBean odfSwitchBean2 = new OdfSwitchBean();
                                        List<OdfSwitchBean>  OdfSwitchBeanList2 = new ArrayList<>();
                                        odfSwitchBean2.setConnectedBeans(OdfSwitchBeanList2);

                                        odfAndSwitchList1.add(odfSwitchBean2);
                                        setOdfSwitchBeanVlue(odfSwitchBean2,cubicle,unit,port_b,getBoardPort(port_b),getBoard(port_b),cubicle0);
                                        setOdfUnit(port_b,odfSwitchBean2);
                                        //对侧装置
                                        String port_a1 = intcore.getPort_a();
                                        String[] split = port_a1.split("\\.");
                                        //改造3.0
                                        OdfSwitchBean odfSwitchBean = new OdfSwitchBean();//设置第二个交换机
                                        OdfSwitchBeanList2.add(odfSwitchBean);

                                        setOtherUnit(km_spcd_intcore,split[0],intcore,2,cubicle,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBean,odfAndSwitchList1);

                                        isGo = 1;//找到之后结束外面的循环标志
                                    }
                                }
                                if (isGo!=1){
                                    //找交换机数据---设置新版数据值
                                    OdfSwitchBean odfSwitchBean2 = new OdfSwitchBean();
                                    odfAndSwitchList1.add(odfSwitchBean2);
                                    setOdfSwitchBeanVlue(odfSwitchBean2,cubicle,unit,port_b,getBoardPort(port_b),getBoard(port_b),cubicle0);
                                    setOdfUnit(port_b,odfSwitchBean2);

                                    setWLconnectFinal(region,cubicle,km_spcd_intcore,port_b,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfAndSwitchList1,odfSwitchBean2,port_a,cubicleName);
                                }
                            }else if (unit.getDev_class().equals("SWITCH")){
                                //先查找intcore
                                for (KM_SPCD_INTCORE intcore:intcores){
                                    if (intcore.getPort_a().startsWith(unit.getName()) && intcore.getPort_a().endsWith(txRx)){
                                        String port_b1 = intcore.getPort_b();
                                        String[] split = port_b1.split("\\.");

                                        //改造3.0
                                        OdfSwitchBean odfSwitchBean = setSwithUnit(odfAndSwitchList1,port_a,port_b,2,unit, km_spcd_core,km_spcd_cable,cubicle,intcore.getPort_a(),odfSwitchBean1, 0);

                                        setOtherUnit(km_spcd_intcore,split[0],intcore,1,cubicle,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBean,odfAndSwitchList1);
                                    }else if (intcore.getPort_b().startsWith(unit.getName()) && intcore.getPort_b().endsWith(txRx)){
                                        String port_a1 = intcore.getPort_a();
                                        String[] split = port_a1.split("\\.");
                                        //改造3.0---设置第二个交换机
                                        OdfSwitchBean odfSwitchBean = setSwithUnit(odfAndSwitchList1,port_a,port_b,2,unit, km_spcd_core,km_spcd_cable, cubicle, intcore.getPort_b(),odfSwitchBean1, 0);

                                        setOtherUnit(km_spcd_intcore,split[0],intcore,2,cubicle,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBean,odfAndSwitchList1);
                                    }
                                }
                                //再接着查找缆,找过的缆不要再找了，避免死循环
                                cubicleName.add(cubicle.getId());

                                OdfSwitchBean odfSwitchBean = setSwithUnit(odfAndSwitchList1,port_a,port_b,2,unit, km_spcd_core,km_spcd_cable, cubicle,port_b,odfSwitchBean1,1);
//                                setodfSwitchBean1Data(odfSwitchBean,null,port_b,cubicle, unit);
                                setWLconnectFinal(region,cubicle,km_spcd_intcore,port_b,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBean1.getConnectedBeans(),odfSwitchBean,port_b,cubicleName);
                                break;
                            }
                        }
                    }
                }else if (type == 2){
                    if (!TextUtils.isEmpty(unitName1)){
                        String[] split1 = unitName1.split("\\.");
                        String unitName = split1[0];

                        KM_SPCD_UNIT unit0 = null;
                        if(!TextUtils.isEmpty(port_b)){
                            unit0 = getUnit(km_spcd_cable.getStation_id(), cubicle1, port_b);//本侧装置
                        }else {
                            continue;
                        }
                        KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicle0, port_a);
                        KM_SPCD_CUBICLE cubicle = getCubicle(1, cubicle0);//对侧屏柜
                        KM_SPCD_REGION region = getRegion(cubicle);//对侧小室
                        if (cubicle == null){
                            return;
                        }

                        int isGoOn = 0;
                        OdfSwitchBean odfSwitchBean1 = null;
                        List<OdfSwitchBean> odfAndSwitchList1 = null;
                        if (port_b.equals(unitName1) && !odfCores.contains(km_spcd_cable.getName()+"/"+km_spcd_core.getNo())
                                && !cubicleName.contains(cubicle.getId())){
                            if (unit0.getDev_class().equals("IED") || unit0.getDev_class().equals("ODF")){
                                odfSwitchBean1 = odfSwitchBeanReceive;
//                            setOdfSwitchBeanData(odfSwitchBean1,odfSwitchBeanReceive);
                                odfAndSwitchList1 = odfSwitchBeanReceive.getConnectedBeans();
                                if (odfAndSwitchList1 == null){
                                    odfAndSwitchList1 = new ArrayList<>();
                                    odfSwitchBeanReceive.setConnectedBeans(odfAndSwitchList1);
                                }

                                isGoOn = 1;
                            }
                        }
                        if (unit0.getDev_class().equals("SWITCH") && port_b.startsWith(unitName) && port_b.endsWith(txRx) && !odfCores.contains(km_spcd_cable.getName()+"/"+km_spcd_core.getNo())
                                && !cubicleName.contains(cubicle.getId())){

                            odfSwitchBean1 = new OdfSwitchBean();
                            setOdfSwitchBeanData(odfSwitchBean1,odfSwitchBeanReceive);
                            odfAndSwitch0.add(odfSwitchBean1);
                            odfAndSwitchList1 = new ArrayList<>();
                            odfSwitchBean1.setConnectedBeans(odfAndSwitchList1);

                            isGoOn = 1;
                        }

//                        if (unit0.getDev_class().equals("SWITCH") && port_b.startsWith(unitName) && port_b.endsWith(txRx) && !odfCores.contains(km_spcd_cable.getName()+"/"+km_spcd_core.getNo())
//                                && !cubicleName.contains(cubicle.getId())){
                        if (isGoOn == 1){

                            //记录找过的缆纤芯不要再找了
                            odfCores.add(km_spcd_cable.getName()+"/"+km_spcd_core.getNo());

//                            OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();
//                            setOdfSwitchBeanData(odfSwitchBean1,odfSwitchBeanReceive);
//                            odfAndSwitch0.add(odfSwitchBean1);
//                            List<OdfSwitchBean> odfAndSwitchList1 = new ArrayList<>();
//                            odfSwitchBean1.setOdfAndSwitch(odfAndSwitchList1);

                            List<KM_SPCD_INTCORE> intcores = intcoreDao.getListForEq("cubicle_id", cubicle.getId());
                            if (unit.getDev_class().equals("IED")){//对侧装置，是否要区分交换机、odf、ied
                                odfSwitchBean1.setPortTx(port_b);

                                OdfSwitchBean odfSwitchBean = new OdfSwitchBean();//设置第二个交换机
                                odfAndSwitchList1.add(odfSwitchBean);

                                //记录经过数据，物理全回路图
                                setWholeCircuitBean(odfSwitchBean,cubicle,1,unit,getBoardPort(port_a),getBoard(port_a),getPort(port_a,1),getPort(port_a,2),null);
                                odfSwitchBean.setType(1);
                                odfSwitchBean.setKm_spcd_unit(unit);
                                odfSwitchBean.setPortRx(port_a);
                            }else if (unit.getDev_class().equals("ODF")) {//对侧装置，是否要区分交换机、odf、ied
                                int isGo = -1;
                                cubicleName.add(cubicle.getId());
                                for (KM_SPCD_INTCORE intcore:intcores){
                                    if (intcore.getPort_a().equals(port_a)){
                                        //装odf
                                        OdfSwitchBean odfSwitchBean2 = new OdfSwitchBean();
                                        List<OdfSwitchBean>  OdfSwitchBeanList2 = new ArrayList<>();
                                        odfSwitchBean2.setConnectedBeans(OdfSwitchBeanList2);

                                        odfAndSwitchList1.add(odfSwitchBean2);
                                        setOdfSwitchBeanVlue(odfSwitchBean2,cubicle,unit,port_a,getBoardPort(port_a),getBoard(port_a),cubicle0);
                                        setOdfUnit(port_a,odfSwitchBean2);
                                        //对侧装置
                                        String port_b1 = intcore.getPort_b();
                                        String[] split = port_b1.split("\\.");
                                        //改造3.0
                                        OdfSwitchBean odfSwitchBean = new OdfSwitchBean();//设置第二个交换机
                                        OdfSwitchBeanList2.add(odfSwitchBean);

                                        setOtherUnit(km_spcd_intcore,split[0],intcore,1,cubicle,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBean,odfAndSwitchList1);

                                        isGo = 1;//找到之后结束外面的循环标志
                                    }else if (intcore.getPort_b().equals(port_a)){
                                        //装odf
                                        OdfSwitchBean odfSwitchBean2 = new OdfSwitchBean();
                                        List<OdfSwitchBean>  OdfSwitchBeanList2 = new ArrayList<>();
                                        odfSwitchBean2.setConnectedBeans(OdfSwitchBeanList2);

                                        odfAndSwitchList1.add(odfSwitchBean2);
                                        setOdfSwitchBeanVlue(odfSwitchBean2,cubicle,unit,port_a,getBoardPort(port_a),getBoard(port_a),cubicle0);
                                        setOdfUnit(port_a,odfSwitchBean2);
                                        //对侧装置
                                        String port_a1 = intcore.getPort_a();
                                        String[] split = port_a1.split("\\.");
                                        //改造3.0
                                        OdfSwitchBean odfSwitchBean = new OdfSwitchBean();//设置第二个交换机
                                        OdfSwitchBeanList2.add(odfSwitchBean);
                                        setOdfSwitchBeanVlue(odfSwitchBean,cubicle,unit,port_a,getBoardPort(port_a),getBoard(port_a),cubicle0);
                                        setOdfUnit(port_a,odfSwitchBean);

                                        setOtherUnit(km_spcd_intcore,split[0],intcore,2,cubicle,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBean,odfAndSwitchList1);

                                        isGo = 1;//找到之后结束外面的循环标志
                                    }
                                }
                                if (isGo!=1){
                                    //找交换机数据---设置新版数据值
                                    OdfSwitchBean odfSwitchBean2 = new OdfSwitchBean();
                                    odfAndSwitchList1.add(odfSwitchBean2);
                                    setOdfSwitchBeanVlue(odfSwitchBean2,cubicle,unit,port_a,getBoardPort(port_a),getBoard(port_a),cubicle0);
                                    setOdfUnit(port_a,odfSwitchBean2);

                                    setWLconnectFinal(region,cubicle,km_spcd_intcore,port_a,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfAndSwitchList1,odfSwitchBean2,port_b,cubicleName);
                                }
                            }else if (unit.getDev_class().equals("SWITCH")){
                                for (KM_SPCD_INTCORE intcore:intcores){
                                    if (intcore.getPort_a().startsWith(unit.getName()) && intcore.getPort_a().endsWith(txRx)){
                                        String port_b1 = intcore.getPort_b();
                                        String[] split = port_b1.split("\\.");
                                        //改造3.0
                                        OdfSwitchBean odfSwitchBean = setSwithUnit(odfAndSwitchList1,port_b,port_a,2,unit, km_spcd_core,km_spcd_cable, cubicle, intcore.getPort_a(),odfSwitchBean1, 0);

                                        setOtherUnit(km_spcd_intcore,split[0],intcore,1,cubicle,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBean,odfAndSwitchList1);
                                    }else if (intcore.getPort_b().startsWith(unit.getName())  && intcore.getPort_b().endsWith(txRx)){
                                        String port_a1 = intcore.getPort_a();
                                        String[] split = port_a1.split("\\.");
                                        //改造3.0
                                        OdfSwitchBean odfSwitchBean = setSwithUnit(odfAndSwitchList1,port_b,port_a,2,unit, km_spcd_core,km_spcd_cable, cubicle, intcore.getPort_b(),odfSwitchBean1, 0);

                                        setOtherUnit(km_spcd_intcore,split[0],intcore,2,cubicle,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBean,odfAndSwitchList1);
                                    }
                                }
                                //再接着查找缆,找过的缆不要再找了，避免死循环
                                cubicleName.add(cubicle.getId());

                                OdfSwitchBean odfSwitchBean = setSwithUnit(odfAndSwitchList1,port_b,port_a,2,unit, km_spcd_core,km_spcd_cable, cubicle, port_a,odfSwitchBean1,1);
//                                setodfSwitchBean1Data(odfSwitchBean,null,port_a,cubicle, unit);
                                setWLconnectFinal(region,cubicle,km_spcd_intcore,port_a,secondWholeCubicle,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBean1.getConnectedBeans(),odfSwitchBean,port_a,cubicleName);
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    public static void setOdfUnit(String port_a,OdfSwitchBean odfSwitchBeanReceive){
        //第一个交换机内容
        WholeCircuitBean.UnitWholeBean unitWholeBean2 = odfSwitchBeanReceive.getUnitWholeBean();
        if (unitWholeBean2 == null){
            unitWholeBean2 = new WholeCircuitBean.UnitWholeBean();
            odfSwitchBeanReceive.setUnitWholeBean(unitWholeBean2);
        }
        if (!TextUtils.isEmpty(port_a)){
            String[] split = port_a.split("\\.");
            unitWholeBean2.setBoard(split[1]);

            Log.e("打印端口数据1",port_a);

            String noDes = split[2];
            String[] noDesSp = null;
            if (!TextUtils.isEmpty(noDes) && noDes.contains("-")){
                noDesSp = noDes.split("-");
                unitWholeBean2.setPortADirection(noDesSp[1]);
                unitWholeBean2.setPort(noDesSp[0]);
                Log.e("打印端口数据",noDesSp[0]);
            }
        }
    }


    public static OdfSwitchBean setSwithUnit(List<OdfSwitchBean> odfAndSwitch, String port_a, String port_b, Integer type, KM_SPCD_UNIT unit, KM_SPCD_CORE km_spcd_core, KM_SPCD_CABLE km_spcd_cable, KM_SPCD_CUBICLE cubicle,
                                             String portA, OdfSwitchBean odfSwitchBeanReceive, int i){
        //第一个交换机内容
        WholeCircuitBean.UnitWholeBean unitWholeBean2 = odfSwitchBeanReceive.getUnitWholeBean();
        if (unitWholeBean2 == null){
            unitWholeBean2 = new WholeCircuitBean.UnitWholeBean();
        }
        if (!TextUtils.isEmpty(port_a)){
            odfSwitchBeanReceive.setPortTx(port_a);
            String[] split = port_a.split("\\.");
            unitWholeBean2.setBoard1(split[1]);

            Log.e("打印端口数据1",port_a);

            String noDes = split[2];
            String[] noDesSp = null;
            if (!TextUtils.isEmpty(noDes) && noDes.contains("-")){
                noDesSp = noDes.split("-");
                unitWholeBean2.setPortA1Direction(noDesSp[1]);
                unitWholeBean2.setPort1(noDesSp[0]);
                Log.e("打印端口数据",noDesSp[0]);
            }
        }

        //改造3.0---设置第二个交换机
        OdfSwitchBean odfSwitchBean0 = new OdfSwitchBean();
        odfAndSwitch.add(odfSwitchBean0);
        List<OdfSwitchBean> odfSwitchBeans = new ArrayList<>();

        odfSwitchBean0.setPortRx(port_b);
        odfSwitchBean0.setType(type);
        odfSwitchBean0.setKm_spcd_unit(unit);
        odfSwitchBean0.setKm_spcd_core(km_spcd_core);
        odfSwitchBean0.setKm_spcd_cable(km_spcd_cable);
        odfSwitchBean0.setConnectedBeans(odfSwitchBeans);


        //记录经过数据，物理全回路图
        setWholeCircuitBean(odfSwitchBean0,cubicle,3,unit,null,null,null,null,null);
        WholeCircuitBean.UnitWholeBean unitWholeBean = odfSwitchBean0.getUnitWholeBean();
        unitWholeBean.setKm_spcd_coreA(km_spcd_core);
        if (!TextUtils.isEmpty(port_b)){
            String[] split = port_b.split("\\.");
            unitWholeBean.setBoard(split[1]);

            String noDes = split[2];
            String[] noDesSp = null;
            if (!TextUtils.isEmpty(noDes) && noDes.contains("-")){
                noDesSp = noDes.split("-");
                unitWholeBean.setPortADirection(noDesSp[1]);

                unitWholeBean.setBoardPort(split[1]+"-"+noDesSp[0]);
                unitWholeBean.setPort(noDesSp[0]);
            }
        }

        //记录经过数据，物理全回路图
        setSwitchPort1(unitWholeBean,portA,odfSwitchBean0);

        if (i == 1){
           return odfSwitchBean0;
        }else{
            //改造3.0
            OdfSwitchBean odfSwitchBean = new OdfSwitchBean();//设置第二个交换机连接的装置
            odfSwitchBeans.add(odfSwitchBean);

            List<OdfSwitchBean> OdfSwitchBeanList = new ArrayList<>();
            odfSwitchBean.setConnectedBeans(OdfSwitchBeanList);

            return odfSwitchBean;
        }
    }

    static List<String> cableName = new ArrayList<>();
    private static void analysisCables(List<KM_SPCD_CABLE> cables, String portOdf, KM_SPCD_INTCORE km_spcd_intcore, int type, List<WholeCircuitBean.CubicleWholeBean> list, Integer once, OdfSwitchBean odfSwitchBean) {
        try {
            if (cables!=null && cables.size()!=0){
                List<Integer> ids = new ArrayList<>();
                for (KM_SPCD_CABLE km_spcd_cable:cables){
                    if (once == 1){
//                        if ("GL".equals(km_spcd_cable.getType())){
                            ids.add(km_spcd_cable.getId());
//                        }
                    }else if (once == 2){
                        ids.add(km_spcd_cable.getId());
                    }
                }
                List<KM_SPCD_CORE> km_spcd_cores = coreDao.getListForIn("cable_id", ids);//找到与当前屏柜cubicleA相连的KM_SPCD_CORE集合
                if (km_spcd_cores!=null && km_spcd_cores.size()!=0){
                    for (KM_SPCD_CORE km_spcd_core:km_spcd_cores){
                        Integer cable_id0 = km_spcd_core.getCable_id();
                        KM_SPCD_CABLE km_spcd_cable0 = (KM_SPCD_CABLE)cableDao.getById(cable_id0);
                        //cubicleA是相连的odf
                        String port_b1 = null;
                        String cubicleB = null;
                        KM_SPCD_CABLE cubicleHole= null;
                        KM_SPCD_CORE km_spcd_core1 = null;

                        KM_SPCD_CABLE km_spcd_cable1 = (KM_SPCD_CABLE)cableDao.getById(km_spcd_core.getCable_id());
                        if (type == 1 && km_spcd_core.getPort_a().equals(portOdf) && !cableName.contains(km_spcd_cable0.getName()) && !odfCores.contains(km_spcd_cable1.getName()+"/"+km_spcd_core.getNo())){
                            port_b1 = km_spcd_core.getPort_b();//与之相连的对侧屏柜的odf
                            Integer cable_id = km_spcd_core.getCable_id();
                            KM_SPCD_CABLE km_spcd_cable = (KM_SPCD_CABLE)cableDao.getById(cable_id);
                            cubicleB = km_spcd_cable.getCubicleB();//对侧屏柜信息，如：R66.XLP1A
                            cubicleHole = km_spcd_cable;
                            glNames.add(km_spcd_cable.getName());
                            km_spcd_core1 = km_spcd_core;

                            //记录找过的缆纤芯不要再找了
                            odfCores.add(km_spcd_cable.getName()+"/"+km_spcd_core.getNo());
                        }else if (type == 2 && km_spcd_core.getPort_b().equals(portOdf) && !cableName.contains(km_spcd_cable0.getName()) && !odfCores.contains(km_spcd_cable1.getName()+"/"+km_spcd_core.getNo())){
                            port_b1 = km_spcd_core.getPort_a();//与之相连的对侧屏柜的odf
                            Integer cable_id = km_spcd_core.getCable_id();
                            KM_SPCD_CABLE km_spcd_cable = (KM_SPCD_CABLE)cableDao.getById(cable_id);
                            cubicleB = km_spcd_cable.getCubicleA();//对侧屏柜信息，如：R66.XLP1A
                            cubicleHole = km_spcd_cable;
                            glNames.add(km_spcd_cable.getName());
                            km_spcd_core1 = km_spcd_core;

                            //记录找过的缆纤芯不要再找了
                            odfCores.add(km_spcd_cable.getName()+"/"+km_spcd_core.getNo());
                        }

                        if (!TextUtils.isEmpty(cubicleB) && cubicleB.contains(".")){
//                            cableName.add(cubicleHole.getName());//讲找过的cable添加到集合，下次循环不再重复查找

                            String[] split = cubicleB.split("\\.");
                            String region = split[0];//小室，如：R66
                            String cubicle  = split[1];//屏柜，如：XLP1A
                            KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("substation_id", km_spcd_substation.getId(),"name",region);
                            KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE)cubicleDao.getFirstForEq
                                    ("region_id", km_spcd_region.getId(),"substation_id", km_spcd_substation.getId(), "name", cubicle);
                            if (km_spcd_cubicle!=null){
                                //改造
                                WholeCircuitBean.CubicleWholeBean cubicleWholeBean = new WholeCircuitBean.CubicleWholeBean();
                                List<WholeCircuitBean.UnitWholeBean> unitWholeBeanList = new ArrayList<>();
                                WholeCircuitBean.UnitWholeBean unitWholeBean = new WholeCircuitBean.UnitWholeBean();
                                unitWholeBeanList.add(unitWholeBean);
                                cubicleWholeBean.setUnitWholeBeans(unitWholeBeanList);
                                list.add(cubicleWholeBean);
                                cubicleWholeBean.setKm_spcd_cubicle(km_spcd_cubicle);

                                //查出对侧odf装置
                                String[] split2 = port_b1.split("\\.");
                                KM_SPCD_UNIT odfUnit = (KM_SPCD_UNIT) unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", split2[0]);
                                //设置中间屏柜
                                IEDsWholeCircuitDataPool.CubicleGroupBaseBean cubicleGroupBaseBeanCenter = new IEDsWholeCircuitDataPool.CubicleGroupBaseBean();
                                cubicleGroupBaseBeanCenter.setCubicleName(buildCubiculeNameDesc(km_spcd_cubicle));
                                //改造
                                String noDes = split2[2];
                                String[] noDesSp = null;
                                if (!TextUtils.isEmpty(noDes) && noDes.contains("-")){
                                    noDesSp = noDes.split("-");
                                    unitWholeBean.setPortADirection(noDesSp[1]);
                                }
                                unitWholeBean.setKm_spcd_unit(odfUnit);
                                unitWholeBean.setKm_spcd_coreA(km_spcd_core1);
                                List<KM_SPCD_CORE> cores = coreDao.getListForEq("cable_id", cubicleHole.getId());
                                unitWholeBean.setKm_spcd_coreAName(cubicleHole.getName()+"("+ cores.size()+"-"+km_spcd_core1.getNo() +")");

                                OdfSwitchBean odfSwitchBean2 = null;
                                if (odfUnit!=null){
                                    //设置屏柜的odf名称(unit.name-unit.desc)
                                    StringBuffer stringBuffer = new StringBuffer(" ");
                                    stringBuffer.append(odfUnit.getName())
                                            .append("-")
                                            .append(odfUnit.getDescription());
                                    cubicleGroupBaseBeanCenter.setOdf_name(stringBuffer.toString());
                                    cubicleGroupBaseBeanCenter.setOdf_portToNo(split2[2]);

                                    //改造
                                    KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) boardDao.getFirstForEq("unit_id", odfUnit.getId(), "slot", split2[1]);
                                    if (km_spcd_board!=null){
                                        unitWholeBean.setPortADesc(km_spcd_board.getDescription()+"-"+noDesSp[0]);
                                        unitWholeBean.setPortAdscInt(km_spcd_board.getSlot()+"-"+noDesSp[0]);
                                    }

                                    //改造3.0  设置相连的对侧odf装置
                                    List<OdfSwitchBean> odfAndSwitch2 = new ArrayList<>();
                                    odfSwitchBean2 = new OdfSwitchBean();
                                    odfAndSwitch2.add(odfSwitchBean2);
                                    odfSwitchBean.setConnectedBeans(odfAndSwitch2);
                                    //设置值
                                    odfSwitchBean2.setType(2);
                                    odfSwitchBean2.setKm_spcd_unit(odfUnit);
                                    odfSwitchBean2.setKm_spcd_cable(cubicleHole);
                                    odfSwitchBean2.setKm_spcd_core(km_spcd_core1);
                                    odfSwitchBean2.setPortRx(port_b1);

                                    //记录经过数据，物理全回路图
                                    setWholeCircuitBean(odfSwitchBean2,km_spcd_cubicle,2,odfUnit,null,null,null,null
                                            ,null);
                                    WholeCircuitBean.UnitWholeBean unitWholeBean2 = odfSwitchBean2.getUnitWholeBean();
                                    if (km_spcd_board!=null){
                                        unitWholeBean2.setBoardPort(km_spcd_board.getSlot()+"-"+noDesSp[0]);
                                        unitWholeBean2.setBoard(km_spcd_board.getSlot());
                                        unitWholeBean2.setPort(split2[1]);
                                        unitWholeBean2.setPortADesc(km_spcd_board.getDescription()+"-"+noDesSp[0]);
                                        unitWholeBean2.setPortAdscInt(km_spcd_board.getSlot()+"-"+noDesSp[0]);

                                        //设置值
                                        odfSwitchBean2.setBoardPort(km_spcd_board.getSlot()+"-"+noDesSp[0]);
                                        odfSwitchBean2.setBoard(km_spcd_board.getSlot());
                                        odfSwitchBean2.setPort(split2[1]);
                                        odfSwitchBean2.setPortAdsc(km_spcd_board.getDescription()+"-"+noDesSp[0]);
                                        odfSwitchBean2.setPortAdscInt(km_spcd_board.getSlot()+"-"+noDesSp[0]);
                                    }
                                    if (!TextUtils.isEmpty(noDes) && noDes.contains("-")){
                                        noDesSp = noDes.split("-");
                                        unitWholeBean2.setPortADirection(noDesSp[1]);
                                    }
                                    unitWholeBean2.setKm_spcd_coreA(km_spcd_core1);
                                    unitWholeBean2.setKm_spcd_coreAName(cubicleHole.getName()+"("+ cores.size()+"-"+km_spcd_core1.getNo() +")");
                                }

                                List<KM_SPCD_INTCORE> km_spcd_intcores0 = new ArrayList<>();
                                //查出port_a是odf的
                                List<String> keys1 = new ArrayList<String>(asList("cubicle_id", "port_a"));
                                List<Object> value1 = new ArrayList<Object>(asList(km_spcd_cubicle.getId(),port_b1));
                                List<KM_SPCD_INTCORE> km_spcd_intcores1 = intcoreDao.getListForEq(keys1, value1);
                                //查出port_b是odf的
                                List<String> keys2 = new ArrayList<String>(asList("cubicle_id", "port_b"));
                                List<KM_SPCD_INTCORE> km_spcd_intcores2 = intcoreDao.getListForEq(keys2, value1);
                                if (km_spcd_intcores1!=null && km_spcd_intcores1.size()!=0){
                                    km_spcd_intcores0.addAll(km_spcd_intcores1);
                                }
                                if (km_spcd_intcores2!=null && km_spcd_intcores2.size()!=0){
                                    km_spcd_intcores0.addAll(km_spcd_intcores2);
                                }

                                if (km_spcd_intcores0!=null && km_spcd_intcores0.size()!=0){
                                    KM_SPCD_INTCORE km_spcd_intcore1 = km_spcd_intcores0.get(0);
                                    String port_a2 = km_spcd_intcore1.getPort_a();
                                    String port_b2 = km_spcd_intcore1.getPort_b();
                                    String[] split1 = null;
                                    int thisType = 0;
                                    if (port_a2.equals(port_b1)){
                                        thisType = 1;
                                        split1 = port_b2.split("\\.");
                                    }
                                    if (port_b2.equals(port_b1)){
                                        thisType = 2;
                                        split1 = port_a2.split("\\.");
                                    }
                                    if (split1!=null){
                                        String unitName1 = split1[0];//如：4n
                                        //查出与odfUnit相连的装置是否是odf，是与否都要设置值
                                        KM_SPCD_UNIT odfConnectUnit = (KM_SPCD_UNIT) unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", unitName1);
                                        String dev_class = odfConnectUnit.getDev_class();
                                        if ("ODF".equals(dev_class)){
                                            //设置屏柜的odf名称(unit.name-unit.desc)
                                            StringBuffer stringBuffer = new StringBuffer(" ");
                                            stringBuffer.append(km_spcd_unit.getName())
                                                    .append("-")
                                                    .append(km_spcd_unit.getDescription());
                                            cubicleGroupBaseBeanCenter.setOdf_name(stringBuffer.toString());
                                            cubicleGroupBaseBeanCenter.setLocationOf(PASS);
                                            cubicleGroupBaseBeanCenter.setOdf_portFrNo(split1[2]);
                                            cubicles.add(cubicleGroupBaseBeanCenter);//屏柜添加进集合
                                        }else {
                                            cubicleGroupBaseBeanCenter.setLocationOf(TO);
                                        }
                                        //改造3.0
                                        List<OdfSwitchBean> odfAndSwitch = new ArrayList<>();
                                        OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();
                                        odfAndSwitch.add(odfSwitchBean1);
                                        odfSwitchBean2.setConnectedBeans(odfAndSwitch);
                                        setOtherUnit(km_spcd_intcore,unitName1,km_spcd_intcore1,thisType,km_spcd_cubicle,cubicleGroupBaseBeanCenter,cubicleWholeBean,unitWholeBeanList,list,odfSwitchBean1,odfAndSwitch);
                                    }
                                }else {
                                    //根据odf去查找Cable连接
                                    String cubicleC = km_spcd_region.getName() +"." + km_spcd_cubicle.getName();//如：cubicleA="R66.XLKZ1A"
                                    //查找与portOdf相连的屏柜
                                    List<String> keys = new ArrayList<String>(asList("station_id", "cubicleA"));
                                    List<Object> value = new ArrayList<Object>(asList(km_spcd_substation.getId(),cubicleC));
                                    List<KM_SPCD_CABLE> cablesA = (List<KM_SPCD_CABLE>) cableDao.getListForEq(keys, value);//cubicleA是当前屏柜

                                    List<String> keys0 = new ArrayList<String>(asList("station_id", "cubicleB"));
                                    List<KM_SPCD_CABLE> cablesB = (List<KM_SPCD_CABLE>) cableDao.getListForEq(keys0, value);//cubicleA是当前屏柜

                                    //改造3.0，如果光缆连接的是光缆
                                    analysisCables(cablesA,port_b1,km_spcd_intcore,1,list,2,odfSwitchBean2);
                                    analysisCables(cablesB,port_b1, km_spcd_intcore, 2,list,2,odfSwitchBean2);
                                }
                            }
                            break;
                        }
                    }
                }
            }
        }catch (SQLException e){
            e.printStackTrace();
        }
    }

    private PortConnectBean.IntcoreBean setSwitchIedFinal(KM_SPCD_INTCORE km_spcd_intcore, String unitName, KM_SPCD_INTCORE km_spcd_intcore0, int type){
        PortConnectBean.IntcoreBean intcoreBean = new PortConnectBean.IntcoreBean();

        String port_a1 = km_spcd_intcore.getPort_a();
        String port_b1 = km_spcd_intcore.getPort_b();
        String[] splitA1 = port_a1.split("\\.");
        String[] splitB1 = port_b1.split("\\.");

        if (km_spcd_intcore0 == null){
            if (type == 1){
                intcoreBean.setBoardA(splitA1[1]);
                intcoreBean.setPortA(splitA1[2]);
                intcoreBean.setBoardB(splitB1[1]);
                intcoreBean.setPortB(splitB1[2]);
            }else if (type == 2){
                intcoreBean.setBoardA(splitB1[1]);
                intcoreBean.setPortA(splitB1[2]);
                intcoreBean.setBoardB(splitA1[1]);
                intcoreBean.setPortB(splitA1[2]);
            }
        }else{
            String port_a = km_spcd_intcore0.getPort_a();
            String port_b = km_spcd_intcore0.getPort_b();
            String[] splitA0 = port_a.split("\\.");
            String[] splitB0 = port_b.split("\\.");

            if (type == 1){
                intcoreBean.setBoardA(splitA1[1]);
                intcoreBean.setPortA(splitA1[2]);
                if (port_a.startsWith(unitName)){//port_a是对侧ied----注意：name=TX01未改值
                    intcoreBean.setBoardB(splitA0[1]);
                    intcoreBean.setPortB(splitA0[2]);
                }else if (port_b.startsWith(unitName)){//port_b是对侧ied----注意：name=TX01未改值
                    intcoreBean.setBoardB(splitB0[1]);
                    intcoreBean.setPortB(splitB0[2]);
                }
            }else if (type == 2){
                intcoreBean.setBoardA(splitB1[1]);
                intcoreBean.setPortA(splitB1[2]);
                if (port_a.startsWith(unitName)){//port_a是对侧ied----注意：name=TX01未改值
                    intcoreBean.setBoardB(splitA0[1]);
                    intcoreBean.setPortB(splitA0[2]);
                }else if (port_b.startsWith(unitName)){//port_b是对侧ied----注意：name=TX01未改值
                    intcoreBean.setBoardB(splitB0[1]);
                    intcoreBean.setPortB(splitB0[2]);
                }
            }
        }
        return intcoreBean;
    }


    private KM_SPCD_INTCORE setSwitchIed(KM_SPCD_INTCORE km_spcd_intcore, String unitName, KM_SPCD_INTCORE km_spcd_intcore0, int type){
        if (type == 1){
            String port_a = km_spcd_intcore0.getPort_a();
            String port_b = km_spcd_intcore0.getPort_b();
            if (port_a.startsWith(unitName)){//port_a是对侧ied----注意：name=TX01未改值
                km_spcd_intcore.setPort_b(km_spcd_intcore0.getPort_a());
            }else if (port_b.startsWith(unitName)){//port_b是对侧ied----注意：name=TX01未改值
                km_spcd_intcore.setPort_b(km_spcd_intcore0.getPort_b());
            }
        }else if (type == 2){
            String port_a = km_spcd_intcore0.getPort_a();
            String port_b = km_spcd_intcore0.getPort_b();
            if (port_a.startsWith(unitName)){//port_a是对侧ied----注意：name=TX01未改值
                km_spcd_intcore.setPort_a(km_spcd_intcore0.getPort_a());
            }else if (port_b.startsWith(unitName)){//port_b是对侧ied----注意：name=TX01未改值
                km_spcd_intcore.setPort_a(km_spcd_intcore0.getPort_b());
            }
        }
        return km_spcd_intcore;
    }


    private PortConnectBean.IedConnectBean getIedConnectBean(KM_SPCD_UNIT km_spcd_unit) {
        PortConnectBean.IedConnectBean  iedConnect= null;
        if (iedList!=null && iedList.size()!=0){
            for (PortConnectBean.IedConnectBean iedConnectBean:iedList){
                KM_SPCD_UNIT otherUnit = iedConnectBean.getOtherUnit();
                if (otherUnit.getId().intValue() == km_spcd_unit.getId().intValue()){
                    iedConnect = iedConnectBean;
                    break;
                }
            }
        }
        return iedConnect;
    }

    //查询所有尾缆
    public Map<KM_SPCD_CABLE, List<KM_SPCD_CORE>> getWLCableCore(){
        Map<KM_SPCD_CABLE, List<KM_SPCD_CORE>> cableCore = new HashMap<>();
        //拼装当前装置的尾缆cubicleA="R66.XLKZ1A"
        String cubicleC= km_spcd_region.getName()+"."+km_spcd_cubicle.getName();
        List<String> keys1 = new ArrayList<String>(asList("station_id", "cubicleA","type"));
        List<String> keys2 = new ArrayList<String>(asList("station_id", "cubicleB","type"));
        List<Object> value = new ArrayList<Object>(asList(km_spcd_substation.getId(),cubicleC,"WL"));

        try {
            List<KM_SPCD_CABLE> cables = new ArrayList<>();
            List<KM_SPCD_CABLE> cables1 = (List<KM_SPCD_CABLE>)cableDao.getListForEq(keys1, value);
            List<KM_SPCD_CABLE> cables2 = (List<KM_SPCD_CABLE>)cableDao.getListForEq(keys2, value);
            if (cables1!=null && cables1.size()!=0){
                cables.addAll(cables1);
            }
            if (cables2!=null && cables2.size()!=0){
                cables.addAll(cables2);
            }
            if (cables !=null && cables.size()!=0){
                for (KM_SPCD_CABLE km_spcd_cable:cables){
                    List<KM_SPCD_CORE> cores = coreDao.getListForEq("cable_id", km_spcd_cable.getId());
                    if (cores!=null && cores.size()!=0){
                        cableCore.put(km_spcd_cable,cores);
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return cableCore;
    }


    private void setWLconnect(List<WholeCircuitBean.CubicleWholeBean> list,KM_SPCD_REGION km_spcd_region,KM_SPCD_CUBICLE km_spcd_cubicle,String port0,String beforeCable,OdfSwitchBean odfSwitchBean) {
        //全数据版本
        List<OdfSwitchBean> odfAndSwitchList = null;
        if(odfSwitchBean == null){
            odfSwitchBean = new OdfSwitchBean();
        }
        odfAndSwitchList = odfSwitchBean.getConnectedBeans();
        if (odfAndSwitchList == null){
            odfAndSwitchList = new ArrayList<>();
            odfSwitchBean.setConnectedBeans(odfAndSwitchList);
        }

        KM_SPCD_UNIT km_spcd_unit = null;
        KM_SPCD_BOARD board = null;
        KM_SPCD_PORT port = null;
        if (!TextUtils.isEmpty(port0)){
            String[] split = port0.split("\\.");
            try {
                km_spcd_unit = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", split[0]);
                board= (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unit.getId(), "slot", split[1]);

                String s1 = split[2];
                if (!TextUtils.isEmpty(s1) && s1.contains("-")){
                    String[] split5 = s1.split("-");
                    if (board!=null){
                        port = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", board.getId(), "no", split5[0],"direction",split5[1]);
                    }
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }


        //拼装当前装置的尾缆cubicleA="R66.XLKZ1A"
        String cubicleC= km_spcd_region.getName()+"."+km_spcd_cubicle.getName();
//        List<String> keys1 = new ArrayList<String>(asList("station_id", "cubicleA","type"));
//        List<String> keys2 = new ArrayList<String>(asList("station_id", "cubicleB","type"));
//        List<Object> value = new ArrayList<Object>(asList(km_spcd_substation.getId(),cubicleC,"WL"));

        List<String> keys1 = new ArrayList<String>(asList("station_id", "cubicleA"));
        List<String> keys2 = new ArrayList<String>(asList("station_id", "cubicleB"));
        List<Object> value = new ArrayList<Object>(asList(km_spcd_substation.getId(),cubicleC));

        try {
            List<KM_SPCD_CABLE> cables = new ArrayList<>();
            List<KM_SPCD_CABLE> cables1 = (List<KM_SPCD_CABLE>)cableDao.getListForEq(keys1, value);
            List<KM_SPCD_CABLE> cables2 = (List<KM_SPCD_CABLE>)cableDao.getListForEq(keys2, value);
            if (cables1!=null && cables1.size()!=0){
                cables.addAll(cables1);
            }
            if (cables2!=null && cables2.size()!=0){
                cables.addAll(cables2);
            }
            if (cables.size()!=0){
                int isGo = -1;
                for (KM_SPCD_CABLE km_spcd_cable:cables){
                    if (isGo == 1){
                        break;
                    }
                    String cubicleA = km_spcd_cable.getCubicleA();
                    String cubicleB = km_spcd_cable.getCubicleB();
                    List<KM_SPCD_CORE> cores = coreDao.getListForEq("cable_id", km_spcd_cable.getId());

                    WholeCircuitBean.CubicleWholeBean cubicleWholeBean = new WholeCircuitBean.CubicleWholeBean();
                    List<WholeCircuitBean.UnitWholeBean> unitWholeBeanList = new ArrayList<>();
                    WholeCircuitBean.UnitWholeBean unitWholeBean = new WholeCircuitBean.UnitWholeBean();
                    cubicleWholeBean.setUnitWholeBeans(unitWholeBeanList);

                    if (cubicleA.equals(cubicleC) && !cubicleB.equals(beforeCable)){
//                        coresWl(km_spcd_cable,cubicleB,1);
                        if (cores!=null && cores.size()!=0){
                            for (KM_SPCD_CORE km_spcd_core:cores){
                                String port_a = km_spcd_core.getPort_a();
                                String port_b = km_spcd_core.getPort_b();
                                if (port0.equals(port_a)){//尾缆找到对侧屏柜连接，停止循环
                                    String[] split2 = port_b.split("\\.");
                                    //找对侧屏柜信息
                                    KM_SPCD_CUBICLE cubicle2 = getcubicle(1, cubicleB);
                                    if (cubicle2 == null){
                                        break;
                                    }
                                    //找对侧装置
                                    KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicleB, port_b);
//                                    //改造
//                                    cubicleWholeBean.setKm_spcd_cubicle(cubicle2);
//                                    unitWholeBean.setType(1);
//                                    unitWholeBean.setKm_spcd_unit(unit);
//                                    unitWholeBean.setBoardPort(getBoardPort(port_b));
//                                    unitWholeBean.setPortA(getNoDes(port_b,2));
////                                    setBoardPort(cubicle2,port_b,unitWholeBean);
//                                    unitWholeBean.setKm_spcd_coreA(km_spcd_core);
//                                    unitWholeBean.setPortAdsc(getPortADes(cubicleB,port_b));
//                                    list.add(cubicleWholeBean);
//                                    //设置对侧装置
//                                    setOtherCubicle(unit,split2,cubicle2);
//                                    isGo = 1;//找到之后结束外面的循环标志

                                    //改造--如果连续是尾缆

                                    //找交换机数据---设置新版数据值 coer--porta
                                    setOdfSwitchBeanVlue(odfSwitchBean,km_spcd_cubicle,km_spcd_unit,port_a,board.getSlot()+"-"+port.getNo(),board.getSlot(),cubicleA);

                                    if (unit.getDev_class().equals("ODF")){
                                        //先去找intcore，找不到就接着找光缆尾缆
                                        KM_SPCD_CUBICLE cubicle1 = getcubicle(1, cubicleB);
                                        List<KM_SPCD_INTCORE> intcores1 = (List<KM_SPCD_INTCORE>) intcoreDao.getListForEq("cubicle_id", cubicle1.getId());
                                        if (intcores1!=null && intcores1.size()!=0){
                                            for (KM_SPCD_INTCORE km_spcd_intcore:intcores1){
                                                String port_a1 = km_spcd_intcore.getPort_a();
                                                String port_b1 = km_spcd_intcore.getPort_b();
                                                if (port_a1.equals(port_b)){
                                                    //odf装置的经过路径也得记录
                                                    WholeCircuitBean.UnitWholeBean unitWholeBean1 = new WholeCircuitBean.UnitWholeBean();
                                                    unitWholeBeanList.add(unitWholeBean1);

                                                    unitWholeBean1.setKm_spcd_unit(unit);
                                                    unitWholeBean1.setBoardPort(getBoardPort(port_b));
                                                    Utils.setBoardPort1(unitWholeBean1,port_b);
                                                    unitWholeBean1.setPortADirection(getNoDes(port_b,2));
                                                    unitWholeBean1.setKm_spcd_coreA(km_spcd_core);
                                                    unitWholeBean1.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                                    unitWholeBean1.setPortADesc(getPortADes(cubicleB,port_b,1));
                                                    unitWholeBean1.setPortAdscInt(getPortADes(cubicleB,port_b,2));

                                                    //找交换机数据---设置新版数据值coer--portb
                                                    OdfSwitchBean odfSwitchBean2 = new OdfSwitchBean();
                                                    List<OdfSwitchBean> OdfSwitchBeanList2 = new ArrayList<>();
                                                    odfSwitchBean2.setConnectedBeans(OdfSwitchBeanList2);
                                                    odfAndSwitchList.add(odfSwitchBean2);
                                                    setOdfSwitchBeanVlue(odfSwitchBean2,cubicle1,unit,port_b,getBoardPort(port_b),getBoard(port_b),cubicleB);

                                                    KM_SPCD_UNIT unit1 = getUnit(1, cubicleB, port_b1);
                                                    String[] split = port_b1.split("\\.");
                                                    setOtherCubicle(unit1,split,cubicle1);
                                                    isGo = 1;//找到之后结束外面的循环标志

                                                    //改造
                                                    cubicleWholeBean.setKm_spcd_cubicle(cubicle1);
                                                    unitWholeBean.setKm_spcd_unit(unit1);
                                                    unitWholeBean.setBoardPort(getBoardPort(port_b1));
                                                    Utils.setBoardPort1(unitWholeBean,port_b1);
                                                    unitWholeBean.setPortADirection(getNoDes(port_b1,2));
                                                    unitWholeBean.setKm_spcd_coreA(km_spcd_core);
                                                    unitWholeBean.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                                    unitWholeBean.setPortADesc(getPortADes(cubicleB,port_b1,1));
                                                    unitWholeBean.setPortAdscInt(getPortADes(cubicleB,port_b1,2));
                                                    unitWholeBeanList.add(unitWholeBean);
                                                    list.add(cubicleWholeBean);

                                                    //找交换机数据---设置新版数据值
                                                    OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();
                                                    OdfSwitchBeanList2.add(odfSwitchBean1);

                                                    setOdfSwitchBeanVlue(odfSwitchBean1,cubicle1,unit1,port_b1,getBoardPort(port_b1),getBoard(port_b1),cubicleB);
                                                }else if (port_b1.equals(port_b)){
                                                    //odf装置的经过路径也得记录
                                                    WholeCircuitBean.UnitWholeBean unitWholeBean1 = new WholeCircuitBean.UnitWholeBean();
                                                    unitWholeBeanList.add(unitWholeBean1);

                                                    unitWholeBean1.setKm_spcd_unit(unit);
                                                    unitWholeBean1.setBoardPort(getBoardPort(port_b));
                                                    Utils.setBoardPort1(unitWholeBean1,port_b);
                                                    unitWholeBean1.setPortADirection(getNoDes(port_b,2));
                                                    unitWholeBean1.setKm_spcd_coreA(km_spcd_core);
                                                    unitWholeBean1.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                                    unitWholeBean1.setPortADesc(getPortADes(cubicleB,port_b,1));
                                                    unitWholeBean1.setPortAdscInt(getPortADes(cubicleB,port_b,2));

                                                    //找交换机数据---设置新版数据值
                                                    OdfSwitchBean odfSwitchBean2 = new OdfSwitchBean();
                                                    List<OdfSwitchBean> OdfSwitchBeanList2 = new ArrayList<>();
                                                    odfSwitchBean2.setConnectedBeans(OdfSwitchBeanList2);
                                                    odfAndSwitchList.add(odfSwitchBean2);
                                                    setOdfSwitchBeanVlue(odfSwitchBean2,cubicle1,unit,port_b,getBoardPort(port_b),getBoard(port_b),cubicleB);


                                                    KM_SPCD_UNIT unit1 = getUnit(1, cubicleB, port_a1);
                                                    unitWholeBean.setKm_spcd_unit(unit1);
                                                    String[] split = port_a1.split("\\.");
                                                    setOtherCubicle(unit1,split,cubicle1);
                                                    isGo = 1;//找到之后结束外面的循环标志

                                                    //改造
                                                    cubicleWholeBean.setKm_spcd_cubicle(cubicle1);
                                                    unitWholeBean.setKm_spcd_unit(unit1);
                                                    unitWholeBean.setBoardPort(getBoardPort(port_a1));
                                                    Utils.setBoardPort1(unitWholeBean,port_a1);
                                                    unitWholeBean.setPortADirection(getNoDes(port_a1,2));
                                                    unitWholeBean.setKm_spcd_coreA(km_spcd_core);
                                                    unitWholeBean.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                                    unitWholeBean.setPortADesc(getPortADes(cubicleB,port_a1,1));
                                                    unitWholeBean.setPortAdscInt(getPortADes(cubicleB,port_a1,2));
                                                    unitWholeBeanList.add(unitWholeBean);
                                                    list.add(cubicleWholeBean);

                                                    //找交换机数据---设置新版数据值
                                                    OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();
                                                    OdfSwitchBeanList2.add(odfSwitchBean1);

                                                    setOdfSwitchBeanVlue(odfSwitchBean1,cubicle1,unit1,port_a1,getBoardPort(port_a1),getBoard(port_a1),cubicleB);
                                                }
                                            }
                                        }
                                        if (isGo!=1){
                                            //也就是说尾缆直接又连了光缆尾缆，这个时候需要记录中间经过的屏柜
                                            cubicleWholeBean.setKm_spcd_cubicle(cubicle2);
                                            unitWholeBean.setKm_spcd_unit(unit);
                                            unitWholeBean.setBoardPort(getBoardPort(port_b));
                                            Utils.setBoardPort1(unitWholeBean,port_b);
                                            unitWholeBean.setPortADirection(getNoDes(port_b,2));
                                            unitWholeBean.setKm_spcd_coreA(km_spcd_core);
                                            unitWholeBean.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                            unitWholeBean.setPortADesc(getPortADes(cubicleB,port_b,1));
                                            unitWholeBean.setPortAdscInt(getPortADes(cubicleB,port_b,2));
                                            unitWholeBeanList.add(unitWholeBean);
                                            list.add(cubicleWholeBean);

                                            //找交换机数据---设置新版数据值
                                            OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();
                                            odfAndSwitchList.add(odfSwitchBean1);
                                            setOdfSwitchBeanVlue(odfSwitchBean1,cubicle2,unit,port_b,getBoardPort(port_b),getBoard(port_b),cubicleB);

                                            KM_SPCD_REGION region = getRegion(1, cubicleB);
                                            setWLconnect(list,region,cubicle2,port_b,cubicleC,odfSwitchBean1);
                                        }
                                    }else {
                                        //改造
                                        cubicleWholeBean.setKm_spcd_cubicle(cubicle2);
                                        unitWholeBean.setKm_spcd_unit(unit);
                                        unitWholeBean.setBoardPort(getBoardPort(port_b));
                                        Utils.setBoardPort1(unitWholeBean,port_b);
                                        unitWholeBean.setPortADirection(getNoDes(port_b,2));
                                        unitWholeBean.setKm_spcd_coreA(km_spcd_core);
                                        unitWholeBean.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                        unitWholeBean.setPortADesc(getPortADes(cubicleB,port_b,1));
                                        unitWholeBean.setPortAdscInt(getPortADes(cubicleB,port_b,2));
                                        unitWholeBeanList.add(unitWholeBean);
                                        list.add(cubicleWholeBean);

                                        //找交换机数据---设置新版数据值
                                        OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();
                                        odfAndSwitchList.add(odfSwitchBean1);

                                        setOdfSwitchBeanVlue(odfSwitchBean1,cubicle2,unit,port_b,getBoardPort(port_b),getBoard(port_b),cubicleB);

                                        //设置对侧装置
                                        setOtherCubicle(unit,split2,cubicle2);
                                        isGo = 1;//找到之后结束外面的循环标志
                                    }

                                    break;
                                }
                            }
                        }
                    }else if (cubicleB.equals(cubicleC) && !cubicleA.equals(beforeCable)){
//                        coresWl(km_spcd_cable,cubicleA,2);
                        if (cores!=null && cores.size()!=0){
                            for (KM_SPCD_CORE km_spcd_core:cores){
                                String port_a = km_spcd_core.getPort_a();
                                String port_b = km_spcd_core.getPort_b();
                                if (port0.equals(port_b)){//尾缆找到对侧屏柜连接，停止循环
                                    String[] split2 = port_a.split("\\.");
                                    //找对侧屏柜信息
                                    KM_SPCD_CUBICLE cubicle2 = getcubicle(1, cubicleA);
                                    if (cubicle2 == null){
                                        break;
                                    }
                                    //找对侧装置
                                    KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicleA, port_a);
//                                    //改造
//                                    cubicleWholeBean.setKm_spcd_cubicle(cubicle2);
//                                    unitWholeBean.setType(1);
//                                    unitWholeBean.setKm_spcd_unit(unit);
//                                    unitWholeBean.setBoardPort(getBoardPort(port_a));
//                                    unitWholeBean.setPortA(getNoDes(port_a,2));
//                                    unitWholeBean.setKm_spcd_coreA(km_spcd_core);
//                                    unitWholeBean.setPortAdsc(getPortADes(cubicleA,port_a));
//                                    list.add(cubicleWholeBean);
//                                    //设置对侧装置
//                                    setOtherCubicle(unit,split2,cubicle2);
//                                    isGo = 1;//找到之后结束外面的循环标志

                                    //找交换机数据---设置新版数据值 coer--porta
                                    setOdfSwitchBeanVlue(odfSwitchBean,km_spcd_cubicle,km_spcd_unit,port_b,board.getSlot()+"-"+port.getNo(),board.getSlot(),cubicleB);

                                    //改造--如果连续是尾缆
                                    if (unit.getDev_class().equals("ODF")){
                                        //先去找intcore，找不到就接着找光缆尾缆
                                        KM_SPCD_CUBICLE cubicle1 = getcubicle(1, cubicleA);
                                        List<KM_SPCD_INTCORE> intcores1 = (List<KM_SPCD_INTCORE>) intcoreDao.getListForEq("cubicle_id", cubicle1.getId());
                                        if (intcores1!=null && intcores1.size()!=0){
                                            for (KM_SPCD_INTCORE km_spcd_intcore:intcores1){
                                                String port_a1 = km_spcd_intcore.getPort_a();
                                                String port_b1 = km_spcd_intcore.getPort_b();
                                                if (port_a1.equals(port_a)){
                                                    //odf装置的经过路径也得记录
                                                    WholeCircuitBean.UnitWholeBean unitWholeBean1 = new WholeCircuitBean.UnitWholeBean();
                                                    unitWholeBeanList.add(unitWholeBean1);

                                                    unitWholeBean1.setKm_spcd_unit(unit);
                                                    unitWholeBean1.setBoardPort(getBoardPort(port_a));
                                                    Utils.setBoardPort1(unitWholeBean1,port_a);
                                                    unitWholeBean1.setPortADirection(getNoDes(port_a,2));
                                                    unitWholeBean1.setKm_spcd_coreA(km_spcd_core);
                                                    unitWholeBean1.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                                    unitWholeBean1.setPortADesc(getPortADes(cubicleA,port_a,1));
                                                    unitWholeBean1.setPortAdscInt(getPortADes(cubicleA,port_a,2));


                                                    //找交换机数据---设置新版数据值coer--portb
                                                    OdfSwitchBean odfSwitchBean2 = new OdfSwitchBean();
                                                    List<OdfSwitchBean> OdfSwitchBeanList2 = new ArrayList<>();
                                                    odfSwitchBean2.setConnectedBeans(OdfSwitchBeanList2);
                                                    odfAndSwitchList.add(odfSwitchBean2);
                                                    setOdfSwitchBeanVlue(odfSwitchBean2,cubicle1,unit,port_a,getBoardPort(port_a),getBoard(port_a),cubicleA);


                                                    KM_SPCD_UNIT unit1 = getUnit(1, cubicleA, port_b1);
                                                    unitWholeBean.setKm_spcd_unit(unit1);
                                                    String[] split = port_b1.split("\\.");
                                                    setOtherCubicle(unit1,split,cubicle1);
                                                    isGo = 1;//找到之后结束外面的循环标志

                                                    //改造
                                                    cubicleWholeBean.setKm_spcd_cubicle(cubicle1);
                                                    unitWholeBean.setKm_spcd_unit(unit1);
                                                    unitWholeBean.setBoardPort(getBoardPort(port_b1));
                                                    Utils.setBoardPort1(unitWholeBean,port_b1);
                                                    unitWholeBean.setPortADirection(getNoDes(port_b1,2));
                                                    unitWholeBean.setKm_spcd_coreA(km_spcd_core);
                                                    unitWholeBean.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                                    unitWholeBean.setPortADesc(getPortADes(cubicleA,port_b1,1));
                                                    unitWholeBean.setPortAdscInt(getPortADes(cubicleA,port_b1,2));
                                                    unitWholeBeanList.add(unitWholeBean);
                                                    list.add(cubicleWholeBean);

                                                    //找交换机数据---设置新版数据值
                                                    OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();
                                                    OdfSwitchBeanList2.add(odfSwitchBean1);

                                                    setOdfSwitchBeanVlue(odfSwitchBean1,cubicle1,unit1,port_b1,getBoardPort(port_b1),getBoard(port_b1),cubicleA);
                                                }else if (port_b1.equals(port_a)){
                                                    //odf装置的经过路径也得记录
                                                    WholeCircuitBean.UnitWholeBean unitWholeBean1 = new WholeCircuitBean.UnitWholeBean();
                                                    unitWholeBeanList.add(unitWholeBean1);

                                                    unitWholeBean1.setKm_spcd_unit(unit);
                                                    unitWholeBean1.setBoardPort(getBoardPort(port_a));
                                                    Utils.setBoardPort1(unitWholeBean1,port_a);
                                                    unitWholeBean1.setPortADirection(getNoDes(port_a,2));
                                                    unitWholeBean1.setKm_spcd_coreA(km_spcd_core);
                                                    unitWholeBean1.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                                    unitWholeBean1.setPortADesc(getPortADes(cubicleA,port_a,1));
                                                    unitWholeBean1.setPortAdscInt(getPortADes(cubicleA,port_a,2));

                                                    //找交换机数据---设置新版数据值coer--portb
                                                    OdfSwitchBean odfSwitchBean2 = new OdfSwitchBean();
                                                    List<OdfSwitchBean> OdfSwitchBeanList2 = new ArrayList<>();
                                                    odfSwitchBean2.setConnectedBeans(OdfSwitchBeanList2);
                                                    odfAndSwitchList.add(odfSwitchBean2);
                                                    setOdfSwitchBeanVlue(odfSwitchBean2,cubicle1,unit,port_a,getBoardPort(port_a),getBoard(port_a),cubicleA);

                                                    KM_SPCD_UNIT unit1 = getUnit(1, cubicleA, port_a1);
                                                    unitWholeBean.setKm_spcd_unit(unit1);
                                                    String[] split = port_a1.split("\\.");
                                                    setOtherCubicle(unit1,split,cubicle1);
                                                    isGo = 1;//找到之后结束外面的循环标志

                                                    //改造
                                                    cubicleWholeBean.setKm_spcd_cubicle(cubicle1);
                                                    unitWholeBean.setKm_spcd_unit(unit1);
                                                    unitWholeBean.setBoardPort(getBoardPort(port_a1));
                                                    Utils.setBoardPort1(unitWholeBean,port_a1);
                                                    unitWholeBean.setPortADirection(getNoDes(port_a1,2));
                                                    unitWholeBean.setKm_spcd_coreA(km_spcd_core);
                                                    unitWholeBean.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                                    unitWholeBean.setPortADesc(getPortADes(cubicleA,port_a1,1));
                                                    unitWholeBean.setPortAdscInt(getPortADes(cubicleA,port_a1,2));
                                                    unitWholeBeanList.add(unitWholeBean);
                                                    list.add(cubicleWholeBean);

                                                    //找交换机数据---设置新版数据值
                                                    OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();
                                                    OdfSwitchBeanList2.add(odfSwitchBean1);

                                                    setOdfSwitchBeanVlue(odfSwitchBean1,cubicle1,unit1,port_a1,getBoardPort(port_a1),getBoard(port_a1),cubicleA);
                                                }
                                            }
                                        }
                                        if (isGo!=1){
                                            //也就是说尾缆直接又连了光缆尾缆，这个时候需要记录中间经过的屏柜
                                            cubicleWholeBean.setKm_spcd_cubicle(cubicle2);
                                            unitWholeBean.setKm_spcd_unit(unit);
                                            unitWholeBean.setBoardPort(getBoardPort(port_a));
                                            Utils.setBoardPort1(unitWholeBean,port_a);
                                            unitWholeBean.setPortADirection(getNoDes(port_a,2));
                                            unitWholeBean.setKm_spcd_coreA(km_spcd_core);
                                            unitWholeBean.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                            unitWholeBean.setPortADesc(getPortADes(cubicleA,port_a,1));
                                            unitWholeBean.setPortAdscInt(getPortADes(cubicleA,port_a,2));
                                            unitWholeBeanList.add(unitWholeBean);
                                            list.add(cubicleWholeBean);

                                            //找交换机数据---设置新版数据值
                                            OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();
                                            odfAndSwitchList.add(odfSwitchBean1);
                                            setOdfSwitchBeanVlue(odfSwitchBean1,cubicle2,unit,port_a,getBoardPort(port_a),getBoard(port_a),cubicleA);

                                            KM_SPCD_REGION region = getRegion(1, cubicleA);
                                            setWLconnect(list,region,cubicle2,port_a,cubicleC,odfSwitchBean1);
                                        }
                                    }else {
                                        //改造
                                        cubicleWholeBean.setKm_spcd_cubicle(cubicle2);
                                        unitWholeBean.setKm_spcd_unit(unit);
                                        unitWholeBean.setBoardPort(getBoardPort(port_a));
                                        Utils.setBoardPort1(unitWholeBean,port_a);
                                        unitWholeBean.setPortADirection(getNoDes(port_a,2));
                                        unitWholeBean.setKm_spcd_coreA(km_spcd_core);
                                        unitWholeBean.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                        unitWholeBean.setPortADesc(getPortADes(cubicleA,port_a,1));
                                        unitWholeBean.setPortAdscInt(getPortADes(cubicleA,port_a,2));
                                        unitWholeBeanList.add(unitWholeBean);
                                        list.add(cubicleWholeBean);

                                        //找交换机数据---设置新版数据值
                                        OdfSwitchBean odfSwitchBean1 = new OdfSwitchBean();
                                        odfAndSwitchList.add(odfSwitchBean1);
                                        setOdfSwitchBeanVlue(odfSwitchBean1,cubicle2,unit,port_a,getBoardPort(port_a),getBoard(port_a),cubicleA);

                                        KM_SPCD_REGION region = getRegion(1, cubicleA);

                                        //设置对侧装置
                                        setOtherCubicle(unit,split2,cubicle2);
                                        isGo = 1;//找到之后结束外面的循环标志
                                    }

                                    break;
                                }
                            }
                        }
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private static void setOdfSwitchBeanVlue(OdfSwitchBean odfSwitchBean1, KM_SPCD_CUBICLE cubicle2, KM_SPCD_UNIT unit, String port_a, String boardPort, String boardStr, String cubicleA){
        odfSwitchBean1.setType(1);
        odfSwitchBean1.setKm_spcd_cubicle(cubicle2);
        odfSwitchBean1.setKm_spcd_unit(unit);
        odfSwitchBean1.setBoardPort(boardPort);
        odfSwitchBean1.setBoard(boardStr);
        odfSwitchBean1.setPort(port_a);
        odfSwitchBean1.setPortRx(port_a);
        odfSwitchBean1.setPortAdsc(getPortADes(cubicleA,port_a,1));
        odfSwitchBean1.setPortAdscInt(getPortADes(cubicleA,port_a,2));
    }

    private void setWLconnect(List<WholeCircuitBean.CubicleWholeBean> list) {
        //拼装当前装置的尾缆cubicleA="R66.XLKZ1A"
        String cubicleC= km_spcd_region.getName()+"."+km_spcd_cubicle.getName();
        List<String> keys1 = new ArrayList<String>(asList("station_id", "cubicleA","type"));
        List<String> keys2 = new ArrayList<String>(asList("station_id", "cubicleB","type"));
        List<Object> value = new ArrayList<Object>(asList(km_spcd_substation.getId(),cubicleC,"WL"));
        try {
            List<KM_SPCD_CABLE> cables = new ArrayList<>();
            List<KM_SPCD_CABLE> cables1 = (List<KM_SPCD_CABLE>)cableDao.getListForEq(keys1, value);
            List<KM_SPCD_CABLE> cables2 = (List<KM_SPCD_CABLE>)cableDao.getListForEq(keys2, value);
            if (cables1!=null && cables1.size()!=0){
                cables.addAll(cables1);
            }
            if (cables2!=null && cables2.size()!=0){
                cables.addAll(cables2);
            }
            if (cables.size()!=0){
                int isGo = -1;
                for (KM_SPCD_CABLE km_spcd_cable:cables){
                    if (isGo == 1){
                        break;
                    }
                    String cubicleA = km_spcd_cable.getCubicleA();
                    String cubicleB = km_spcd_cable.getCubicleB();
                    List<KM_SPCD_CORE> cores = coreDao.getListForEq("cable_id", km_spcd_cable.getId());

                    WholeCircuitBean.CubicleWholeBean cubicleWholeBean = new WholeCircuitBean.CubicleWholeBean();
                    List<WholeCircuitBean.UnitWholeBean> unitWholeBeanList = new ArrayList<>();
                    WholeCircuitBean.UnitWholeBean unitWholeBean = new WholeCircuitBean.UnitWholeBean();
                    unitWholeBeanList.add(unitWholeBean);
                    cubicleWholeBean.setUnitWholeBeans(unitWholeBeanList);

                    if (cubicleA.equals(cubicleC)){
//                        coresWl(km_spcd_cable,cubicleB,1);
                        if (cores!=null && cores.size()!=0){
                            for (KM_SPCD_CORE km_spcd_core:cores){
                                String port_a = km_spcd_core.getPort_a();
                                String port_b = km_spcd_core.getPort_b();
                                if (port0.equals(port_a)){//尾缆找到对侧屏柜连接，停止循环
                                    String[] split2 = port_b.split("\\.");
                                    //找对侧屏柜信息
                                    KM_SPCD_CUBICLE cubicle2 = getcubicle(1, cubicleB);
                                    if (cubicle2 == null){
                                        break;
                                    }
                                    //找对侧装置
                                    KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicleB, port_b);
                                    //改造
                                    cubicleWholeBean.setKm_spcd_cubicle(cubicle2);
                                    unitWholeBean.setKm_spcd_unit(unit);
                                    unitWholeBean.setBoardPort(getBoardPort(port_b));
                                    Utils.setBoardPort1(unitWholeBean,port_b);
                                    unitWholeBean.setPortADirection(getNoDes(port_b,2));
//                                    setBoardPort(cubicle2,port_b,unitWholeBean);
                                    unitWholeBean.setKm_spcd_coreA(km_spcd_core);
                                    unitWholeBean.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                    unitWholeBean.setPortADesc(getPortADes(cubicleB,port_b,1));
                                    unitWholeBean.setPortAdscInt(getPortADes(cubicleB,port_b,2));
                                    list.add(cubicleWholeBean);
                                    //设置对侧装置
                                    setOtherCubicle(unit,split2,cubicle2);
                                    isGo = 1;//找到之后结束外面的循环标志
                                    break;
                                }
                            }
                        }
                    }else if (cubicleB.equals(cubicleC)){
//                        coresWl(km_spcd_cable,cubicleA,2);
                        if (cores!=null && cores.size()!=0){
                            for (KM_SPCD_CORE km_spcd_core:cores){
                                String port_a = km_spcd_core.getPort_a();
                                String port_b = km_spcd_core.getPort_b();
                                if (port0.equals(port_b)){//尾缆找到对侧屏柜连接，停止循环
                                    String[] split2 = port_a.split("\\.");
                                    //找对侧屏柜信息
                                    KM_SPCD_CUBICLE cubicle2 = getcubicle(1, cubicleA);
                                    if (cubicle2 == null){
                                        break;
                                    }
                                    //找对侧装置
                                    KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicleA, port_a);
                                    //改造
                                    cubicleWholeBean.setKm_spcd_cubicle(cubicle2);
                                    unitWholeBean.setKm_spcd_unit(unit);
                                    unitWholeBean.setBoardPort(getBoardPort(port_a));
                                    Utils.setBoardPort1(unitWholeBean,port_a);
                                    unitWholeBean.setPortADirection(getNoDes(port_a,2));
                                    unitWholeBean.setKm_spcd_coreA(km_spcd_core);
                                    unitWholeBean.setKm_spcd_coreAName(km_spcd_cable.getName()+"("+ cores.size()+"-"+km_spcd_core.getNo() +")");
                                    unitWholeBean.setPortADesc(getPortADes(cubicleA,port_a,1));
                                    unitWholeBean.setPortAdscInt(getPortADes(cubicleA,port_a,2));
                                    list.add(cubicleWholeBean);
                                    //设置对侧装置
                                    setOtherCubicle(unit,split2,cubicle2);
                                    isGo = 1;//找到之后结束外面的循环标志
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private void setBoardPort(KM_SPCD_CUBICLE cubicle, String port, WholeCircuitBean.UnitWholeBean unitWholeBean) {
        if (!TextUtils.isEmpty(port)){
            String[] split = port.split("\\.");
            KM_SPCD_UNIT km_spcd_unitA = null;
            try {
                km_spcd_unitA = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", cubicle.getId(), "name", split[0]);
                KM_SPCD_BOARD km_spcd_boardA = (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unitA.getId(), "slot", split[1]);
                unitWholeBean.setKm_spcd_boardA(km_spcd_boardA);
                if (!TextUtils.isEmpty(split[2]) && split[2].contains("-")){
                    String[] split5 = split[2].split("-");
                    List<KM_SPCD_PORT> ports = (List<KM_SPCD_PORT>) portDao.getListForEq("board_id", km_spcd_boardA.getId(), "no", split5[0]);
                    if (ports!=null && ports.size()!=0){
                        for (KM_SPCD_PORT km_spcd_port:ports){
                            if (!km_spcd_port.getDirection().equals(split5[1])){
                                unitWholeBean.setKm_spcd_portA(km_spcd_port);
                            }
                        }
                    }
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    private void setOtherCubicle(KM_SPCD_UNIT unit, String[] split2, KM_SPCD_CUBICLE cubicle2){
        //设置对侧装置
        List<IEDsWholeCircuitDataPool.IED> ieds2 = new ArrayList<>();
        IEDsWholeCircuitDataPool.IED ied2 = new IEDsWholeCircuitDataPool.IED();
        ied2.setIedName(buildUnitNameDesc(unit));
        ied2.setIed_portNo(split2[2]);
        ieds2.add(ied2);

        IEDsWholeCircuitDataPool.CubicleGroupBaseBean cubicleGroupBaseBean2 = new IEDsWholeCircuitDataPool.CubicleGroupBaseBean();
        cubicleGroupBaseBean2.setCubicleName(buildCubiculeNameDesc(cubicle2));
        cubicleGroupBaseBean2.setIeds(ieds2);
        //设置类型
        cubicleGroupBaseBean2.setLocationOf(DOUBLE_TO);
        cubicleGroupBaseBean1.setLocationOf(DOUBLE_FROM);
        //设置连接类型
        instance.setLink_type(IEDsWholeCircuitDataPool.TYPE_1IED_IN_1CUBICULE_STRAIT_THR);

        //3.添加第一个屏柜
        cubicles.add(cubicleGroupBaseBean1);
        cubicles.add(cubicleGroupBaseBean2);
    }

    private void coresWl(KM_SPCD_CABLE km_spcd_cable, String cubicle0, int type){
        List<KM_SPCD_CORE> cores = coreDao.getListForEq("cable_id", km_spcd_cable.getId());
        if (cores!=null && cores.size()!=0){
            for (KM_SPCD_CORE km_spcd_core:cores){
                String port_a = km_spcd_core.getPort_a();
                String port_b = km_spcd_core.getPort_b();
                PortConnectBean.IedConnectBean iedConnectBean = null;
                if (type == 1){//port_a是本屏柜装置，port_b是对侧屏柜装置
                    KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicle0, port_b);
                    if (unit!=null){
                        KM_SPCD_INTCORE km_spcd_intcore = new KM_SPCD_INTCORE();
                        km_spcd_intcore.setPort_a(port_a);
                        km_spcd_intcore.setPort_b(port_b);
                        setIedConnectBean(unit,km_spcd_intcore,1);
                    }
                }else if (type == 2){
                    KM_SPCD_UNIT unit = getUnit(km_spcd_cable.getStation_id(), cubicle0, port_a);
                    if (unit!=null){
                        KM_SPCD_INTCORE km_spcd_intcore = new KM_SPCD_INTCORE();
                        km_spcd_intcore.setPort_a(port_a);
                        km_spcd_intcore.setPort_b(port_b);
                        setIedConnectBean(unit,km_spcd_intcore,2);
                    }
                }
            }
        }
    }

    private void setIedConnectBean(KM_SPCD_UNIT unit,KM_SPCD_INTCORE km_spcd_intcore,Integer type){
        PortConnectBean.IedConnectBean iedConnectBean = getIedConnectBean(unit);
        List<KM_SPCD_INTCORE> intcores;
        List<PortConnectBean.IntcoreBean> intcoress;
        if (iedConnectBean == null){
            iedConnectBean = new PortConnectBean.IedConnectBean();
            intcores = new ArrayList<>();
            intcoress = new ArrayList<>();
            iedConnectBean.setOtherUnit(unit);
            PortConnectBean.IntcoreBean intcoreBean = setSwitchIedFinal(km_spcd_intcore, null, null, type);
            intcores.add(km_spcd_intcore);
            intcoress.add(intcoreBean);

            iedConnectBean.setIntcores(intcores);
            iedConnectBean.setIntcoress(intcoress);
            iedList.add(iedConnectBean);
        }else {
            intcores = iedConnectBean.getIntcores();
            intcoress = iedConnectBean.getIntcoress();
            PortConnectBean.IntcoreBean intcoreBean = setSwitchIedFinal(km_spcd_intcore, null, null, type);
            intcores.add(km_spcd_intcore);
            intcoress.add(intcoreBean);
            iedConnectBean.setIntcores(intcores);
            iedConnectBean.setIntcoress(intcoress);
        }
    }

    public static String buildCubiculeNameDesc(KM_SPCD_CUBICLE cubicle){
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(cubicle.getName())
                .append("-")
                .append(cubicle.getDescription());
        return stringBuffer.toString();
    }

    public static String buildUnitNameDesc(KM_SPCD_UNIT unit){
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(unit.getName())
                .append("-")
                .append(unit.getDescription());
        return stringBuffer.toString();
    }


    //-----------工具类-----------

    //通过
    private KM_SPCD_PORT getPortByPortA(KM_SPCD_CUBICLE cubicle,String port){
        String[] split4 = port.split("\\.");
        String s1 = split4[2];
        KM_SPCD_UNIT km_spcd_unitA = null;
        try {
            km_spcd_unitA = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", cubicle.getId(), "name", split4[0]);
            KM_SPCD_BOARD km_spcd_boardA = (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unitA.getId(), "slot", split4[1]);
            if (!TextUtils.isEmpty(s1) && s1.contains("-")){
                String[] split5 = s1.split("-");
                if (km_spcd_boardA!=null){
                    KM_SPCD_PORT port1 = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", km_spcd_boardA.getId(), "no", split5[0],"direction",split5[1]);
                    return port1;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    //通过porta,找到porta收的另一条光纤
    private KM_SPCD_PORT getRelevancePort(KM_SPCD_CUBICLE cubicle,String unit){
        String[] split4 = unit.split("\\.");
        String s1 = split4[2];
        KM_SPCD_UNIT km_spcd_unitA = null;
        try {
            km_spcd_unitA = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", cubicle.getId(), "name", split4[0]);
            KM_SPCD_BOARD km_spcd_boardA = (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unitA.getId(), "slot", split4[1]);
            if (!TextUtils.isEmpty(s1) && s1.contains("-")){
                String[] split5 = s1.split("-");
                if (km_spcd_boardA!=null){
                    List<KM_SPCD_PORT> ports = (List<KM_SPCD_PORT>) portDao.getListForEq("board_id", km_spcd_boardA.getId(), "no", split5[0]);
                    if (ports!=null && ports.size()!=0){
                        for (KM_SPCD_PORT km_spcd_port:ports){
                            if (!km_spcd_port.getDirection().equals(split5[1])){
                                return km_spcd_port;
                            }
                        }
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    private String getOtherPort(KM_SPCD_CUBICLE cubicle,String unit,Integer type){
        String[] split4 = unit.split("\\.");
        String s1 = split4[2];
        KM_SPCD_UNIT km_spcd_unitA = null;
        try {
            km_spcd_unitA = (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id", cubicle.getId(), "name", split4[0]);
            KM_SPCD_BOARD km_spcd_boardA = (KM_SPCD_BOARD)boardDao.getFirstForEq("unit_id", km_spcd_unitA.getId(), "slot", split4[1]);
            if (!TextUtils.isEmpty(s1) && s1.contains("-")){
                String[] split5 = s1.split("-");
                if (km_spcd_boardA!=null){
                    List<KM_SPCD_PORT> ports = (List<KM_SPCD_PORT>) portDao.getListForEq("board_id", km_spcd_boardA.getId(), "no", split5[0]);
                    if (ports!=null && ports.size()!=0){
                        for (KM_SPCD_PORT km_spcd_port:ports){
                            if (!km_spcd_port.getDirection().equals(split5[1])){
                                if (type == 1){
                                    return km_spcd_port.getDirection();
                                }else if (type == 2){
                                    return km_spcd_unitA.getName()+"."+km_spcd_boardA.getSlot()+"."+km_spcd_port.getNo()+"-"+km_spcd_port.getDirection();
                                }
                            }
                        }
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    public KM_SPCD_INTCORE getOtherIntcore(String port,Integer type){
        String[] split1 = port.split("\\.");
        List<KM_SPCD_BOARD> boards = (List<KM_SPCD_BOARD>) boardDao.getListForEq("unit_id", km_spcd_unit.getId(), "slot", split1[1]);
        if (boards!=null && boards.size()!=0){
            KM_SPCD_BOARD km_spcd_board = boards.get(0);
            String no = getNoDes(port, 1);
            String des = getNoDes(port, 2);
            List<KM_SPCD_PORT> ports = portDao.getListForEq("board_id", km_spcd_board.getId(), "no", no);
            if (ports!=null && ports.size()!=0){
                for (KM_SPCD_PORT km_spcd_port:ports){
                    if (!km_spcd_port.getDirection().equals(des)){
                        String port1 = km_spcd_unit.getName() + "." + km_spcd_board.getSlot() + "." + km_spcd_port.getNo()+"-"+km_spcd_port.getDirection();
                        List<KM_SPCD_INTCORE> intcoress = null;
                        if (type == 1){
                            intcoress = (List<KM_SPCD_INTCORE>) intcoreDao.getListForEq("cubicle_id", km_spcd_cubicle.getId(), "port_a", port1);
                        }else if (type == 2){
                            intcoress = (List<KM_SPCD_INTCORE>) intcoreDao.getListForEq("cubicle_id", km_spcd_cubicle.getId(), "port_b", port1);
                        }
                        if (intcoress!=null && intcoress.size()!=0){
                            KM_SPCD_INTCORE km_spcd_intcore1 = intcoress.get(0);
                            return km_spcd_intcore1;
                        }
                    }
                }
            }
        }
        return null;
    }

    private static String getBoardPort(String port_a) {
        String[] split = port_a.split("\\.");
        String s = split[2];
        String boardPort = null;
        if (!TextUtils.isEmpty(s) && s.contains("-")){
            String[] split1 = s.split("-");
            boardPort = split[1] + "-" + split1[0];
            return boardPort;
        }
        return null;
    }

    private static String getBoard(String port_a) {
        if (!TextUtils.isEmpty(port_a)){
            String[] split = port_a.split("\\.");
            String s = split[1];
            return s;
        }
        return null;
    }

    private static String getPort(String port_a,Integer type) {
        String[] split = port_a.split("\\.");
        String s = split[2];
        String boardPort = null;
        if (!TextUtils.isEmpty(s) && s.contains("-")){
            String[] split1 = s.split("-");
            if (type == 1){
                return split1[0];
            }else if (type == 2){
                return split1[1];
            }
        }
        return null;
    }

    //通过core里面的portA="1n.1.A-Tx"，如1n去找到该装置
    private static String getPortADes(String cubicleA, String port_a, Integer type) {
        if (!TextUtils.isEmpty(cubicleA) && !TextUtils.isEmpty(port_a)){
            String[] split = cubicleA.split("\\.");
            String region = split[0];//小室
            String cubicle = split[1];//屏柜
            String[] split1 = port_a.split("\\.");
            String unitName = split1[0];//unitName
            //查询小室id
            try {
                KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("substation_id", 1,"name",region);
                KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getFirstForEq("region_id", km_spcd_region.getId(), "substation_id", 1,"name",cubicle);
                if (km_spcd_cubicle!=null){
                    KM_SPCD_UNIT km_spcd_unit = (KM_SPCD_UNIT) unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", unitName);
                    KM_SPCD_BOARD km_spcd_board = (KM_SPCD_BOARD) boardDao.getFirstForEq("unit_id", km_spcd_unit.getId(), "slot", split1[1]);
                    if (!TextUtils.isEmpty(split1[2]) && split1[2].contains("-")){
                        String[] split2 = split1[2].split("-");

                        KM_SPCD_PORT km_spcd_port = (KM_SPCD_PORT) portDao.getFirstForEq("board_id", km_spcd_board.getId(), "no", split2[0],"direction",split2[1]);
                        highlightPorts.add(km_spcd_port);

                        if (type == 1){
                            return km_spcd_board.getDescription()+"-"+split2[0];
                        }else if (type == 2){
                            return km_spcd_board.getSlot()+"-"+split2[0];
                        }else {
                            return null;
                        }
                    }
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    //通过core里面的portA="1n.1.A-Tx"，如1n去找到该装置
    private KM_SPCD_CUBICLE getcubicle(Integer stationId, String cubicleA) {
        if (!TextUtils.isEmpty(cubicleA)){
            String[] split = cubicleA.split("\\.");
            String region = split[0];//小室
            String cubicle = split[1];//屏柜
            //查询小室id
            try {
                KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("substation_id", stationId,"name",region);
                if (km_spcd_region == null){
                    return null;
                }
                KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getFirstForEq("region_id", km_spcd_region.getId(), "substation_id", stationId,"name",cubicle);
                if (km_spcd_cubicle!=null){
                    return km_spcd_cubicle;
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public String getNoDes(String port,Integer type){//type=1返回no，type=2返回端口如Tx
        if (!TextUtils.isEmpty(port)){
            String[] split1 = port.split("\\.");
            String noDes = split1[2];
            if (!TextUtils.isEmpty(noDes) && noDes.contains("-")){
                String[] split2 = noDes.split("-");
                if (type == 1){
                    return split2[0];
                }else if (type == 2){
                    return split2[1];
                }else {
                    return null;
                }
            }
        }
        return null;
    }

    //通过core里面的portA="1n.1.A-Tx"，如1n去找到该装置
    private static KM_SPCD_UNIT getUnit(Integer stationId, String cubicleA, String port_a) {
        if (!TextUtils.isEmpty(cubicleA) && !TextUtils.isEmpty(port_a)){
            String[] split = cubicleA.split("\\.");
            String region = split[0];//小室
            String cubicle = split[1];//屏柜
            String[] split1 = port_a.split("\\.");
            String unitName = split1[0];//unitName
            //查询小室id
            try {
                KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("substation_id", stationId,"name",region);
                KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getFirstForEq("region_id", km_spcd_region.getId(), "substation_id", stationId,"name",cubicle);
                if (km_spcd_cubicle!=null){
                    KM_SPCD_UNIT km_spcd_unit = (KM_SPCD_UNIT) unitDao.getFirstForEq("cubicle_id", km_spcd_cubicle.getId(), "name", unitName);
                    return km_spcd_unit;
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    //通过core里面的portA="1n.1.A-Tx"，如1n去找到该装置
    private static KM_SPCD_CUBICLE getCubicle(Integer stationId, String cubicleA) {
        if (!TextUtils.isEmpty(cubicleA)){
            String[] split = cubicleA.split("\\.");
            String region = split[0];//小室
            String cubicle = split[1];//屏柜
            //查询小室id
            try {
                KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("substation_id", stationId,"name",region);
                KM_SPCD_CUBICLE km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getFirstForEq("region_id", km_spcd_region.getId(), "substation_id", stationId,"name",cubicle);
                if (km_spcd_cubicle!=null){
                    return km_spcd_cubicle;
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    //找小室
    private static KM_SPCD_REGION getRegion(KM_SPCD_CUBICLE km_spcd_cubicle) {
        if (km_spcd_cubicle!=null){
            //查询小室
            try {
                KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getById(km_spcd_cubicle.getRegion_id());
                return km_spcd_region;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    //通过core里面的portA="1n.1.A-Tx"，如1n去找到该
    private static KM_SPCD_REGION getRegion(Integer stationId, String cubicleA) {
        if (!TextUtils.isEmpty(cubicleA)){
            String[] split = cubicleA.split("\\.");
            String region = split[0];//小室
            //查询小室id
            try {
                KM_SPCD_REGION km_spcd_region = (KM_SPCD_REGION)regionDao.getFirstForEq("substation_id", stationId,"name",region);
                if (km_spcd_region == null){
                    return null;
                }
                return km_spcd_region;
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
