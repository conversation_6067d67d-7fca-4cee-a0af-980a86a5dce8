package com.kemov.visual.spcd.spcdvisualandroidapp.activity.ied_whole_circuit;

import android.app.Activity;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.util.Pair;

import com.baseres.base.PubApp;

import com.j256.ormlite.stmt.Where;
import com.kemov.sclaata.common.app.PubUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.OdfSwitchPassBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.WholeCircuitBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_REGION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_SUBSTATION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDao;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.Parser.model.ConnectionCoreBase;
import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.Parser.model.Core;
import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.Parser.model.IntCore;
import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.Parser.model.Port;
import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.Parser.model.PortDirection;
import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.Parser.model.Unit;
import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.SPCDFile;
import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.connection.PairPortPhyConnection;
import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.connection.PortPhyPath;
import com.kemov.visual.spcd.spcdvisualandroidapp.spcdex.connection.UnitPhyConnection;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.SpcdUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 传入端口，找到中间经过的装置，及目的装置（找到交换机会接着找，直到找到ied装置）
 */
public class HandleForWholeCircuitsOptimizeEx {
    private final String TAG = "HandleForWholeCircuitsOptimizeEx";

    public final String DEVCLASS_IED = "IED";
    public final String DEVCLASS_SWITCH = "SWITCH";
    public final String DEVCLASS_ODF = "ODF";

    Activity mCtx;
    private static SPCDFile g_spcdFile;
    private static String g_spcdFilePath;
    private String dbName;
    public PubUtils mPubUtils = null;

    BaseDao cableDao = null;
    BaseDao coreDao = null;
    BaseDaoImp unitDao = null;
    BaseDaoImp boardDao = null;
    BaseDaoImp portDao = null;
    BaseDaoImp cubicleDao = null;
    BaseDaoImp regionDao = null;
    BaseDaoImp intcoreDao = null;
    BaseDaoImp substationDao = null;
    private KM_SPCD_UNIT km_spcd_unit;
    private KM_SPCD_CUBICLE km_spcd_cubicle;
    private KM_SPCD_REGION km_spcd_region;
    private KM_SPCD_SUBSTATION km_spcd_substation;

    List<KM_SPCD_PORT> highlightPorts = new ArrayList<>();
    List<Map<String, List<List<OdfSwitchPassBean>>>> listAll = new ArrayList<>();//所有连到目的装置的路线集合,不过滤的


    public static String getTxRx(String port) {
        String port1 = null;
        if (port.endsWith("Tx")) {
            port1 = "Rx";
        } else if (port.endsWith("Rx")) {
            port1 = "Tx";
        } else if (port.endsWith("RT")) {
            port1 = "RT";
        }
        return port1;
    }

    public List<Map<String, List<List<OdfSwitchPassBean>>>> getListAll() {
        return listAll;
    }

    private List<UnitPhyConnection> getPhyPathFromPortId(SPCDFile spcdFile, Integer portId) {
        List<UnitPhyConnection> ret = new ArrayList<>();
        try {
            KM_SPCD_PORT port = (KM_SPCD_PORT) portDao.getById(portId);
            if (port == null)
                return ret;
            KM_SPCD_BOARD board = (KM_SPCD_BOARD) boardDao.getById(port.getBoard_id());
            km_spcd_unit = SpcdUtils.getUnitByPortId(mCtx, dbName, port);
            if (km_spcd_unit == null) {
                return ret;
            }
            km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getById(km_spcd_unit.getCubicle_id());//屏柜
            km_spcd_region = (KM_SPCD_REGION) regionDao.getById(km_spcd_cubicle.getRegion_id());//小室
            km_spcd_substation =(KM_SPCD_SUBSTATION) substationDao.getById(km_spcd_region.getSubstation_id());
            //组成Port的全路径
            String portFullPath = km_spcd_unit.getName() + "." + board.getSlot() + "." + port.getNo() + "-" + port.getDirection();
            String cubiclePath = km_spcd_region.getName() + "." + km_spcd_cubicle.getName();
            return spcdFile.getUnitPortPhyConnection(cubiclePath, portFullPath);
        } catch (Exception e) {
            e.printStackTrace();
            return ret;
        }
    }

    private List<UnitPhyConnection> getPhyPathFromIntCoreId(SPCDFile spcdFile, Integer intCoreid) {
        KM_SPCD_INTCORE intcore = (KM_SPCD_INTCORE) intcoreDao.getById(intCoreid);
        if (intcore == null)
            return new ArrayList<>();
        km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getById(intcore.getCubicle_id());
        if (km_spcd_cubicle == null)
            return new ArrayList<>();
        km_spcd_region = (KM_SPCD_REGION) regionDao.getById(km_spcd_cubicle.getRegion_id());
        km_spcd_unit = SpcdUtils.getUnitByIntcoreId(mCtx, dbName, intcore);
        if (km_spcd_unit == null || km_spcd_region == null)
            return new ArrayList<>();
        km_spcd_substation =(KM_SPCD_SUBSTATION) substationDao.getById(km_spcd_region.getSubstation_id());
        //组成Port的全路径
        String portaFullPath = intcore.getPort_a();
        String cubiclePath = km_spcd_region.getName() + "." + km_spcd_cubicle.getName();
        List<UnitPhyConnection> ret = spcdFile.getUnitPortPhyConnection(cubiclePath, portaFullPath);
        //去除不包含 intCore.getPort_b()的路径
        return ret;
    }

    private List<UnitPhyConnection> getPhyPathFromUnitId(SPCDFile spcdFile,Integer unitId) {
        List<UnitPhyConnection> ret = new ArrayList<>();
        km_spcd_unit = (KM_SPCD_UNIT) unitDao.getById(unitId);
        if (km_spcd_unit == null)
            return ret;
        km_spcd_cubicle = (KM_SPCD_CUBICLE) cubicleDao.getById(km_spcd_unit.getCubicle_id());
        if (km_spcd_cubicle == null)
            return null;
        km_spcd_region = (KM_SPCD_REGION) regionDao.getById(km_spcd_cubicle.getRegion_id());
        km_spcd_substation =(KM_SPCD_SUBSTATION) substationDao.getById(km_spcd_region.getSubstation_id());
        String unitPath=km_spcd_region.getName()+"."+km_spcd_cubicle.getName()+"."+km_spcd_unit.getName();
        return spcdFile.getUnitPhyConnection(unitPath,null);
    }
    private void initDao() {
        unitDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_UNIT.class, dbName);
        boardDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_BOARD.class, dbName);
        portDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_PORT.class, dbName);
        cableDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_CABLE.class, dbName);
        coreDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_CORE.class, dbName);
        cubicleDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_CUBICLE.class, dbName);
        regionDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_REGION.class, dbName);
        intcoreDao = new BaseDaoImp(mCtx.getApplicationContext(), KM_SPCD_INTCORE.class, dbName);
        substationDao =new BaseDaoImp(mCtx.getApplicationContext(),KM_SPCD_SUBSTATION.class,dbName);
        highlightPorts.clear();

    }
    /**
     * @param mCtx
     * @param dbName
     * @param id
     * @param idType          type == 1 传进来的是装置unitid
     *                        type == 2 传进来的是装置IntCoreId
     *                        type == 3 || type == 4 传进来的是装置PortId
     * @param goalUnitIedName
     */
    public HandleForWholeCircuitsOptimizeEx(Activity mCtx, String dbName, Integer id, Integer idType, String goalUnitIedName) {
        this.mCtx = mCtx;
        this.dbName = dbName;
        String spcdFileFullPath = PubApp.getSpcdFilePath();
        if (spcdFileFullPath == null || spcdFileFullPath.isEmpty())
            return;
        initDao();
        if(g_spcdFilePath==null||!spcdFileFullPath.equals(g_spcdFilePath)) {
            g_spcdFile = SPCDFile.parseFromFile(spcdFileFullPath);
            g_spcdFilePath=spcdFileFullPath;
        }
        List<UnitPhyConnection> phyConnections = null;

        switch (idType) {
            case 1://传进来的是装置 id
                phyConnections = getPhyPathFromUnitId(g_spcdFile,id);
                break;
            case 2://传进来的是intCore id
                phyConnections = getPhyPathFromIntCoreId(g_spcdFile, id);
                break;
            case 3://传进来的是portId
            case 4://传进来的是portId
                phyConnections = getPhyPathFromPortId(g_spcdFile, id);
                break;
        }
        //通过goalUnitIedName过滤
        if(goalUnitIedName!=null&&goalUnitIedName.isEmpty()){
            phyConnections=phyConnections.stream().filter(unitPhyConnection->{
                return unitPhyConnection.getEndUnit().getIedName().equals(goalUnitIedName);
            }).collect(Collectors.toList());
        }
        //转换成listAll
        listAll=new ArrayList<>();
        listAll.add(toOdfSwitchPassBea(phyConnections));
    }

    private Map<String, List<List<OdfSwitchPassBean>>> toOdfSwitchPassBea(List<UnitPhyConnection> phyConnections) {
        Map<String, List<List<OdfSwitchPassBean>>> ret=new HashMap<>();
        for(UnitPhyConnection unitPhyConnection:phyConnections){
            String iedName=unitPhyConnection.getEndUnit().getIedName();
            if(iedName.isEmpty())//如果没有iedName，则用unitName代替
                iedName=unitPhyConnection.getEndUnit().getName();
            List<OdfSwitchPassBean> paths=unitPhyConnectionToOdfSwitchPassBean(unitPhyConnection);
            if(ret.containsKey(iedName)){
                ret.get(iedName).add(paths);
            }else {
                List<List<OdfSwitchPassBean>> value=new ArrayList<>();
                value.add(paths);
                ret.put(iedName,value);
            }
        }
        return ret;
    }

    /**
     * 物理连接向OdfSwitchPassBean转换
     * @param unitPhyConnection
     * @return
     */
    private List<OdfSwitchPassBean> unitPhyConnectionToOdfSwitchPassBean(UnitPhyConnection unitPhyConnection){
        List<OdfSwitchPassBean> ret=new ArrayList<>();

        for(PairPortPhyConnection pairPortPhyConnection:unitPhyConnection.getPairPortPath()){
            List<Port> rxPortList=pairPortPhyConnection.getPortRxPath().getPathAllPort();
            List<Port> txPortList=pairPortPhyConnection.getPortTxPath().getPathAllPort();
            //rxPortList和txPortList 有可能不一样长取最长的链路
            List<Port> portList=rxPortList.size()>txPortList.size()?rxPortList:txPortList;
            List<Port> pairPortList=rxPortList.size()>txPortList.size()?txPortList:rxPortList;
            int size =  portList.size();
            for(int i=0;i<size;i++) {
                Port port=portList.get(i);
                Port prePort=(i==0)?portList.get(i+1):portList.get(i-1);//第一个元素取后面一个port否则取前一个port
                KM_SPCD_UNIT unit = unitConvert(port.getUnit());
                ConnectionCoreBase core=port.getPortConnection(prePort);
                Port pairPort=i<pairPortList.size()?pairPortList.get(i):null;//port的TxRx成对端口
                OdfSwitchPassBean odfSwitchPassBean = new OdfSwitchPassBean();
                odfSwitchPassBean.setKm_spcd_unit(unit);
                Object coreConnection=coreConvert(core);
                if(coreConnection instanceof KM_SPCD_CORE){
                    odfSwitchPassBean.setKm_spcd_core((KM_SPCD_CORE)coreConnection);
                    odfSwitchPassBean.setKm_spcd_cable((KM_SPCD_CABLE) cableDao.getById((((KM_SPCD_CORE) coreConnection).getCable_id())));
                }else{
                    odfSwitchPassBean.setKm_spcd_intcore((KM_SPCD_INTCORE)coreConnection);
                }
                odfSwitchPassBean.setPortA(port.getCorePath());
                odfSwitchPassBean.setType(port.equals(core.getPortAObject())?1:2);
                if(DEVCLASS_SWITCH.equals(unit.getDev_class())&&(i<size-1)) {
                    pairPort=portList.get(i+1);
                    i++;
                }
                odfSwitchPassBean.setPortB(pairPort==null?"":pairPort.getCorePath());
                //高亮的端口还没有设置
                //odfSwitchPassBean.setHighlightPorts(odfSwitchBean1.getHighlightPorts());
                //设置unitWholeBean(对应port)和unitWholeBean1(对应pairPort;
                WholeCircuitBean.UnitWholeBean unitWholeBean=new WholeCircuitBean.UnitWholeBean();
                WholeCircuitBean.UnitWholeBean unitWholeBean1=new WholeCircuitBean.UnitWholeBean();
                unitWholeBean.setKm_spcd_unit(unit);
                unitWholeBean.setBoard(port.getBoard().getSlot());
                unitWholeBean.setBoardPort(port.getBoard().getSlot()+"-"+port.getNo());
                unitWholeBean.setPort(port.getNo());
                unitWholeBean.setPortADirection(port.getDirection().getValue());
                //如果是ODF必须是板卡号-端口号
                if("ODF".equals(unit.getDev_class()))
                    unitWholeBean.setPortADesc(port.getBoard().getSlot()+"-"+port.getNo());
                else
                    unitWholeBean.setPortADesc(port.getDesc());

                unitWholeBean1.setKm_spcd_unit(unit);
                unitWholeBean1.setBoard(pairPort==null?"":pairPort.getBoard().getSlot());
                unitWholeBean1.setBoardPort(pairPort==null?"":port.getBoard().getSlot()+"-"+port.getNo());
                unitWholeBean1.setPort(pairPort==null?"":pairPort.getNo());
                unitWholeBean1.setPortADirection(pairPort==null?"":pairPort.getDirection().getValue());
                //如果是ODF必须是板卡号-端口号
                if("ODF".equals(unit.getDev_class()))
                    unitWholeBean1.setPortADesc(pairPort==null?null:pairPort.getBoard().getSlot()+"-"+pairPort.getNo());
                else
                    unitWholeBean1.setPortADesc(pairPort==null?"":pairPort.getDesc());
                if(DEVCLASS_SWITCH.equals(unit.getDev_class())) {
                    unitWholeBean.setPort1(pairPort==null?"":pairPort.getNo());
                    unitWholeBean.setBoard1(pairPort==null?"":pairPort.getBoard().getSlot());
                    unitWholeBean1.setPort(port.getNo());
                    unitWholeBean1.setBoard(port.getBoard().getSlot());
                    unitWholeBean1.setBoard(port.getBoard().getSlot());
                    unitWholeBean1.setPort1(pairPort==null?"":pairPort.getNo());
                    unitWholeBean1.setBoard1(pairPort==null?"":pairPort.getBoard().getSlot());
                    unitWholeBean.setPortA1Direction(pairPort==null?"": PortDirection.getPairDirection(port.getDirection()).getValue());
                    unitWholeBean1.setPortA1Direction(pairPort==null?"":PortDirection.getPairDirection(pairPort.getDirection()).getValue());
                }

                if(coreConnection instanceof KM_SPCD_INTCORE)
                    unitWholeBean.setKm_spcd_intcoreA((KM_SPCD_INTCORE) coreConnection);
                else
                    unitWholeBean.setKm_spcd_coreA((KM_SPCD_CORE) coreConnection);
                odfSwitchPassBean.setUnitWholeBean(unitWholeBean);
                if(pairPort!=null)
                    odfSwitchPassBean.setUnitWholeBean1(unitWholeBean1);
                odfSwitchPassBean.setKm_spcd_cubicle((KM_SPCD_CUBICLE)cubicleDao.getById(unit.getCubicle_id()));
                ret.add(odfSwitchPassBean);
            }
        }
        return ret;
    }
    private KM_SPCD_UNIT unitConvert(Unit unit){
        try {
            KM_SPCD_REGION region = (KM_SPCD_REGION) regionDao.getFirstForEq("substation_id", km_spcd_substation.getId(), "name", unit.getRegion().getName());
            KM_SPCD_CUBICLE cubicle=(KM_SPCD_CUBICLE) cubicleDao.getFirstForEq("region_id",region.getId(),"name",unit.getCubicle().getName());
            return (KM_SPCD_UNIT)unitDao.getFirstForEq("cubicle_id",cubicle.getId(),"name",unit.getName());
        }catch (Exception ex){
            ex.printStackTrace();
            return null;
        }
    }
    private Object coreConvert(ConnectionCoreBase connectionCoreBase){
        //判断是intCore还是Core
        if(connectionCoreBase instanceof IntCore){//是intCore连接
            Port port=connectionCoreBase.getPortAObject()==null?connectionCoreBase.getPortBObject():connectionCoreBase.getPortAObject();
            if(port==null)
                return null;
            KM_SPCD_UNIT unit=unitConvert(port.getUnit());
            if(unit==null)
                return null;
            try {
                Where condition = intcoreDao.where();
                condition.and(condition.eq("cubicle_id", unit.getCubicle_id()),
                        condition.or(
                                condition.and(condition.eq("port_a", connectionCoreBase.getPortA()),
                                        condition.eq("port_b", connectionCoreBase.getPortB())
                                ),
                                condition.and(condition.eq("port_a", connectionCoreBase.getPortB()),
                                        condition.eq("port_b", connectionCoreBase.getPortA())
                                )
                        )
                );
                List<KM_SPCD_INTCORE> retList=intcoreDao.query(condition);
                if(retList==null||retList.size()==0)
                    return null;
                return retList.get(0);
            }catch (Exception ex)
            {
                ex.printStackTrace();;
                return null;
            }
        }else {//是core连接
            Core core=(Core)connectionCoreBase;
            try {
                KM_SPCD_CABLE cable =(KM_SPCD_CABLE) cableDao.getFirstForEq("name", core.getCable().getName());
                Where condition=coreDao.where();
                condition.and(condition.eq("cable_id",cable.getId()),
                        condition.or(
                                condition.and(condition.eq("port_a",core.getPortA()),
                                        condition.eq("port_b",core.getPortB())),
                                condition.and(condition.eq("port_a",core.getPortB()),
                                        condition.eq("port_b",core.getPortA()))
                        )
                );
                List<KM_SPCD_CORE> coreList=coreDao.query(condition);
                if((coreList==null)||(coreList.size()==0))
                    return null;
                return coreList.get(0);
            }catch (Exception ex){
                ex.printStackTrace();
                return null;
            }
        }
    }
}
