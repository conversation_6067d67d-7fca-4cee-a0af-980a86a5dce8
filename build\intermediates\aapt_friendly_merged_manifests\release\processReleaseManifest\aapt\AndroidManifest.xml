<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.kemov.visual.spcd.spcdvisualandroidapp"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="28" />
    <!-- gy added begin -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:name="com.kemov.visual.spcd.spcdvisualandroidapp.MainApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme" >
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.switch_vlan.SwitchVlanShowActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.PlansFilesShowActivity" />
        <activity
            android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.CenterNodeActivity"
            android:theme="@style/activityTheme" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.device_files.DevFilesShowActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.OpticalInfoActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.GlConnectActivity" />
        <activity
            android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.TagViewDlgActivity"
            android:theme="@android:style/Theme.Holo.Dialog.NoActionBar" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.GQActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.GlwlLabelActivity" />
        <activity
            android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.GlwlLabelActivityDlg"
            android:theme="@style/DialogStyleInActivity" />
        <activity
            android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.TopBarBaseActivity"
            android:label="@string/title_activity_top_bar_base"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.GyTestZoomingActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.GlwlDetailActivity" />
        <activity
            android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.VirPortsLinksActivity"
            android:label="@string/title_activity_vir_ports_links"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.OdfActivity"
            android:theme="@style/activityTheme" />
        <activity
            android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.GLWLGQActivity"
            android:theme="@style/activityTheme" />
        <activity
            android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.OdfNewActivity"
            android:theme="@style/activityTheme" />
        <activity
            android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.OdfFinalActivity"
            android:launchMode="singleTask"
            android:theme="@style/activityTheme" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.VRCircleViewActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.cubicle.DeviceBackPanelViewActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.OdfLogicActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.OdfViewActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.GLActivity" />
        <activity
            android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.IedsWholeCircuitActivity"
            android:configChanges="orientation|keyboardHidden|screenSize" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.cubicle.CubicleListActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.cubicle.RegionListActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.SubstationActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.MainActivity" >

            <!--
             <intent-filter>
             <action android:name="android.intent.action.MAIN" />
             <category android:name="android.intent.category.LAUNCHER" />
             </intent-filter>
            -->
        </activity>
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.CubicleViewShowActivity" />
        <activity
            android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.CubicleBackPanelViewShowActivity"
            android:label="@string/title_activity_cubicle_back_panel_view_show"
            android:theme="@style/AppTheme.NoActionBar" /> <!-- 线缆可视化模块 -->
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.cubicle.CubicleVisualActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.CableAndCoreListActivity" /> <!-- 调试模块 -->
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.TestActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.WLActivity" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.optical_fiber_visual.OpticalFiberVisualActivity" />
        <activity
            android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.IEDVisualActivity"
            android:theme="@style/activityTheme" /> <!-- android:launchMode="singleTop" -->
        <activity
            android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.IEDVisualActivityNew"
            android:theme="@style/activityTheme" />
        <activity android:name="com.kemov.visual.spcd.spcdvisualandroidapp.activity.SecondActivity" />
    </application>

</manifest>