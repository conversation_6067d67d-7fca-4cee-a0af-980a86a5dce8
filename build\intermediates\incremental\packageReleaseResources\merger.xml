<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res"/><source path="D:\svn\DeviceVisualAS\SpcdVisual\build\generated\res\rs\release"/><source path="D:\svn\DeviceVisualAS\SpcdVisual\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res"><file name="dialog_enter" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\dialog_enter.xml" qualifiers="" type="anim"/><file name="dialog_exit" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\dialog_exit.xml" qualifiers="" type="anim"/><file name="drop_down_from_top" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\drop_down_from_top.xml" qualifiers="" type="anim"/><file name="drop_down_to_bottom" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\drop_down_to_bottom.xml" qualifiers="" type="anim"/><file name="fade_in" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="fade_out" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\fade_out.xml" qualifiers="" type="anim"/><file name="grow_fade_in_from_bottom" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\grow_fade_in_from_bottom.xml" qualifiers="" type="anim"/><file name="head_in" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\head_in.xml" qualifiers="" type="anim"/><file name="head_out" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\head_out.xml" qualifiers="" type="anim"/><file name="hide_to_top" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\hide_to_top.xml" qualifiers="" type="anim"/><file name="hold" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\hold.xml" qualifiers="" type="anim"/><file name="m_down_dialog" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\m_down_dialog.xml" qualifiers="" type="anim"/><file name="m_up_dialog" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\m_up_dialog.xml" qualifiers="" type="anim"/><file name="popup_form_bottom" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\popup_form_bottom.xml" qualifiers="" type="anim"/><file name="push_bottom_in" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\push_bottom_in.xml" qualifiers="" type="anim"/><file name="push_bottom_out" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\push_bottom_out.xml" qualifiers="" type="anim"/><file name="push_left_in" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\push_left_in.xml" qualifiers="" type="anim"/><file name="push_left_out" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\push_left_out.xml" qualifiers="" type="anim"/><file name="push_right_in" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\push_right_in.xml" qualifiers="" type="anim"/><file name="push_right_out" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\push_right_out.xml" qualifiers="" type="anim"/><file name="push_top_in" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\push_top_in.xml" qualifiers="" type="anim"/><file name="push_top_in2" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\push_top_in2.xml" qualifiers="" type="anim"/><file name="push_top_out" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\push_top_out.xml" qualifiers="" type="anim"/><file name="push_top_out2" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\push_top_out2.xml" qualifiers="" type="anim"/><file name="push_up_in" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\push_up_in.xml" qualifiers="" type="anim"/><file name="push_up_out" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\push_up_out.xml" qualifiers="" type="anim"/><file name="shrink_fade_out_from_bottom" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\shrink_fade_out_from_bottom.xml" qualifiers="" type="anim"/><file name="slide_in_from_left" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\slide_in_from_left.xml" qualifiers="" type="anim"/><file name="slide_in_from_right" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\slide_in_from_right.xml" qualifiers="" type="anim"/><file name="slide_out_to_left" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\slide_out_to_left.xml" qualifiers="" type="anim"/><file name="slide_out_to_right" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\anim\slide_out_to_right.xml" qualifiers="" type="anim"/><file name="border" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\border.xml" qualifiers="" type="drawable"/><file name="file_icon_default" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\file_icon_default.png" qualifiers="" type="drawable"/><file name="folder_1" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\folder_1.png" qualifiers="" type="drawable"/><file name="ic_android_black_24dp" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ic_android_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_enter" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ic_enter.png" qualifiers="" type="drawable"/><file name="ic_enter2" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ic_enter2.png" qualifiers="" type="drawable"/><file name="ic_svg_close" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ic_svg_close.xml" qualifiers="" type="drawable"/><file name="ied_fc_close_rt" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_fc_close_rt.xml" qualifiers="" type="drawable"/><file name="ied_fc_open_rt" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_fc_open_rt.xml" qualifiers="" type="drawable"/><file name="ied_fc_open_tr" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_fc_open_tr.xml" qualifiers="" type="drawable"/><file name="ied_lc_r" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_lc_r.xml" qualifiers="" type="drawable"/><file name="ied_lc_rt" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_lc_rt.xml" qualifiers="" type="drawable"/><file name="ied_lc_t" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_lc_t.xml" qualifiers="" type="drawable"/><file name="ied_lc_tr" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_lc_tr.xml" qualifiers="" type="drawable"/><file name="ied_na" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_na.xml" qualifiers="" type="drawable"/><file name="ied_pair_r" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_pair_r.xml" qualifiers="" type="drawable"/><file name="ied_pair_t" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_pair_t.xml" qualifiers="" type="drawable"/><file name="ied_rj45" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_rj45.xml" qualifiers="" type="drawable"/><file name="ied_sc_rt" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_sc_rt.xml" qualifiers="" type="drawable"/><file name="ied_sc_tr" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_sc_tr.xml" qualifiers="" type="drawable"/><file name="ied_st_rt" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_st_rt.xml" qualifiers="" type="drawable"/><file name="ied_st_tr" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\ied_st_tr.xml" qualifiers="" type="drawable"/><file name="item_selector" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\item_selector.xml" qualifiers="" type="drawable"/><file name="switch_fc_down_rt" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_fc_down_rt.xml" qualifiers="" type="drawable"/><file name="switch_fc_down_tr" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_fc_down_tr.xml" qualifiers="" type="drawable"/><file name="switch_fc_up_rt" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_fc_up_rt.xml" qualifiers="" type="drawable"/><file name="switch_fc_up_tr" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_fc_up_tr.xml" qualifiers="" type="drawable"/><file name="switch_lc_down_r" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_lc_down_r.xml" qualifiers="" type="drawable"/><file name="switch_lc_down_rt" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_lc_down_rt.xml" qualifiers="" type="drawable"/><file name="switch_lc_down_t" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_lc_down_t.xml" qualifiers="" type="drawable"/><file name="switch_lc_down_tr" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_lc_down_tr.xml" qualifiers="" type="drawable"/><file name="switch_lc_up_r" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_lc_up_r.xml" qualifiers="" type="drawable"/><file name="switch_lc_up_rt" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_lc_up_rt.xml" qualifiers="" type="drawable"/><file name="switch_lc_up_t" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_lc_up_t.xml" qualifiers="" type="drawable"/><file name="switch_lc_up_tr" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_lc_up_tr.xml" qualifiers="" type="drawable"/><file name="switch_pair_r_down" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_pair_r_down.xml" qualifiers="" type="drawable"/><file name="switch_pair_r_up" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_pair_r_up.xml" qualifiers="" type="drawable"/><file name="switch_pair_t_down" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_pair_t_down.xml" qualifiers="" type="drawable"/><file name="switch_pair_t_up" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_pair_t_up.xml" qualifiers="" type="drawable"/><file name="switch_rj45_down" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_rj45_down.xml" qualifiers="" type="drawable"/><file name="switch_rj45_up" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_rj45_up.xml" qualifiers="" type="drawable"/><file name="switch_sc_down_rt" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_sc_down_rt.xml" qualifiers="" type="drawable"/><file name="switch_sc_down_tr" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_sc_down_tr.xml" qualifiers="" type="drawable"/><file name="switch_sc_up_rt" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_sc_up_rt.xml" qualifiers="" type="drawable"/><file name="switch_sc_up_tr" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_sc_up_tr.xml" qualifiers="" type="drawable"/><file name="switch_st_down_rt" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_st_down_rt.xml" qualifiers="" type="drawable"/><file name="switch_st_down_tr" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_st_down_tr.xml" qualifiers="" type="drawable"/><file name="switch_st_up_rt" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_st_up_rt.xml" qualifiers="" type="drawable"/><file name="switch_st_up_tr" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\switch_st_up_tr.xml" qualifiers="" type="drawable"/><file name="swithc_lc_down_r" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\swithc_lc_down_r.xml" qualifiers="" type="drawable"/><file name="taginfo_textcolor_selector" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\taginfo_textcolor_selector.xml" qualifiers="" type="drawable"/><file name="text_color_selector_menu" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\drawable\text_color_selector_menu.xml" qualifiers="" type="drawable"/><file name="activity_cable_and_core_layout" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_cable_and_core_layout.xml" qualifiers="" type="layout"/><file name="activity_center_node" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_center_node.xml" qualifiers="" type="layout"/><file name="activity_cubicle_back_panel_view_show" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_cubicle_back_panel_view_show.xml" qualifiers="" type="layout"/><file name="activity_cubicle_list" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_cubicle_list.xml" qualifiers="" type="layout"/><file name="activity_cubicle_view_show" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_cubicle_view_show.xml" qualifiers="" type="layout"/><file name="activity_cubicle_visual" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_cubicle_visual.xml" qualifiers="" type="layout"/><file name="activity_dev_files_show" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_dev_files_show.xml" qualifiers="" type="layout"/><file name="activity_gl" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_gl.xml" qualifiers="" type="layout"/><file name="activity_glwl_detail" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_glwl_detail.xml" qualifiers="" type="layout"/><file name="activity_glwl_label" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_glwl_label.xml" qualifiers="" type="layout"/><file name="activity_glwl_label_dlg" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_glwl_label_dlg.xml" qualifiers="" type="layout"/><file name="activity_gl_connect" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_gl_connect.xml" qualifiers="" type="layout"/><file name="activity_gq" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_gq.xml" qualifiers="" type="layout"/><file name="activity_gy_test_zooming" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_gy_test_zooming.xml" qualifiers="" type="layout"/><file name="activity_ieds_whole_circuit" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_ieds_whole_circuit.xml" qualifiers="" type="layout"/><file name="activity_ied_back_panel_view" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_ied_back_panel_view.xml" qualifiers="" type="layout"/><file name="activity_ied_visual" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_ied_visual.xml" qualifiers="" type="layout"/><file name="activity_ied_visual_new" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_ied_visual_new.xml" qualifiers="" type="layout"/><file name="activity_odf" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_odf.xml" qualifiers="" type="layout"/><file name="activity_odf_logic" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_odf_logic.xml" qualifiers="" type="layout"/><file name="activity_odf_view" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_odf_view.xml" qualifiers="" type="layout"/><file name="activity_optical_fiber_visual" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_optical_fiber_visual.xml" qualifiers="" type="layout"/><file name="activity_optical_info" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_optical_info.xml" qualifiers="" type="layout"/><file name="activity_plans_files_show" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_plans_files_show.xml" qualifiers="" type="layout"/><file name="activity_region_list" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_region_list.xml" qualifiers="" type="layout"/><file name="activity_second" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_second.xml" qualifiers="" type="layout"/><file name="activity_substation" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_substation.xml" qualifiers="" type="layout"/><file name="activity_switch_vlan_show" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_switch_vlan_show.xml" qualifiers="" type="layout"/><file name="activity_test" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_test.xml" qualifiers="" type="layout"/><file name="activity_top_bar_base" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_top_bar_base.xml" qualifiers="" type="layout"/><file name="activity_vir_ports_links" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_vir_ports_links.xml" qualifiers="" type="layout"/><file name="activity_vrcircle_view" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_vrcircle_view.xml" qualifiers="" type="layout"/><file name="activity_wl" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\activity_wl.xml" qualifiers="" type="layout"/><file name="content_vir_ports_links" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\content_vir_ports_links.xml" qualifiers="" type="layout"/><file name="device_backpanel_layout" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\device_backpanel_layout.xml" qualifiers="" type="layout"/><file name="device_backpanel_view_layout" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\device_backpanel_view_layout.xml" qualifiers="" type="layout"/><file name="device_portabout_frament" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\device_portabout_frament.xml" qualifiers="" type="layout"/><file name="fragment_odf_logic" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\fragment_odf_logic.xml" qualifiers="" type="layout"/><file name="fragment_odf_new_view" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\fragment_odf_new_view.xml" qualifiers="" type="layout"/><file name="fragment_odf_tier_new_view" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\fragment_odf_tier_new_view.xml" qualifiers="" type="layout"/><file name="fragment_odf_tier_new_view_include" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\fragment_odf_tier_new_view_include.xml" qualifiers="" type="layout"/><file name="fragment_odf_tier_view" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\fragment_odf_tier_view.xml" qualifiers="" type="layout"/><file name="fragment_odf_view" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\fragment_odf_view.xml" qualifiers="" type="layout"/><file name="gl_info" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\gl_info.xml" qualifiers="" type="layout"/><file name="gl_title" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\gl_title.xml" qualifiers="" type="layout"/><file name="gl_title_1" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\gl_title_1.xml" qualifiers="" type="layout"/><file name="gq_info" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\gq_info.xml" qualifiers="" type="layout"/><file name="layout_common_title" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\layout_common_title.xml" qualifiers="" type="layout"/><file name="layout_item_filepath" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\layout_item_filepath.xml" qualifiers="" type="layout"/><file name="layout_title" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\layout_title.xml" qualifiers="" type="layout"/><file name="layout_title_cable" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\layout_title_cable.xml" qualifiers="" type="layout"/><file name="list_header_cubicle" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\list_header_cubicle.xml" qualifiers="" type="layout"/><file name="list_item_cubicle" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\list_item_cubicle.xml" qualifiers="" type="layout"/><file name="list_item_cubicle_fill" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\list_item_cubicle_fill.xml" qualifiers="" type="layout"/><file name="list_item_cubicle_filld" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\list_item_cubicle_filld.xml" qualifiers="" type="layout"/><file name="list_item_view" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\list_item_view.xml" qualifiers="" type="layout"/><file name="list_item_vlantable" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\list_item_vlantable.xml" qualifiers="" type="layout"/><file name="my_camera" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\my_camera.xml" qualifiers="" type="layout"/><file name="odf_item" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\odf_item.xml" qualifiers="" type="layout"/><file name="popupview" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\popupview.xml" qualifiers="" type="layout"/><file name="port_ctrlbolck_layout" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\port_ctrlbolck_layout.xml" qualifiers="" type="layout"/><file name="port_ctrlbolck_virlink" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\port_ctrlbolck_virlink.xml" qualifiers="" type="layout"/><file name="port_odf_link_item" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\port_odf_link_item.xml" qualifiers="" type="layout"/><file name="qr_code_info_item" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\qr_code_info_item.xml" qualifiers="" type="layout"/><file name="qr_code_info_item_gb" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\qr_code_info_item_gb.xml" qualifiers="" type="layout"/><file name="qr_code_info_layout" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\qr_code_info_layout.xml" qualifiers="" type="layout"/><file name="sample_cubicle_view" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\sample_cubicle_view.xml" qualifiers="" type="layout"/><file name="spcd_activity_main" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\spcd_activity_main.xml" qualifiers="" type="layout"/><file name="td_fragment2" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\td_fragment2.xml" qualifiers="" type="layout"/><file name="toolbar" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\toolbar.xml" qualifiers="" type="layout"/><file name="vr_circuit_layout" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\layout\vr_circuit_layout.xml" qualifiers="" type="layout"/><file name="toobar" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\menu\toobar.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="pic_arrow_down" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-hdpi\pic_arrow_down.png" qualifiers="hdpi-v4" type="mipmap"/><file name="pic_arrow_down_1" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-hdpi\pic_arrow_down_1.png" qualifiers="hdpi-v4" type="mipmap"/><file name="pic_left_arrow" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-hdpi\pic_left_arrow.png" qualifiers="hdpi-v4" type="mipmap"/><file name="pic_more" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-hdpi\pic_more.png" qualifiers="hdpi-v4" type="mipmap"/><file name="pic_photo" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-hdpi\pic_photo.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="indicator_input_error" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-xxhdpi\indicator_input_error.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="auto_substation" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\raw\auto_substation.spcd" qualifiers="" type="raw"/><file name="spcd_20190521" path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\raw\spcd_20190521.spcd" qualifiers="" type="raw"/><file path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\values\attrs_cubicle_view.xml" qualifiers=""><declare-styleable name="CubicleView">
        <attr format="string" name="exampleString"/>
        <attr format="dimension" name="exampleDimension"/>
        <attr format="color" name="exampleColor"/>
        <attr format="color|reference" name="exampleDrawable"/>
    </declare-styleable></file><file path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\values\colors.xml" qualifiers=""><color name="colorPrimary">#3F51B5</color><color name="colorPrimaryDark">#303F9F</color><color name="colorAccent">#FF4081</color><color name="white">#ffffff</color><color name="ivory">#fffff0</color><color name="lightyellow">#ffffe0</color><color name="yellow">#ffff00</color><color name="snow">#fffafa</color><color name="floralwhite">#fffaf0</color><color name="lemonchiffon">#fffacd</color><color name="cornsilk">#fff8dc</color><color name="seaShell">#fff5ee</color><color name="lavenderblush">#fff0f5</color><color name="papayawhip">#ffefd5</color><color name="blanchedalmond">#ffebcd</color><color name="mistyrose">#ffe4e1</color><color name="bisque">#ffe4c4</color><color name="moccasin">#ffe4b5</color><color name="navajowhite">#ffdead</color><color name="peachpuff">#ffdab9</color><color name="gold">#ffd700</color><color name="pink">#ffc0cb</color><color name="lightpink">#ffb6c1</color><color name="orange">#ffa500</color><color name="lightsalmon">#ffa07a</color><color name="darkorange">#ff8c00</color><color name="coral">#ff7f50</color><color name="hotpink">#ff69b4</color><color name="tomato">#ff6347</color><color name="orangered">#ff4500</color><color name="deeppink">#ff1493</color><color name="fuchsia">#ff00ff</color><color name="magenta">#ff00ff</color><color name="red">#ff0000</color><color name="oldlace">#fdf5e6</color><color name="lightgoldenrodyellow">#fafad2</color><color name="linen">#faf0e6</color><color name="antiquewhite">#faebd7</color><color name="salmon">#fa8072</color><color name="ghostwhite">#f8f8ff</color><color name="mintcream">#f5fffa</color><color name="whitesmoke">#f5f5f5</color><color name="beige">#f5f5dc</color><color name="wheat">#f5deb3</color><color name="sandybrown">#f4a460</color><color name="azure">#f0ffff</color><color name="honeydew">#f0fff0</color><color name="aliceblue">#f0f8ff</color><color name="khaki">#f0e68c</color><color name="lightcoral">#f08080</color><color name="palegoldenrod">#eee8aa</color><color name="violet">#ee82ee</color><color name="darksalmon">#e9967a</color><color name="lavender">#e6e6fa</color><color name="lightcyan">#e0ffff</color><color name="burlywood">#deb887</color><color name="plum">#dda0dd</color><color name="gainsboro">#dcdcdc</color><color name="crimson">#dc143c</color><color name="palevioletred">#db7093</color><color name="goldenrod">#daa520</color><color name="orchid">#da70d6</color><color name="thistle">#d8bfd8</color><color name="lightgray">#d3d3d3</color><color name="lightgrey">#d3d3d3</color><color name="tan">#d2b48c</color><color name="chocolate">#d2691e</color><color name="peru">#cd853f</color><color name="indianred">#cd5c5c</color><color name="mediumvioletred">#c71585</color><color name="silver">#c0c0c0</color><color name="darkkhaki">#bdb76b</color><color name="rosybrown">#bc8f8f</color><color name="mediumorchid">#ba55d3</color><color name="darkgoldenrod">#b8860b</color><color name="firebrick">#b22222</color><color name="powderblue">#b0e0e6</color><color name="lightsteelblue">#b0c4de</color><color name="paleturquoise">#afeeee</color><color name="greenyellow">#adff2f</color><color name="lightblue">#add8e6</color><color name="darkgray">#a9a9a9</color><color name="darkgrey">#a9a9a9</color><color name="brown">#a52a2a</color><color name="sienna">#a0522d</color><color name="darkorchid">#9932cc</color><color name="palegreen">#98fb98</color><color name="darkviolet">#9400d3</color><color name="mediumpurple">#9370db</color><color name="lightgreen">#90ee90</color><color name="darkseagreen">#8fbc8f</color><color name="saddlebrown">#8b4513</color><color name="darkmagenta">#8b008b</color><color name="darkred">#8b0000</color><color name="blueviolet">#8a2be2</color><color name="lightskyblue">#87cefa</color><color name="skyblue">#87ceeb</color><color name="gray">#808080</color><color name="grey">#808080</color><color name="olive">#808000</color><color name="purple">#800080</color><color name="maroon">#800000</color><color name="aquamarine">#7fffd4</color><color name="chartreuse">#7fff00</color><color name="lawngreen">#7cfc00</color><color name="mediumslateblue">#7b68ee</color><color name="lightslategray">#778899</color><color name="lightslategrey">#778899</color><color name="slategray">#708090</color><color name="olivedrab">#6b8e23</color><color name="slateblue">#6a5acd</color><color name="dimgray">#696969</color><color name="dimgrey">#696969</color><color name="mediumaquamarine">#66cdaa</color><color name="cornflowerblue">#6495ed</color><color name="cadetblue">#5f9ea0</color><color name="darkolivegreen">#556b2f</color><color name="indigo">#4b0082</color><color name="mediumturquoise">#48d1cc</color><color name="darkslateblue">#483d8b</color><color name="steelblue">#4682b4</color><color name="royalblue">#4169e1</color><color name="turquoise">#40e0d0</color><color name="mediumseagreen">#3cb371</color><color name="limegreen">#32cd32</color><color name="darkslategray">#2f4f4f</color><color name="darkslategrey">#2f4f4f</color><color name="seagreen">#2e8b57</color><color name="forestgreen">#228b22</color><color name="lightseagreen">#20b2aa</color><color name="dodgerblue">#1e90ff</color><color name="midnightblue">#191970</color><color name="aqua">#00ffff</color><color name="cyan">#00ffff</color><color name="springgreen">#00ff7f</color><color name="lime">#00ff00</color><color name="mediumspringgreen">#00fa9a</color><color name="darkturquoise">#00ced1</color><color name="deepskyblue">#00bfff</color><color name="darkcyan">#008b8b</color><color name="teal">#008080</color><color name="green">#008000</color><color name="darkgreen">#006400</color><color name="blue">#0000ff</color><color name="mediumblue">#0000cd</color><color name="darkblue">#00008b</color><color name="navy">#000080</color><color name="black">#000000</color><color name="col1">#43BAD2</color><color name="col2">#AFDFE4</color><color name="col3">#d238be</color><color name="col4">#f9f3d3</color><color name="PrimaryColor">#2196F3</color><color name="PrimaryDarkColor">#1976D2</color><color name="color_00000000">#00000000</color><color name="deep_gray">#6C778A</color></file><file path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\values\dimens.xml" qualifiers=""><dimen name="enter_detaile_width">35dp</dimen><dimen name="enter_detaile_height">30dp</dimen><dimen name="sp_text_header">13sp</dimen><dimen name="sp_text1">18sp</dimen><dimen name="sp_text2">15sp</dimen><dimen name="fab_margin">16dp</dimen></file><file path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">SpcdVisualAndroidApp</string><string name="title_activity_cubicle_back_panel_view_show">CubicleBackPanelViewShowActivity
    </string><string name="hello_blank_fragment">Hello blank fragment</string><string name="title_activity_vir_ports_links">VirPortsLinksActivity</string><string name="title_activity_top_bar_base">TopBarBaseActivity</string><string name="str_opposite_dev">对侧装置</string><string name="str_odf_view">ODF视图</string><string name="str_phy_whole_cir_view">物理全回路</string><string name="str_tag_view">光纤标签图</string><string-array name="letter">
        <item>A</item>
        <item>B</item>
        <item>C</item>
        <item>D</item>
        <item>E</item>
        <item>F</item>
        <item>G</item>
    </string-array></file><file path="D:\svn\DeviceVisualAS\SpcdVisual\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style><style name="AppThemeLNoAction" parent="Theme.AppCompat.Light.NoActionBar"/><style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/><style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/><style name="style_user_seldia" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        
        <item name="android:windowIsFloating">true</item>
        
        <item name="android:windowIsTranslucent">true</item>
        
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/color_00000000</item>
        <item name="android:backgroundDimEnabled">true</item>
        
    </style><style name="AppTheme.ActionBar" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="actionMenuTextColor">@drawable/text_color_selector_menu</item>
    </style><style name="activityTheme" parent="AppTheme">
        <item name="android:windowIsTranslucent">true</item>
    </style><style name="AnimationUpPopup">
        <item name="android:windowEnterAnimation">@anim/grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/shrink_fade_out_from_bottom</item>
    </style><style name="AnimationFromButtom">
        <item name="android:windowEnterAnimation">@anim/popup_form_bottom</item>
        <item name="android:windowExitAnimation">@anim/drop_down_to_bottom</item>
    </style><style name="AnimationFromTop">
        <item name="android:windowEnterAnimation">@anim/drop_down_from_top</item>
        <item name="android:windowExitAnimation">@anim/hide_to_top</item>
    </style><style name="DialogStyleInActivity" parent="AppTheme.NoActionBar">
        <item name="android:windowBackground">@android:color/white</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style></file></source><source path="D:\svn\DeviceVisualAS\SpcdVisual\build\generated\res\rs\release"/><source path="D:\svn\DeviceVisualAS\SpcdVisual\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\svn\DeviceVisualAS\SpcdVisual\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\svn\DeviceVisualAS\SpcdVisual\src\release\res"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="CubicleView">
        <attr format="string" name="exampleString"/>
        <attr format="dimension" name="exampleDimension"/>
        <attr format="color" name="exampleColor"/>
        <attr format="color|reference" name="exampleDrawable"/>
    </declare-styleable></configuration></mergedItems></merger>