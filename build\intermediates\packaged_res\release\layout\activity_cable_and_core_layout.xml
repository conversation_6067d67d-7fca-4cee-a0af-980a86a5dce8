<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.CableAndCoreListActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"/>

    <include
        android:id="@+id/toolbar1"
        layout="@layout/layout_title_cable"/>

    <include
        android:id="@+id/header_layout"
        layout="@layout/list_header_cubicle"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/toolbar1" />

    <ListView
        android:id="@+id/list_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header_layout" />

</android.support.constraint.ConstraintLayout>