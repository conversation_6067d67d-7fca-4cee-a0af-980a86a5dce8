<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.kemov.visual.spcd.spcdvisualandroidapp.activity.CubicleViewShowActivity">
    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content" />

    <com.kemov.visual.spcd.spcdvisualandroidapp.visualview.CubicleBackPanelView
        android:id="@+id/cubicleBackPanelView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/toolbar"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentStart="true"/>

</RelativeLayout>
