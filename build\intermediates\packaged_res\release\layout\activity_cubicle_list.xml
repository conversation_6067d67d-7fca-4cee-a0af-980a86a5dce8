<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.cubicle.CubicleListActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"/>
    <include
        android:id="@+id/header_layout"
        layout="@layout/list_header_cubicle"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <ListView
        android:id="@+id/list_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="30dp"
        app:layout_constraintTop_toBottomOf="@+id/header_layout" />

</android.support.constraint.ConstraintLayout>