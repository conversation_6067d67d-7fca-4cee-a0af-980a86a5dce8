<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.kemov.visual.spcd.spcdvisualandroidapp.activity.CubicleViewShowActivity">
    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"/>

    <include
        android:id="@+id/toolbar2"
        layout="@layout/layout_title"/>

    <!-- 正面图-->
    <com.kemov.visual.spcd.spcdvisualandroidapp.visualview.CubicleFrontView
        android:id="@+id/cubicleFrontView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/toolbar2"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentStart="true" />
    <!-- 背面图-->
    <com.kemov.visual.spcd.spcdvisualandroidapp.visualview.CubicleBackPanelView
        android:id="@+id/cubicleBackPanelView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/toolbar2"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentStart="true"
        android:visibility="gone"/>
    <include
        android:id="@+id/qr_code_info"
        layout="@layout/qr_code_info_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:visibility="gone"/>

    <LinearLayout
        android:id="@+id/ll_zoom_bar"
        android:layout_width="40dp"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar2"
        android:layout_alignParentRight="true"
        android:visibility="gone"
        android:background="@android:color/transparent"
        android:orientation="vertical">

        <View
            android:layout_width="0dp"
            android:layout_height="10dp"
            android:layout_weight="10"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapBigger0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_bigger"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapSmaller0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_smaller"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapOriginal0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_original_size_w" />

        <ImageView
            android:id="@+id/mapOriginalFilled"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_filled_size_h" />

        <View
            android:layout_width="0dp"
            android:layout_height="10dp"
            android:layout_weight="0"
            android:visibility="invisible" />
    </LinearLayout>
</RelativeLayout>
