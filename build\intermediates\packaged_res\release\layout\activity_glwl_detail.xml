<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.GlwlDetailActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/region"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="30dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:text="当前小室："/>

    <TextView
        android:id="@+id/region_detail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@+id/region"
        app:layout_constraintTop_toTopOf="@+id/region"
        android:textSize="16sp"
        android:layout_marginLeft="10dp"
        android:textColor="@color/deep_gray"
        android:text=""/>

    <TextView
        android:id="@+id/cubicle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@+id/region"
        app:layout_constraintTop_toBottomOf="@+id/region"
        android:layout_marginTop="30dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:text="当前屏柜："/>

    <TextView
        android:id="@+id/cubicle_detail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@+id/cubicle"
        app:layout_constraintTop_toTopOf="@+id/cubicle"
        android:textSize="16sp"
        android:layout_marginLeft="10dp"
        android:textColor="@color/deep_gray"
        android:text=""/>

    <TextView
        android:id="@+id/region_other"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@+id/region"
        app:layout_constraintTop_toBottomOf="@+id/cubicle"
        android:layout_marginTop="30dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:text="对侧小室："/>

    <TextView
        android:id="@+id/region_other_detail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@+id/region_other"
        app:layout_constraintTop_toTopOf="@+id/region_other"
        android:textSize="16sp"
        android:layout_marginLeft="10dp"
        android:textColor="@color/deep_gray"
        android:text=""/>

    <TextView
        android:id="@+id/cubicle_other"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@+id/region"
        app:layout_constraintTop_toBottomOf="@+id/region_other"
        android:layout_marginTop="30dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:text="对侧屏柜："/>

    <TextView
        android:id="@+id/cubicle_other_detail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@+id/cubicle_other"
        app:layout_constraintTop_toTopOf="@+id/cubicle_other"
        android:textSize="16sp"
        android:layout_marginLeft="10dp"
        android:textColor="@color/deep_gray"
        android:text=""/>

</android.support.constraint.ConstraintLayout>