<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_add_src_by_others"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    android:gravity="center"
    android:layout_gravity="center"
    tools:context="com.share.mycustomviewlib.bean.VirPortsLinksDlgStyleActivity">

    <include
        android:visibility="gone"
        layout="@layout/layout_common_title"/>
    <TextView
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:gravity="center"
        android:background="@color/back_gray"
        android:textStyle="bold"
        android:textSize="24sp"
        android:text="标签图"/>
    <com.kemov.visual.spcd.spcdvisualandroidapp.visualview.GlwlLabelView
        android:layout_width="800dp"
        android:layout_height="600dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true" />

    <LinearLayout
        android:id="@+id/ll_zoom_bar"
        android:layout_width="40dp"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar2"
        android:layout_alignParentRight="true"
        android:visibility="gone"
        android:background="@android:color/transparent"
        android:orientation="vertical">

        <View
            android:layout_width="0dp"
            android:layout_height="10dp"
            android:layout_weight="10"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapBigger0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_bigger"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapSmaller0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_smaller"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapOriginal0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_original_size" />

        <ImageView
            android:id="@+id/mapOriginalFilled"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_filled_size" />

        <View
            android:layout_width="0dp"
            android:layout_height="10dp"
            android:layout_weight="0"
            android:visibility="invisible" />
    </LinearLayout>

</FrameLayout>