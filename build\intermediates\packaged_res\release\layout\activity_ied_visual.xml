<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.IEDVisual.IEDVisualActivity">
    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar"
        android:visibility="gone"/>

    <include
        android:id="@+id/toolbar2"
        layout="@layout/layout_title"/>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:id="@+id/viewGroup"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">
            <com.kemov.visual.spcd.spcdvisualandroidapp.visualview.DeviceBackPanelView
                android:id="@+id/device_back_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_alignParentEnd="true"
                android:layout_alignParentStart="true"/>
            <com.kemov.visual.spcd.spcdvisualandroidapp.visualview.VirtalRealCircuitView_v2
                android:id="@+id/virtal_real_circuit_view_v2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignParentBottom="true"
                android:layout_alignParentEnd="true"
                android:layout_alignParentStart="true"
                android:visibility="gone"/>

            <LinearLayout
                android:id="@+id/ll_zoom_bar"
                android:layout_width="40dp"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:visibility="visible"
                android:background="@android:color/transparent"
                android:orientation="vertical">

                <View
                    android:layout_width="0dp"
                    android:layout_height="10dp"
                    android:layout_weight="10"
                    android:visibility="invisible" />

                <ImageView
                    android:id="@+id/mapBigger0"
                    android:layout_width="fill_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:src="@drawable/map_bigger"
                    android:visibility="invisible" />

                <ImageView
                    android:id="@+id/mapSmaller0"
                    android:layout_width="fill_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:src="@drawable/map_smaller"
                    android:visibility="invisible" />

                <ImageView
                    android:id="@+id/mapOriginal0"
                    android:layout_width="fill_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:src="@drawable/map_original_size_w" />

                <ImageView
                    android:id="@+id/mapOriginalFilled"
                    android:layout_width="fill_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:src="@drawable/map_filled_size_h" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="10dp"
                    android:layout_weight="0"
                    android:visibility="invisible" />
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:visibility="gone">
            <ListView
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
            </ListView>
        </LinearLayout>
    </LinearLayout>



</RelativeLayout>
