<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:orientation="vertical">
<!--    tools:context=".activity.optical_fiber_visual.OpticalFiberVisualActivity">-->
    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar"
        android:visibility="gone"/>

    <include
        android:id="@+id/toolbar2"
        layout="@layout/layout_title"/>

    <!-- 可以在程序中根据toolbar、抽屉菜单、自定义标签页等切换Fragment -->
    <FrameLayout
        android:id="@+id/fragment_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    </FrameLayout>
</LinearLayout>
