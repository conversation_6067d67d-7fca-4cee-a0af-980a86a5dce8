<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.IEDVisual.switch_vlan.SwitchVlanShowActivity">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:id="@+id/ll"
            android:orientation="vertical">
            <!--头标-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:background="@drawable/border"
                    android:text="序号"/>
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:gravity="center"
                    android:background="@drawable/border"
                    android:text="VLAN-ID(十进制)"/>
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="4"
                    android:gravity="center"
                    android:background="@drawable/border"
                    android:text="交换机端口"/>

            </LinearLayout>
            <ListView
                android:id="@+id/lv_vlan_table"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>

        </LinearLayout>
    </LinearLayout>

</LinearLayout>