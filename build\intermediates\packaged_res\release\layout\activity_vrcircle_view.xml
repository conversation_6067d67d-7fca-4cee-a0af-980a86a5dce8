<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.VRCircleViewActivity">

    <com.kemov.visual.spcd.spcdvisualandroidapp.visualview.VirtalRealCircuitView
        android:id="@+id/virRealCircuitView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"/>
    <com.kemov.visual.spcd.spcdvisualandroidapp.visualview.VirtalRealCircuitView_v2
        android:id="@+id/virRealCircuitView_v2"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
</android.support.constraint.ConstraintLayout>