<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.kemov.visual.spcd.spcdvisualandroidapp.activity.CubicleViewShowActivity">

    <include
        android:id="@+id/device_back"
        layout="@layout/device_backpanel_view_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true" />

    <include
        android:id="@+id/qr_code_info"
        layout="@layout/qr_code_info_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:visibility="gone"/>
    <!--一次出差何工告知的需求，增加控制块、经过的odf信息，废弃-ToEnd！-->
    <TextView
        android:id="@+id/draggableTV"
        style="@style/FirHeaderTVStyle"
        android:layout_width="fill_parent"
        android:layout_height="25dp"
        android:background="@color/silver"
        android:gravity="left"
        android:visibility="gone"
        android:text="端口相关信息（可拖动）" />
    <include
        android:id="@+id/port_info_list"
        layout="@layout/device_portabout_frament"
        android:layout_width="match_parent"
        android:visibility="gone"
        android:layout_height="0dp"
        android:layout_weight="1"/>
</RelativeLayout>
