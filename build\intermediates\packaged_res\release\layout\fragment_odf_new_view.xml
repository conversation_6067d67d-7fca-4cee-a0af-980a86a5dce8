<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:layout_marginLeft="15dp"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:textColor="@color/black"
            android:textSize="15sp"
            android:gravity="center_vertical"
            android:text="选择层级："/>

        <Spinner
            android:id="@+id/spinner"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:dropDownVerticalOffset="30dp"
            android:entries="@array/letter"/>

        <ImageView
            android:id="@+id/otherOdf"
            android:layout_width="18dp"
            android:layout_height="wrap_content"
            android:src="@mipmap/pic_arrow_down_1"
            android:gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:visibility="gone"/>
    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/gray"/>

    <com.kemov.visual.spcd.spcdvisualandroidapp.visualview.OdfNewFontView
        android:id="@+id/odfView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <LinearLayout
        android:id="@+id/ll_zoom_bar"
        android:layout_width="40dp"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:visibility="gone"
        android:background="@android:color/transparent"
        android:orientation="vertical">

        <View
            android:layout_width="0dp"
            android:layout_height="10dp"
            android:layout_weight="10"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapBigger0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_bigger"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapSmaller0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_smaller"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapOriginal0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_original_size" />

        <ImageView
            android:id="@+id/mapOriginalFilled"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_filled_size" />

        <View
            android:layout_width="0dp"
            android:layout_height="10dp"
            android:layout_weight="0"
            android:visibility="invisible" />
    </LinearLayout>
</LinearLayout>