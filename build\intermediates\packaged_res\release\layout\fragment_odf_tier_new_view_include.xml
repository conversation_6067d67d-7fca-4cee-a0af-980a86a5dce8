<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/title_lln"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="15dp"
        android:paddingLeft="15dp"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:textColor="@color/black"
                android:gravity="center_vertical"
                android:textSize="15sp"
                android:text="所属屏柜："/>
            <TextView
                android:id="@+id/cubicleName"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:textColor="@color/black"
                android:textSize="15sp"
                android:gravity="center_vertical"
                android:text="ODF名称："/>
            <TextView
                android:id="@+id/odfName"
                android:gravity="center_vertical"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:visibility="gone"/>

            <Spinner
                android:id="@+id/spinner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:dropDownVerticalOffset="30dp"
                android:entries="@array/letter"/>

            <ImageView
                android:id="@+id/otherOdf"
                android:layout_width="18dp"
                android:layout_height="wrap_content"
                android:src="@mipmap/pic_arrow_down_1"
                android:gravity="center_vertical"
                android:layout_marginLeft="20dp"
                android:visibility="gone"/>
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/device"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_below="@+id/title_lln"
        android:background="@color/gray"/>

    <com.kemov.visual.spcd.spcdvisualandroidapp.visualview.OdfTierNewView
        android:id="@+id/odfView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/device"/>

    <LinearLayout
        android:id="@+id/ll_zoom_bar"
        android:layout_width="40dp"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:visibility="visible"
        android:background="@android:color/transparent"
        android:orientation="vertical">

        <View
            android:layout_width="0dp"
            android:layout_height="10dp"
            android:layout_weight="10"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapBigger0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_bigger"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapSmaller0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_smaller"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapOriginal0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_original_size_w" />

        <ImageView
            android:id="@+id/mapOriginalFilled"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_filled_size_h" />

        <View
            android:layout_width="0dp"
            android:layout_height="10dp"
            android:layout_weight="0"
            android:visibility="invisible" />
    </LinearLayout>
</RelativeLayout>