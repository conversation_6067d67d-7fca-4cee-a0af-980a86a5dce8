<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="35dp"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="horizontal">
        <TextView
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="@color/gray"/>
        <TextView
            android:id="@+id/num"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="@dimen/gl_text"
            android:textColor="@color/black"
            android:text="纤芯编号"/>

        <TextView
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="@color/gray"/>

        <TextView
            android:id="@+id/opticalStartUnitName"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="@dimen/gl_text"
            android:textColor="@color/black"
            android:text="光纤起点单元名称"/>

        <TextView
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="@color/gray"/>

        <TextView
            android:id="@+id/opticalStartPort"
            android:layout_width="0dp"
            android:layout_weight="1.8"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="@dimen/gl_text"
            android:textColor="@color/black"
            android:text="光纤起点端口"/>

        <TextView
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="@color/gray"/>

        <TextView
            android:id="@+id/opticalEndUnitName"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="@dimen/gl_text"
            android:textColor="@color/black"
            android:text="光纤终点单元名称"/>

        <TextView
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="@color/gray"/>

        <TextView
            android:id="@+id/opticalEndPort"
            android:layout_width="0dp"
            android:layout_weight="1.8"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="@dimen/gl_text"
            android:textColor="@color/black"
            android:text="光纤终点端口"/>

        <TextView
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="@color/gray"/>

        <TextView
            android:id="@+id/isCandidate"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="@dimen/gl_text"
            android:textColor="@color/black"
            android:text="是否备用"/>

        <TextView
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="@color/gray"/>
    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/gray"/>
</LinearLayout>