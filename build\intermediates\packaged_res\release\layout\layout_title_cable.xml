<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/title_rel"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@color/dodgerblue"
    android:gravity="center">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal">
            <ImageButton
                android:layout_centerVertical="true"
                android:id="@+id/left_back"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:clickable="true"
                android:padding="10dp"
                android:layout_alignParentLeft="true"
                android:background="@null"
                android:src="@mipmap/pic_left_arrow" />
            <TextView
                android:id="@+id/title_topLevel"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:layout_toRightOf="@+id/left_back"
                android:singleLine="true"
                android:paddingLeft="5dp"
                android:textColor="@color/text_white"
                android:textSize="14sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp"
            android:layout_centerInParent="true"
            android:singleLine="true"
            android:padding="6dp"
            android:gravity="center"
            android:textColor="@color/text_white"
            android:textSize="18sp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/tv_left"
                android:layout_width="45dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:text=""
                android:visibility="gone"
                android:background="@color/PrimaryDarkColor"/>
            <TextView
                android:id="@+id/tv_center"
                android:layout_width="45dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:text=""
                android:visibility="gone"/>
            <TextView
                android:id="@+id/tv_right"
                android:layout_width="45dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:text=""
                android:visibility="gone"/>
            <TextView
                android:id="@+id/export"
                android:layout_width="45dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:text="导出"/>
        </LinearLayout>

</RelativeLayout>