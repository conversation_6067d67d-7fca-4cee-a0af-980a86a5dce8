<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="#ffffff">
        <TextView
            android:id="@+id/num"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="12" />

        <TextView
            android:id="@+id/name"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="5dp"
            android:textSize="@dimen/sp_text1"
            android:text="名称"/>

        <TextView
            android:id="@+id/desc"
            android:layout_width="0dp"
            android:layout_weight="4"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="5dp"
            android:textSize="@dimen/sp_text1"
            android:text="描述"/>

        <Button
            android:id="@+id/detail_btn"
            android:layout_width="@dimen/enter_detaile_width"
            android:layout_height="@dimen/enter_detaile_height"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:ellipsize="end"
            android:gravity="center"
            android:textSize="@dimen/sp_text1"
            android:background="@drawable/ic_enter2"
            android:visibility="gone"/>
    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/color_dad9db"/>
</LinearLayout>