<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:background="#ffffff"
    >

    <TextView
        android:id="@+id/tv_num"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:text="序号" />

    <TextView
        android:id="@+id/tv_vlan_id"
        android:layout_width="0dp"
        android:layout_weight="2"
        android:layout_height="match_parent"
        android:gravity="center"
        android:padding="5dp"
        android:textSize="@dimen/sp_text1"
        android:text="vlan-id"/>

    <TextView
        android:id="@+id/tv_switch_port"
        android:layout_width="0dp"
        android:layout_weight="4"
        android:layout_height="match_parent"
        android:gravity="center"
        android:padding="5dp"
        android:textSize="@dimen/sp_text1"
        android:text="交换机端口"/>

    <Button
        android:id="@+id/detail_btn"
        android:layout_width="@dimen/enter_detaile_width"
        android:layout_height="@dimen/enter_detaile_height"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:ellipsize="end"
        android:gravity="center"
        android:textSize="@dimen/sp_text1"
        android:background="@drawable/ic_enter2"
        android:visibility="gone"/>
</LinearLayout>