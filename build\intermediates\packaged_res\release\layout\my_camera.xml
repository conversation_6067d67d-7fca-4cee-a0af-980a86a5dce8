<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent" >

    <SurfaceView
        android:id="@+id/preview_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <com.uuzuche.lib_zxing.view.ViewfinderView
        android:id="@+id/viewfinder_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:inner_width="300dp"
        app:inner_height="300dp"
        app:inner_margintop="200dp"
        app:inner_corner_length="30dp"
        app:inner_corner_width="5dp"
        app:inner_scan_speed="10"
        app:inner_scan_iscircle="false"/>
    <!--app:inner_corner_color="@color/fragment_title_back"-->
    <!--app:inner_scan_bitmap="@mipmap/qr"-->
</FrameLayout>