<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:orientation="vertical"
              android:background="@android:color/holo_green_light"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content">
    <Button
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_opposite_dev"
        android:id="@+id/btn_jumpToTheOtherDevice"/>
    <!--当端口经过ODF则显示-->
    <Button
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_odf_view"
        android:id="@+id/btn_jumpToODF"/>
    <Button
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_phy_whole_cir_view"
        android:id="@+id/btn_jumpToTheWholeCircle"/>
</LinearLayout>