<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:id="@+id/tv_index"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:padding="10dp"
        android:gravity="center"
        android:text="1"/>
    <TextView
        android:id="@+id/tv_FCDA"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:padding="10dp"
        android:gravity="center"
        android:text="跳闸信号123"/>
    <TextView
        android:id="@+id/tv_div"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:padding="10dp"
        android:gravity="center"
        android:text="--"/>
    <TextView
        android:id="@+id/tv_extRef"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:padding="10dp"
        android:gravity="center"
        android:text="跳闸位置123"/>
</LinearLayout>