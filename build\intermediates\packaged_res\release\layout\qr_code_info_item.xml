<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_below="@id/tv_tips"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="10dp"
    android:background="#FFFFFFFF">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="22">
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1">
            <TextView
                android:id="@+id/tv_cable_name"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="5"
                android:gravity="center"
                android:text="1SS-G101A"/>
            <TextView
                android:id="@+id/tv_direction1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="S"/>
            <TextView
                android:id="@+id/tv_start_ied_name"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="8"
                android:gravity="center"
                android:text="330大保当1线保护一"/>
            <TextView
                android:id="@+id/tv_signal_type1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:gravity="center"
                android:text="GS"/>
            <TextView
                android:id="@+id/tv_port_info1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="5"
                android:gravity="center"
                android:text="B07_03_2Rx"/>
        </LinearLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1">
            <TextView
                android:id="@+id/tv_cable_num_core_no"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="5"
                android:gravity="center"
                android:text="1SS-G101A"/>
            <TextView
                android:id="@+id/tv_direction2"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="E"/>
            <TextView
                android:id="@+id/tv_end_ied_name"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="8"
                android:gravity="center"
                android:text="330大保当1线保护一"/>
            <TextView
                android:id="@+id/tv_signal_type2"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:gravity="center"
                android:text="GS"/>
            <!--两装置之间经过ODF则为熔接端口,直连则为对侧端口-->
            <TextView
                android:id="@+id/tv_port_info2"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="5"
                android:gravity="center"
                android:text="H_A01_Rx"/>
        </LinearLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/img_qr_code"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0"
        android:gravity="center"
        android:src="@drawable/fivestar_yelow"/>

</LinearLayout>
