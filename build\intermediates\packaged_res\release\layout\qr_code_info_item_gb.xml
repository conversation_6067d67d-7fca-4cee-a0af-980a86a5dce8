<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_below="@id/tv_tips"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"

    android:background="#FFFFFFFF">
    <LinearLayout
        android:padding="10dp"
        android:orientation="vertical"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="3">
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="left"
                android:text="No: "/>
            <TextView
                android:id="@+id/tv_tag_gb_no"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="left"
                android:text="TL22117311A-2"/>/>
        </LinearLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="left"
                android:text="Fr: "/>
            <TextView
                android:id="@+id/tv_tag_gb_fr"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="left"
                android:text="11C/11n/1/5Tx"/>/>
        </LinearLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="left"
                android:text="To: "/>
            <TextView
                android:id="@+id/tv_tag_gb_to"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="left"
                android:text="3C/12n/1/1Rx"/>/>
        </LinearLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/img_qr_code"
        android:padding="3dp"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="2"
        android:layout_gravity="start"
        android:src="@drawable/fivestar_yelow"/>
    <!--<View
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="2"/>-->

</LinearLayout>
