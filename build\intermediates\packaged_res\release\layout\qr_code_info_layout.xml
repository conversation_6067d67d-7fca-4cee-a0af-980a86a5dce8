<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/back_gray"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:id="@+id/tagInfoTitleBar"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#ffc0c0c0">
        <TextView
            android:id="@+id/tv_tips"
            android:layout_weight="4"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="left|center_vertical"
            android:padding="2dp"
            android:textStyle="bold"
            android:text=" 端口光纤标签信息"/>
        <TextView
            android:id="@+id/btn_jumpToOptFiberViewDlg"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textStyle="bold"
            android:layout_gravity="center"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="50dp"
            android:textColor="@drawable/taginfo_textcolor_selector"
            android:text="@string/str_odf_view"
            />
        <ImageView
            android:id="@+id/btn_closeTagInfo"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginRight="5dp"
            app:srcCompat="@drawable/ic_svg_close"/>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/ll_attached_features"
        android:layout_below="@id/tagInfoTitleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="horizontal">
        <!--<Button
            android:id="@+id/btn_opposite_dev"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_opposite_dev"/>
        <Button
            android:id="@+id/btn_odf_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_odf_view"/>
        <Button
            android:id="@+id/btn_phy_whole_cir_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_phy_whole_cir_view"/>-->
    </LinearLayout>

    <ListView
        android:id="@+id/qr_code_listView"
        android:layout_below="@id/ll_attached_features"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
</RelativeLayout>
