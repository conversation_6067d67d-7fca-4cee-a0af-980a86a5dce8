<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.kemov.visual.spcd.spcdvisualandroidapp.MainActivity">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Hello World!"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <Button
        android:id="@+id/btnOpenCubicleView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="打开屏柜视图"
        tools:layout_editor_absoluteX="8dp"
        tools:layout_editor_absoluteY="29dp"
        tools:text="打开屏柜视图"/>
    <Button
        android:id="@+id/btnOpenCubicleBackPanelView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="打开屏柜后背板视图"
        tools:layout_editor_absoluteX="4dp"
        tools:text="打开屏柜后背板视图"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/btnOpenCubicleView"/>

    <Button
        android:id="@+id/btnOpenIEDsWholeCircuitView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="打开全回路图"
        app:layout_constraintTop_toBottomOf="@+id/btnOpenCubicleBackPanelView"
        tools:layout_editor_absoluteX="4dp"
        tools:text="打开全回路图" />
    <Button
        android:id="@+id/btnIedBackView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="装置背板图"
        app:layout_constraintTop_toBottomOf="@+id/btnOpenIEDsWholeCircuitView"
        tools:layout_editor_absoluteX="4dp"
        tools:text="打开装置背板图" />
    <Button
        android:id="@+id/btnVRview"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="虚实回路图"
        app:layout_constraintTop_toBottomOf="@+id/btnIedBackView"
        tools:layout_editor_absoluteX="4dp"
        tools:text="虚实回路图" />
    <Button
        android:id="@+id/btnGyTestZoomingView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="GyTestZoomingView"
        app:layout_constraintTop_toBottomOf="@+id/btnVRview"
        tools:layout_editor_absoluteX="4dp"
        tools:text="GyTestZoomingView" />

</android.support.constraint.ConstraintLayout>
