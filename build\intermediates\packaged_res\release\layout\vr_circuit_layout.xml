<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.kemov.visual.spcd.spcdvisualandroidapp.activity.CubicleViewShowActivity">

    <com.kemov.visual.spcd.spcdvisualandroidapp.visualview.VirtalRealCircuitView
        android:id="@+id/vrCircuitView001"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="true"
        android:visibility="gone"/>
    <com.kemov.visual.spcd.spcdvisualandroidapp.visualview.VirtalRealCircuitView_v2
        android:id="@+id/vrCircuitView002"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="true"/>
    <LinearLayout
        android:id="@+id/ll_zoom_bar"
        android:layout_width="40dp"
        android:layout_height="match_parent"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:visibility="visible"
        android:background="@android:color/transparent"
        android:orientation="vertical">

        <View
            android:layout_width="0dp"
            android:layout_height="10dp"
            android:layout_weight="10"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapBigger0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_bigger"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapSmaller0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_smaller"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/mapOriginal0"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_original_size_w" />

        <ImageView
            android:id="@+id/mapOriginalFilled"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:src="@drawable/map_filled_size_h" />

        <View
            android:layout_width="0dp"
            android:layout_height="10dp"
            android:layout_weight="0"
            android:visibility="invisible" />
    </LinearLayout>
</RelativeLayout>
