<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="letter">
        <item>A</item>
        <item>B</item>
        <item>C</item>
        <item>D</item>
        <item>E</item>
        <item>F</item>
        <item>G</item>
    </string-array>
    <color name="PrimaryColor">#2196F3</color>
    <color name="PrimaryDarkColor">#1976D2</color>
    <color name="aliceblue">#f0f8ff</color>
    <color name="antiquewhite">#faebd7</color>
    <color name="aqua">#00ffff</color>
    <color name="aquamarine">#7fffd4</color>
    <color name="azure">#f0ffff</color>
    <color name="beige">#f5f5dc</color>
    <color name="bisque">#ffe4c4</color>
    <color name="black">#000000</color>
    <color name="blanchedalmond">#ffebcd</color>
    <color name="blue">#0000ff</color>
    <color name="blueviolet">#8a2be2</color>
    <color name="brown">#a52a2a</color>
    <color name="burlywood">#deb887</color>
    <color name="cadetblue">#5f9ea0</color>
    <color name="chartreuse">#7fff00</color>
    <color name="chocolate">#d2691e</color>
    <color name="col1">#43BAD2</color>
    <color name="col2">#AFDFE4</color>
    <color name="col3">#d238be</color>
    <color name="col4">#f9f3d3</color>
    <color name="colorAccent">#FF4081</color>
    <color name="colorPrimary">#3F51B5</color>
    <color name="colorPrimaryDark">#303F9F</color>
    <color name="color_00000000">#00000000</color>
    <color name="coral">#ff7f50</color>
    <color name="cornflowerblue">#6495ed</color>
    <color name="cornsilk">#fff8dc</color>
    <color name="crimson">#dc143c</color>
    <color name="cyan">#00ffff</color>
    <color name="darkblue">#00008b</color>
    <color name="darkcyan">#008b8b</color>
    <color name="darkgoldenrod">#b8860b</color>
    <color name="darkgray">#a9a9a9</color>
    <color name="darkgreen">#006400</color>
    <color name="darkgrey">#a9a9a9</color>
    <color name="darkkhaki">#bdb76b</color>
    <color name="darkmagenta">#8b008b</color>
    <color name="darkolivegreen">#556b2f</color>
    <color name="darkorange">#ff8c00</color>
    <color name="darkorchid">#9932cc</color>
    <color name="darkred">#8b0000</color>
    <color name="darksalmon">#e9967a</color>
    <color name="darkseagreen">#8fbc8f</color>
    <color name="darkslateblue">#483d8b</color>
    <color name="darkslategray">#2f4f4f</color>
    <color name="darkslategrey">#2f4f4f</color>
    <color name="darkturquoise">#00ced1</color>
    <color name="darkviolet">#9400d3</color>
    <color name="deep_gray">#6C778A</color>
    <color name="deeppink">#ff1493</color>
    <color name="deepskyblue">#00bfff</color>
    <color name="dimgray">#696969</color>
    <color name="dimgrey">#696969</color>
    <color name="dodgerblue">#1e90ff</color>
    <color name="firebrick">#b22222</color>
    <color name="floralwhite">#fffaf0</color>
    <color name="forestgreen">#228b22</color>
    <color name="fuchsia">#ff00ff</color>
    <color name="gainsboro">#dcdcdc</color>
    <color name="ghostwhite">#f8f8ff</color>
    <color name="gold">#ffd700</color>
    <color name="goldenrod">#daa520</color>
    <color name="gray">#808080</color>
    <color name="green">#008000</color>
    <color name="greenyellow">#adff2f</color>
    <color name="grey">#808080</color>
    <color name="honeydew">#f0fff0</color>
    <color name="hotpink">#ff69b4</color>
    <color name="indianred">#cd5c5c</color>
    <color name="indigo">#4b0082</color>
    <color name="ivory">#fffff0</color>
    <color name="khaki">#f0e68c</color>
    <color name="lavender">#e6e6fa</color>
    <color name="lavenderblush">#fff0f5</color>
    <color name="lawngreen">#7cfc00</color>
    <color name="lemonchiffon">#fffacd</color>
    <color name="lightblue">#add8e6</color>
    <color name="lightcoral">#f08080</color>
    <color name="lightcyan">#e0ffff</color>
    <color name="lightgoldenrodyellow">#fafad2</color>
    <color name="lightgray">#d3d3d3</color>
    <color name="lightgreen">#90ee90</color>
    <color name="lightgrey">#d3d3d3</color>
    <color name="lightpink">#ffb6c1</color>
    <color name="lightsalmon">#ffa07a</color>
    <color name="lightseagreen">#20b2aa</color>
    <color name="lightskyblue">#87cefa</color>
    <color name="lightslategray">#778899</color>
    <color name="lightslategrey">#778899</color>
    <color name="lightsteelblue">#b0c4de</color>
    <color name="lightyellow">#ffffe0</color>
    <color name="lime">#00ff00</color>
    <color name="limegreen">#32cd32</color>
    <color name="linen">#faf0e6</color>
    <color name="magenta">#ff00ff</color>
    <color name="maroon">#800000</color>
    <color name="mediumaquamarine">#66cdaa</color>
    <color name="mediumblue">#0000cd</color>
    <color name="mediumorchid">#ba55d3</color>
    <color name="mediumpurple">#9370db</color>
    <color name="mediumseagreen">#3cb371</color>
    <color name="mediumslateblue">#7b68ee</color>
    <color name="mediumspringgreen">#00fa9a</color>
    <color name="mediumturquoise">#48d1cc</color>
    <color name="mediumvioletred">#c71585</color>
    <color name="midnightblue">#191970</color>
    <color name="mintcream">#f5fffa</color>
    <color name="mistyrose">#ffe4e1</color>
    <color name="moccasin">#ffe4b5</color>
    <color name="navajowhite">#ffdead</color>
    <color name="navy">#000080</color>
    <color name="oldlace">#fdf5e6</color>
    <color name="olive">#808000</color>
    <color name="olivedrab">#6b8e23</color>
    <color name="orange">#ffa500</color>
    <color name="orangered">#ff4500</color>
    <color name="orchid">#da70d6</color>
    <color name="palegoldenrod">#eee8aa</color>
    <color name="palegreen">#98fb98</color>
    <color name="paleturquoise">#afeeee</color>
    <color name="palevioletred">#db7093</color>
    <color name="papayawhip">#ffefd5</color>
    <color name="peachpuff">#ffdab9</color>
    <color name="peru">#cd853f</color>
    <color name="pink">#ffc0cb</color>
    <color name="plum">#dda0dd</color>
    <color name="powderblue">#b0e0e6</color>
    <color name="purple">#800080</color>
    <color name="red">#ff0000</color>
    <color name="rosybrown">#bc8f8f</color>
    <color name="royalblue">#4169e1</color>
    <color name="saddlebrown">#8b4513</color>
    <color name="salmon">#fa8072</color>
    <color name="sandybrown">#f4a460</color>
    <color name="seaShell">#fff5ee</color>
    <color name="seagreen">#2e8b57</color>
    <color name="sienna">#a0522d</color>
    <color name="silver">#c0c0c0</color>
    <color name="skyblue">#87ceeb</color>
    <color name="slateblue">#6a5acd</color>
    <color name="slategray">#708090</color>
    <color name="snow">#fffafa</color>
    <color name="springgreen">#00ff7f</color>
    <color name="steelblue">#4682b4</color>
    <color name="tan">#d2b48c</color>
    <color name="teal">#008080</color>
    <color name="thistle">#d8bfd8</color>
    <color name="tomato">#ff6347</color>
    <color name="turquoise">#40e0d0</color>
    <color name="violet">#ee82ee</color>
    <color name="wheat">#f5deb3</color>
    <color name="white">#ffffff</color>
    <color name="whitesmoke">#f5f5f5</color>
    <color name="yellow">#ffff00</color>
    <dimen name="enter_detaile_height">30dp</dimen>
    <dimen name="enter_detaile_width">35dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="sp_text1">18sp</dimen>
    <dimen name="sp_text2">15sp</dimen>
    <dimen name="sp_text_header">13sp</dimen>
    <string name="app_name">SpcdVisualAndroidApp</string>
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="str_odf_view">ODF视图</string>
    <string name="str_opposite_dev">对侧装置</string>
    <string name="str_phy_whole_cir_view">物理全回路</string>
    <string name="str_tag_view">光纤标签图</string>
    <string name="title_activity_cubicle_back_panel_view_show">CubicleBackPanelViewShowActivity
    </string>
    <string name="title_activity_top_bar_base">TopBarBaseActivity</string>
    <string name="title_activity_vir_ports_links">VirPortsLinksActivity</string>
    <style name="AnimationFromButtom">
        <item name="android:windowEnterAnimation">@anim/popup_form_bottom</item>
        <item name="android:windowExitAnimation">@anim/drop_down_to_bottom</item>
    </style>
    <style name="AnimationFromTop">
        <item name="android:windowEnterAnimation">@anim/drop_down_from_top</item>
        <item name="android:windowExitAnimation">@anim/hide_to_top</item>
    </style>
    <style name="AnimationUpPopup">
        <item name="android:windowEnterAnimation">@anim/grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/shrink_fade_out_from_bottom</item>
    </style>
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>
    <style name="AppTheme.ActionBar" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="actionMenuTextColor">@drawable/text_color_selector_menu</item>
    </style>
    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="AppThemeLNoAction" parent="Theme.AppCompat.Light.NoActionBar"/>
    <style name="DialogStyleInActivity" parent="AppTheme.NoActionBar">
        <item name="android:windowBackground">@android:color/white</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <style name="activityTheme" parent="AppTheme">
        <item name="android:windowIsTranslucent">true</item>
    </style>
    <style name="style_user_seldia" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <!-- Dialog的windowFrame框为无 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 是否半透明 -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/color_00000000</item>
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 背景是否变模糊 -->
    </style>
    <declare-styleable name="CubicleView">
        <attr format="string" name="exampleString"/>
        <attr format="dimension" name="exampleDimension"/>
        <attr format="color" name="exampleColor"/>
        <attr format="color|reference" name="exampleDrawable"/>
    </declare-styleable>
</resources>