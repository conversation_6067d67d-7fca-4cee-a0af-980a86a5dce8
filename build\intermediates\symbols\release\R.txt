int anim abc_fade_in 0x7f010001
int anim abc_fade_out 0x7f010002
int anim abc_grow_fade_in_from_bottom 0x7f010003
int anim abc_popup_enter 0x7f010004
int anim abc_popup_exit 0x7f010005
int anim abc_shrink_fade_out_from_bottom 0x7f010006
int anim abc_slide_in_bottom 0x7f010007
int anim abc_slide_in_top 0x7f010008
int anim abc_slide_out_bottom 0x7f010009
int anim abc_slide_out_top 0x7f01000a
int anim abc_tooltip_enter 0x7f01000b
int anim abc_tooltip_exit 0x7f01000c
int anim design_bottom_sheet_slide_in 0x7f01000d
int anim design_bottom_sheet_slide_out 0x7f01000e
int anim design_fab_in 0x7f01000f
int anim design_fab_out 0x7f010010
int anim design_snackbar_in 0x7f010011
int anim design_snackbar_out 0x7f010012
int anim dialog_enter 0x7f010013
int anim dialog_exit 0x7f010014
int anim drop_down_from_top 0x7f010015
int anim drop_down_to_bottom 0x7f010016
int anim fade_in 0x7f010017
int anim fade_out 0x7f010018
int anim grow_fade_in_from_bottom 0x7f010019
int anim head_in 0x7f01001a
int anim head_out 0x7f01001b
int anim hide_to_top 0x7f01001c
int anim hold 0x7f01001d
int anim m_down_dialog 0x7f01001e
int anim m_up_dialog 0x7f01001f
int anim popup_form_bottom 0x7f010020
int anim push_bottom_in 0x7f010021
int anim push_bottom_out 0x7f010022
int anim push_left_in 0x7f010023
int anim push_left_out 0x7f010024
int anim push_right_in 0x7f010025
int anim push_right_out 0x7f010026
int anim push_top_in 0x7f010027
int anim push_top_in2 0x7f010028
int anim push_top_out 0x7f010029
int anim push_top_out2 0x7f01002a
int anim push_up_in 0x7f01002b
int anim push_up_out 0x7f01002c
int anim shrink_fade_out_from_bottom 0x7f01002d
int anim slide_in_from_left 0x7f01002e
int anim slide_in_from_right 0x7f01002f
int anim slide_out_to_left 0x7f010030
int anim slide_out_to_right 0x7f010031
int animator design_appbar_state_list_animator 0x7f020001
int animator design_fab_hide_motion_spec 0x7f020002
int animator design_fab_show_motion_spec 0x7f020003
int animator mtrl_btn_state_list_anim 0x7f020004
int animator mtrl_btn_unelevated_state_list_anim 0x7f020005
int animator mtrl_chip_state_list_anim 0x7f020006
int animator mtrl_fab_hide_motion_spec 0x7f020007
int animator mtrl_fab_show_motion_spec 0x7f020008
int animator mtrl_fab_transformation_sheet_collapse_spec 0x7f020009
int animator mtrl_fab_transformation_sheet_expand_spec 0x7f02000a
int array equip_menu 0x7f030001
int array intent_cellkey 0x7f030002
int array intent_list 0x7f030003
int array letter 0x7f030004
int array visual_tablist 0x7f030005
int attr actionBarDivider 0x7f040001
int attr actionBarItemBackground 0x7f040002
int attr actionBarPopupTheme 0x7f040003
int attr actionBarSize 0x7f040004
int attr actionBarSplitStyle 0x7f040005
int attr actionBarStyle 0x7f040006
int attr actionBarTabBarStyle 0x7f040007
int attr actionBarTabStyle 0x7f040008
int attr actionBarTabTextStyle 0x7f040009
int attr actionBarTheme 0x7f04000a
int attr actionBarWidgetTheme 0x7f04000b
int attr actionButtonStyle 0x7f04000c
int attr actionDropDownStyle 0x7f04000d
int attr actionLayout 0x7f04000e
int attr actionMenuTextAppearance 0x7f04000f
int attr actionMenuTextColor 0x7f040010
int attr actionModeBackground 0x7f040011
int attr actionModeCloseButtonStyle 0x7f040012
int attr actionModeCloseDrawable 0x7f040013
int attr actionModeCopyDrawable 0x7f040014
int attr actionModeCutDrawable 0x7f040015
int attr actionModeFindDrawable 0x7f040016
int attr actionModePasteDrawable 0x7f040017
int attr actionModePopupWindowStyle 0x7f040018
int attr actionModeSelectAllDrawable 0x7f040019
int attr actionModeShareDrawable 0x7f04001a
int attr actionModeSplitBackground 0x7f04001b
int attr actionModeStyle 0x7f04001c
int attr actionModeWebSearchDrawable 0x7f04001d
int attr actionOverflowButtonStyle 0x7f04001e
int attr actionOverflowMenuStyle 0x7f04001f
int attr actionProviderClass 0x7f040020
int attr actionViewClass 0x7f040021
int attr activityChooserViewStyle 0x7f040022
int attr alertDialogButtonGroupStyle 0x7f040023
int attr alertDialogCenterButtons 0x7f040024
int attr alertDialogStyle 0x7f040025
int attr alertDialogTheme 0x7f040026
int attr allowStacking 0x7f040027
int attr alpha 0x7f040028
int attr alphabeticModifiers 0x7f040029
int attr altSrc 0x7f04002a
int attr animate_relativeTo 0x7f04002b
int attr applyMotionScene 0x7f04002c
int attr arcMode 0x7f04002d
int attr array_id 0x7f04002e
int attr arrowHeadLength 0x7f04002f
int attr arrowShaftLength 0x7f040030
int attr attributeName 0x7f040031
int attr autoCompleteTextViewStyle 0x7f040032
int attr autoSizeMaxTextSize 0x7f040033
int attr autoSizeMinTextSize 0x7f040034
int attr autoSizePresetSizes 0x7f040035
int attr autoSizeStepGranularity 0x7f040036
int attr autoSizeTextType 0x7f040037
int attr autoTransition 0x7f040038
int attr background 0x7f040039
int attr backgroundSplit 0x7f04003a
int attr backgroundStacked 0x7f04003b
int attr backgroundTint 0x7f04003c
int attr backgroundTintMode 0x7f04003d
int attr barLength 0x7f04003e
int attr barrierAllowsGoneWidgets 0x7f04003f
int attr barrierDirection 0x7f040040
int attr barrierMargin 0x7f040041
int attr behavior_autoHide 0x7f040042
int attr behavior_fitToContents 0x7f040043
int attr behavior_hideable 0x7f040044
int attr behavior_overlapTop 0x7f040045
int attr behavior_peekHeight 0x7f040046
int attr behavior_skipCollapsed 0x7f040047
int attr bookmark_tab_selected 0x7f040048
int attr bookmark_tab_unselected 0x7f040049
int attr borderWidth 0x7f04004a
int attr borderlessButtonStyle 0x7f04004b
int attr bottomAppBarStyle 0x7f04004c
int attr bottomNavigationStyle 0x7f04004d
int attr bottomSheetDialogTheme 0x7f04004e
int attr bottomSheetStyle 0x7f04004f
int attr boxBackgroundColor 0x7f040050
int attr boxBackgroundMode 0x7f040051
int attr boxCollapsedPaddingTop 0x7f040052
int attr boxCornerRadiusBottomEnd 0x7f040053
int attr boxCornerRadiusBottomStart 0x7f040054
int attr boxCornerRadiusTopEnd 0x7f040055
int attr boxCornerRadiusTopStart 0x7f040056
int attr boxStrokeColor 0x7f040057
int attr boxStrokeWidth 0x7f040058
int attr brightness 0x7f040059
int attr buttonBarButtonStyle 0x7f04005a
int attr buttonBarNegativeButtonStyle 0x7f04005b
int attr buttonBarNeutralButtonStyle 0x7f04005c
int attr buttonBarPositiveButtonStyle 0x7f04005d
int attr buttonBarStyle 0x7f04005e
int attr buttonGravity 0x7f04005f
int attr buttonIconDimen 0x7f040060
int attr buttonPanelSideLayout 0x7f040061
int attr buttonStyle 0x7f040062
int attr buttonStyleSmall 0x7f040063
int attr buttonTint 0x7f040064
int attr buttonTintMode 0x7f040065
int attr cardBackgroundColor 0x7f040066
int attr cardCornerRadius 0x7f040067
int attr cardElevation 0x7f040068
int attr cardMaxElevation 0x7f040069
int attr cardPreventCornerOverlap 0x7f04006a
int attr cardUseCompatPadding 0x7f04006b
int attr cardViewStyle 0x7f04006c
int attr chainUseRtl 0x7f04006d
int attr checkboxStyle 0x7f04006e
int attr checkedChip 0x7f04006f
int attr checkedIcon 0x7f040070
int attr checkedIconEnabled 0x7f040071
int attr checkedIconVisible 0x7f040072
int attr checkedTextViewStyle 0x7f040073
int attr chipBackgroundColor 0x7f040074
int attr chipCornerRadius 0x7f040075
int attr chipEndPadding 0x7f040076
int attr chipGroupStyle 0x7f040077
int attr chipIcon 0x7f040078
int attr chipIconEnabled 0x7f040079
int attr chipIconSize 0x7f04007a
int attr chipIconTint 0x7f04007b
int attr chipIconVisible 0x7f04007c
int attr chipMinHeight 0x7f04007d
int attr chipSpacing 0x7f04007e
int attr chipSpacingHorizontal 0x7f04007f
int attr chipSpacingVertical 0x7f040080
int attr chipStandaloneStyle 0x7f040081
int attr chipStartPadding 0x7f040082
int attr chipStrokeColor 0x7f040083
int attr chipStrokeWidth 0x7f040084
int attr chipStyle 0x7f040085
int attr circleRadius 0x7f040086
int attr clickAction 0x7f040087
int attr closeIcon 0x7f040088
int attr closeIconEnabled 0x7f040089
int attr closeIconEndPadding 0x7f04008a
int attr closeIconSize 0x7f04008b
int attr closeIconStartPadding 0x7f04008c
int attr closeIconTint 0x7f04008d
int attr closeIconVisible 0x7f04008e
int attr closeItemLayout 0x7f04008f
int attr collapseContentDescription 0x7f040090
int attr collapseIcon 0x7f040091
int attr collapsedTitleGravity 0x7f040092
int attr collapsedTitleTextAppearance 0x7f040093
int attr color 0x7f040094
int attr colorAccent 0x7f040095
int attr colorBackgroundFloating 0x7f040096
int attr colorButtonNormal 0x7f040097
int attr colorControlActivated 0x7f040098
int attr colorControlHighlight 0x7f040099
int attr colorControlNormal 0x7f04009a
int attr colorError 0x7f04009b
int attr colorPrimary 0x7f04009c
int attr colorPrimaryDark 0x7f04009d
int attr colorSecondary 0x7f04009e
int attr colorSwitchThumbNormal 0x7f04009f
int attr commitIcon 0x7f0400a0
int attr constraintSet 0x7f0400a1
int attr constraintSetEnd 0x7f0400a2
int attr constraintSetStart 0x7f0400a3
int attr constraint_referenced_ids 0x7f0400a4
int attr constraint_referenced_tags 0x7f0400a5
int attr constraints 0x7f0400a6
int attr content 0x7f0400a7
int attr contentDescription 0x7f0400a8
int attr contentInsetEnd 0x7f0400a9
int attr contentInsetEndWithActions 0x7f0400aa
int attr contentInsetLeft 0x7f0400ab
int attr contentInsetRight 0x7f0400ac
int attr contentInsetStart 0x7f0400ad
int attr contentInsetStartWithNavigation 0x7f0400ae
int attr contentPadding 0x7f0400af
int attr contentPaddingBottom 0x7f0400b0
int attr contentPaddingLeft 0x7f0400b1
int attr contentPaddingRight 0x7f0400b2
int attr contentPaddingTop 0x7f0400b3
int attr contentScrim 0x7f0400b4
int attr contrast 0x7f0400b5
int attr controlBackground 0x7f0400b6
int attr coordinatorLayoutStyle 0x7f0400b7
int attr cornerRadius 0x7f0400b8
int attr counterEnabled 0x7f0400b9
int attr counterMaxLength 0x7f0400ba
int attr counterOverflowTextAppearance 0x7f0400bb
int attr counterTextAppearance 0x7f0400bc
int attr crossfade 0x7f0400bd
int attr currentState 0x7f0400be
int attr curveFit 0x7f0400bf
int attr customBoolean 0x7f0400c0
int attr customColorDrawableValue 0x7f0400c1
int attr customColorValue 0x7f0400c2
int attr customDimension 0x7f0400c3
int attr customFloatValue 0x7f0400c4
int attr customIntegerValue 0x7f0400c5
int attr customNavigationLayout 0x7f0400c6
int attr customPixelDimension 0x7f0400c7
int attr customStringValue 0x7f0400c8
int attr defaultDuration 0x7f0400c9
int attr defaultQueryHint 0x7f0400ca
int attr defaultState 0x7f0400cb
int attr deltaPolarAngle 0x7f0400cc
int attr deltaPolarRadius 0x7f0400cd
int attr deriveConstraintsFrom 0x7f0400ce
int attr dialogCornerRadius 0x7f0400cf
int attr dialogPreferredPadding 0x7f0400d0
int attr dialogTheme 0x7f0400d1
int attr displayOptions 0x7f0400d2
int attr divider 0x7f0400d3
int attr dividerHorizontal 0x7f0400d4
int attr dividerPadding 0x7f0400d5
int attr dividerVertical 0x7f0400d6
int attr dragDirection 0x7f0400d7
int attr dragScale 0x7f0400d8
int attr dragThreshold 0x7f0400d9
int attr drawPath 0x7f0400da
int attr drawableSize 0x7f0400db
int attr drawerArrowStyle 0x7f0400dc
int attr dropDownListViewStyle 0x7f0400dd
int attr dropdownListPreferredItemHeight 0x7f0400de
int attr duration 0x7f0400df
int attr editTextBackground 0x7f0400e0
int attr editTextColor 0x7f0400e1
int attr editTextStyle 0x7f0400e2
int attr elevation 0x7f0400e3
int attr emptyVisibility 0x7f0400e4
int attr enforceMaterialTheme 0x7f0400e5
int attr enforceTextAppearance 0x7f0400e6
int attr errorEnabled 0x7f0400e7
int attr errorTextAppearance 0x7f0400e8
int attr exampleColor 0x7f0400e9
int attr exampleDimension 0x7f0400ea
int attr exampleDrawable 0x7f0400eb
int attr exampleString 0x7f0400ec
int attr expandActivityOverflowButtonDrawable 0x7f0400ed
int attr expanded 0x7f0400ee
int attr expandedTitleGravity 0x7f0400ef
int attr expandedTitleMargin 0x7f0400f0
int attr expandedTitleMarginBottom 0x7f0400f1
int attr expandedTitleMarginEnd 0x7f0400f2
int attr expandedTitleMarginStart 0x7f0400f3
int attr expandedTitleMarginTop 0x7f0400f4
int attr expandedTitleTextAppearance 0x7f0400f5
int attr fabAlignmentMode 0x7f0400f6
int attr fabCradleMargin 0x7f0400f7
int attr fabCradleRoundedCornerRadius 0x7f0400f8
int attr fabCradleVerticalOffset 0x7f0400f9
int attr fabCustomSize 0x7f0400fa
int attr fabSize 0x7f0400fb
int attr fastScrollEnabled 0x7f0400fc
int attr fastScrollHorizontalThumbDrawable 0x7f0400fd
int attr fastScrollHorizontalTrackDrawable 0x7f0400fe
int attr fastScrollVerticalThumbDrawable 0x7f0400ff
int attr fastScrollVerticalTrackDrawable 0x7f040100
int attr firstBaselineToTopHeight 0x7f040101
int attr floatingActionButtonStyle 0x7f040102
int attr flow_firstHorizontalBias 0x7f040103
int attr flow_firstHorizontalStyle 0x7f040104
int attr flow_firstVerticalBias 0x7f040105
int attr flow_firstVerticalStyle 0x7f040106
int attr flow_horizontalAlign 0x7f040107
int attr flow_horizontalBias 0x7f040108
int attr flow_horizontalGap 0x7f040109
int attr flow_horizontalStyle 0x7f04010a
int attr flow_lastHorizontalBias 0x7f04010b
int attr flow_lastHorizontalStyle 0x7f04010c
int attr flow_lastVerticalBias 0x7f04010d
int attr flow_lastVerticalStyle 0x7f04010e
int attr flow_maxElementsWrap 0x7f04010f
int attr flow_padding 0x7f040110
int attr flow_verticalAlign 0x7f040111
int attr flow_verticalBias 0x7f040112
int attr flow_verticalGap 0x7f040113
int attr flow_verticalStyle 0x7f040114
int attr flow_wrapMode 0x7f040115
int attr font 0x7f040116
int attr fontFamily 0x7f040117
int attr fontProviderAuthority 0x7f040118
int attr fontProviderCerts 0x7f040119
int attr fontProviderFetchStrategy 0x7f04011a
int attr fontProviderFetchTimeout 0x7f04011b
int attr fontProviderPackage 0x7f04011c
int attr fontProviderQuery 0x7f04011d
int attr fontStyle 0x7f04011e
int attr fontVariationSettings 0x7f04011f
int attr fontWeight 0x7f040120
int attr foregroundInsidePadding 0x7f040121
int attr framePosition 0x7f040122
int attr gapBetweenBars 0x7f040123
int attr goIcon 0x7f040124
int attr headerLayout 0x7f040125
int attr height 0x7f040126
int attr helperText 0x7f040127
int attr helperTextEnabled 0x7f040128
int attr helperTextTextAppearance 0x7f040129
int attr hideMotionSpec 0x7f04012a
int attr hideOnContentScroll 0x7f04012b
int attr hideOnScroll 0x7f04012c
int attr hintAnimationEnabled 0x7f04012d
int attr hintEnabled 0x7f04012e
int attr hintTextAppearance 0x7f04012f
int attr homeAsUpIndicator 0x7f040130
int attr homeLayout 0x7f040131
int attr hoveredFocusedTranslationZ 0x7f040132
int attr icon 0x7f040133
int attr iconEndPadding 0x7f040134
int attr iconGravity 0x7f040135
int attr iconPadding 0x7f040136
int attr iconSize 0x7f040137
int attr iconStartPadding 0x7f040138
int attr iconTint 0x7f040139
int attr iconTintMode 0x7f04013a
int attr iconifiedByDefault 0x7f04013b
int attr imageButtonStyle 0x7f04013c
int attr indeterminateProgressStyle 0x7f04013d
int attr initialActivityCount 0x7f04013e
int attr inner_corner_color 0x7f04013f
int attr inner_corner_length 0x7f040140
int attr inner_corner_width 0x7f040141
int attr inner_height 0x7f040142
int attr inner_margintop 0x7f040143
int attr inner_scan_bitmap 0x7f040144
int attr inner_scan_iscircle 0x7f040145
int attr inner_scan_speed 0x7f040146
int attr inner_width 0x7f040147
int attr insetForeground 0x7f040148
int attr isLightTheme 0x7f040149
int attr itemBackground 0x7f04014a
int attr itemHorizontalPadding 0x7f04014b
int attr itemHorizontalTranslationEnabled 0x7f04014c
int attr itemIconPadding 0x7f04014d
int attr itemIconSize 0x7f04014e
int attr itemIconTint 0x7f04014f
int attr itemPadding 0x7f040150
int attr itemSpacing 0x7f040151
int attr itemTextAppearance 0x7f040152
int attr itemTextAppearanceActive 0x7f040153
int attr itemTextAppearanceInactive 0x7f040154
int attr itemTextColor 0x7f040155
int attr item_layout 0x7f040156
int attr keyPositionType 0x7f040157
int attr keylines 0x7f040158
int attr labelVisibilityMode 0x7f040159
int attr lastBaselineToBottomHeight 0x7f04015a
int attr layout 0x7f04015b
int attr layoutDescription 0x7f04015c
int attr layoutDuringTransition 0x7f04015d
int attr layoutManager 0x7f04015e
int attr layout_anchor 0x7f04015f
int attr layout_anchorGravity 0x7f040160
int attr layout_behavior 0x7f040161
int attr layout_collapseMode 0x7f040162
int attr layout_collapseParallaxMultiplier 0x7f040163
int attr layout_constrainedHeight 0x7f040164
int attr layout_constrainedWidth 0x7f040165
int attr layout_constraintBaseline_creator 0x7f040166
int attr layout_constraintBaseline_toBaselineOf 0x7f040167
int attr layout_constraintBottom_creator 0x7f040168
int attr layout_constraintBottom_toBottomOf 0x7f040169
int attr layout_constraintBottom_toTopOf 0x7f04016a
int attr layout_constraintCircle 0x7f04016b
int attr layout_constraintCircleAngle 0x7f04016c
int attr layout_constraintCircleRadius 0x7f04016d
int attr layout_constraintDimensionRatio 0x7f04016e
int attr layout_constraintEnd_toEndOf 0x7f04016f
int attr layout_constraintEnd_toStartOf 0x7f040170
int attr layout_constraintGuide_begin 0x7f040171
int attr layout_constraintGuide_end 0x7f040172
int attr layout_constraintGuide_percent 0x7f040173
int attr layout_constraintHeight_default 0x7f040174
int attr layout_constraintHeight_max 0x7f040175
int attr layout_constraintHeight_min 0x7f040176
int attr layout_constraintHeight_percent 0x7f040177
int attr layout_constraintHorizontal_bias 0x7f040178
int attr layout_constraintHorizontal_chainStyle 0x7f040179
int attr layout_constraintHorizontal_weight 0x7f04017a
int attr layout_constraintLeft_creator 0x7f04017b
int attr layout_constraintLeft_toLeftOf 0x7f04017c
int attr layout_constraintLeft_toRightOf 0x7f04017d
int attr layout_constraintRight_creator 0x7f04017e
int attr layout_constraintRight_toLeftOf 0x7f04017f
int attr layout_constraintRight_toRightOf 0x7f040180
int attr layout_constraintStart_toEndOf 0x7f040181
int attr layout_constraintStart_toStartOf 0x7f040182
int attr layout_constraintTag 0x7f040183
int attr layout_constraintTop_creator 0x7f040184
int attr layout_constraintTop_toBottomOf 0x7f040185
int attr layout_constraintTop_toTopOf 0x7f040186
int attr layout_constraintVertical_bias 0x7f040187
int attr layout_constraintVertical_chainStyle 0x7f040188
int attr layout_constraintVertical_weight 0x7f040189
int attr layout_constraintWidth_default 0x7f04018a
int attr layout_constraintWidth_max 0x7f04018b
int attr layout_constraintWidth_min 0x7f04018c
int attr layout_constraintWidth_percent 0x7f04018d
int attr layout_dodgeInsetEdges 0x7f04018e
int attr layout_editor_absoluteX 0x7f04018f
int attr layout_editor_absoluteY 0x7f040190
int attr layout_goneMarginBottom 0x7f040191
int attr layout_goneMarginEnd 0x7f040192
int attr layout_goneMarginLeft 0x7f040193
int attr layout_goneMarginRight 0x7f040194
int attr layout_goneMarginStart 0x7f040195
int attr layout_goneMarginTop 0x7f040196
int attr layout_insetEdge 0x7f040197
int attr layout_keyline 0x7f040198
int attr layout_optimizationLevel 0x7f040199
int attr layout_scrollFlags 0x7f04019a
int attr layout_scrollInterpolator 0x7f04019b
int attr liftOnScroll 0x7f04019c
int attr limitBoundsTo 0x7f04019d
int attr lineHeight 0x7f04019e
int attr lineSpacing 0x7f04019f
int attr listChoiceBackgroundIndicator 0x7f0401a0
int attr listDividerAlertDialog 0x7f0401a1
int attr listItemLayout 0x7f0401a2
int attr listLayout 0x7f0401a3
int attr listMenuViewStyle 0x7f0401a4
int attr listPopupWindowStyle 0x7f0401a5
int attr listPreferredItemHeight 0x7f0401a6
int attr listPreferredItemHeightLarge 0x7f0401a7
int attr listPreferredItemHeightSmall 0x7f0401a8
int attr listPreferredItemPaddingLeft 0x7f0401a9
int attr listPreferredItemPaddingRight 0x7f0401aa
int attr logo 0x7f0401ab
int attr logoDescription 0x7f0401ac
int attr materialButtonStyle 0x7f0401ad
int attr materialCardViewStyle 0x7f0401ae
int attr maxAcceleration 0x7f0401af
int attr maxActionInlineWidth 0x7f0401b0
int attr maxButtonHeight 0x7f0401b1
int attr maxHeight 0x7f0401b2
int attr maxImageSize 0x7f0401b3
int attr maxVelocity 0x7f0401b4
int attr maxWidth 0x7f0401b5
int attr measureWithLargestChild 0x7f0401b6
int attr menu 0x7f0401b7
int attr minHeight 0x7f0401b8
int attr minWidth 0x7f0401b9
int attr mock_diagonalsColor 0x7f0401ba
int attr mock_label 0x7f0401bb
int attr mock_labelBackgroundColor 0x7f0401bc
int attr mock_labelColor 0x7f0401bd
int attr mock_showDiagonals 0x7f0401be
int attr mock_showLabel 0x7f0401bf
int attr motionDebug 0x7f0401c0
int attr motionInterpolator 0x7f0401c1
int attr motionPathRotate 0x7f0401c2
int attr motionProgress 0x7f0401c3
int attr motionStagger 0x7f0401c4
int attr motionTarget 0x7f0401c5
int attr motion_postLayoutCollision 0x7f0401c6
int attr motion_triggerOnCollision 0x7f0401c7
int attr moveWhenScrollAtTop 0x7f0401c8
int attr multiChoiceItemLayout 0x7f0401c9
int attr navigationContentDescription 0x7f0401ca
int attr navigationIcon 0x7f0401cb
int attr navigationMode 0x7f0401cc
int attr navigationViewStyle 0x7f0401cd
int attr nestedScrollFlags 0x7f0401ce
int attr numericModifiers 0x7f0401cf
int attr onCross 0x7f0401d0
int attr onHide 0x7f0401d1
int attr onNegativeCross 0x7f0401d2
int attr onPositiveCross 0x7f0401d3
int attr onShow 0x7f0401d4
int attr onTouchUp 0x7f0401d5
int attr overlapAnchor 0x7f0401d6
int attr overlay 0x7f0401d7
int attr paddingBottomNoButtons 0x7f0401d8
int attr paddingEnd 0x7f0401d9
int attr paddingStart 0x7f0401da
int attr paddingTopNoTitle 0x7f0401db
int attr panelBackground 0x7f0401dc
int attr panelMenuListTheme 0x7f0401dd
int attr panelMenuListWidth 0x7f0401de
int attr passwordToggleContentDescription 0x7f0401df
int attr passwordToggleDrawable 0x7f0401e0
int attr passwordToggleEnabled 0x7f0401e1
int attr passwordToggleTint 0x7f0401e2
int attr passwordToggleTintMode 0x7f0401e3
int attr pathMotionArc 0x7f0401e4
int attr path_percent 0x7f0401e5
int attr percent 0x7f0401e6
int attr percentHeight 0x7f0401e7
int attr percentWidth 0x7f0401e8
int attr percentX 0x7f0401e9
int attr percentY 0x7f0401ea
int attr perpendicularPath_percent 0x7f0401eb
int attr pivotAnchor 0x7f0401ec
int attr placeholder_emptyVisibility 0x7f0401ed
int attr popupMenuStyle 0x7f0401ee
int attr popupTheme 0x7f0401ef
int attr popupWindowStyle 0x7f0401f0
int attr preserveIconSpacing 0x7f0401f1
int attr pressedTranslationZ 0x7f0401f2
int attr progressBarPadding 0x7f0401f3
int attr progressBarStyle 0x7f0401f4
int attr queryBackground 0x7f0401f5
int attr queryHint 0x7f0401f6
int attr radioButtonStyle 0x7f0401f7
int attr ratingBarStyle 0x7f0401f8
int attr ratingBarStyleIndicator 0x7f0401f9
int attr ratingBarStyleSmall 0x7f0401fa
int attr region_heightLessThan 0x7f0401fb
int attr region_heightMoreThan 0x7f0401fc
int attr region_widthLessThan 0x7f0401fd
int attr region_widthMoreThan 0x7f0401fe
int attr relation 0x7f0401ff
int attr reverseLayout 0x7f040200
int attr rippleColor 0x7f040201
int attr round 0x7f040202
int attr roundBackgroundColor 0x7f040203
int attr roundForegroundColor 0x7f040204
int attr roundPercent 0x7f040205
int attr roundWidth 0x7f040206
int attr saturation 0x7f040207
int attr scrimAnimationDuration 0x7f040208
int attr scrimBackground 0x7f040209
int attr scrimVisibleHeightTrigger 0x7f04020a
int attr searchHintIcon 0x7f04020b
int attr searchIcon 0x7f04020c
int attr searchViewStyle 0x7f04020d
int attr seekBarStyle 0x7f04020e
int attr selectableItemBackground 0x7f04020f
int attr selectableItemBackgroundBorderless 0x7f040210
int attr showAsAction 0x7f040211
int attr showDividers 0x7f040212
int attr showMotionSpec 0x7f040213
int attr showPaths 0x7f040214
int attr showText 0x7f040215
int attr showTitle 0x7f040216
int attr singleChoiceItemLayout 0x7f040217
int attr singleLine 0x7f040218
int attr singleSelection 0x7f040219
int attr sizePercent 0x7f04021a
int attr snackbarButtonStyle 0x7f04021b
int attr snackbarStyle 0x7f04021c
int attr spanCount 0x7f04021d
int attr spinBars 0x7f04021e
int attr spinnerDropDownItemStyle 0x7f04021f
int attr spinnerStyle 0x7f040220
int attr spinner_array 0x7f040221
int attr splitTrack 0x7f040222
int attr srcCompat 0x7f040223
int attr stackFromEnd 0x7f040224
int attr staggered 0x7f040225
int attr state_above_anchor 0x7f040226
int attr state_collapsed 0x7f040227
int attr state_collapsible 0x7f040228
int attr state_liftable 0x7f040229
int attr state_lifted 0x7f04022a
int attr statusBarBackground 0x7f04022b
int attr statusBarScrim 0x7f04022c
int attr string_array 0x7f04022d
int attr strokeColor 0x7f04022e
int attr strokeWidth 0x7f04022f
int attr subMenuArrow 0x7f040230
int attr submitBackground 0x7f040231
int attr subtitle 0x7f040232
int attr subtitleTextAppearance 0x7f040233
int attr subtitleTextColor 0x7f040234
int attr subtitleTextStyle 0x7f040235
int attr suggestionRowLayout 0x7f040236
int attr switchMinWidth 0x7f040237
int attr switchPadding 0x7f040238
int attr switchStyle 0x7f040239
int attr switchTextAppearance 0x7f04023a
int attr tabBackground 0x7f04023b
int attr tabContentStart 0x7f04023c
int attr tabGravity 0x7f04023d
int attr tabIconTint 0x7f04023e
int attr tabIconTintMode 0x7f04023f
int attr tabIndicator 0x7f040240
int attr tabIndicatorAnimationDuration 0x7f040241
int attr tabIndicatorColor 0x7f040242
int attr tabIndicatorFullWidth 0x7f040243
int attr tabIndicatorGravity 0x7f040244
int attr tabIndicatorHeight 0x7f040245
int attr tabInlineLabel 0x7f040246
int attr tabMaxWidth 0x7f040247
int attr tabMinWidth 0x7f040248
int attr tabMode 0x7f040249
int attr tabPadding 0x7f04024a
int attr tabPaddingBottom 0x7f04024b
int attr tabPaddingEnd 0x7f04024c
int attr tabPaddingStart 0x7f04024d
int attr tabPaddingTop 0x7f04024e
int attr tabRippleColor 0x7f04024f
int attr tabSelectedTextColor 0x7f040250
int attr tabStyle 0x7f040251
int attr tabTextAppearance 0x7f040252
int attr tabTextColor 0x7f040253
int attr tabUnboundedRipple 0x7f040254
int attr targetId 0x7f040255
int attr telltales_tailColor 0x7f040256
int attr telltales_tailScale 0x7f040257
int attr telltales_velocityMode 0x7f040258
int attr textAllCaps 0x7f040259
int attr textAppearanceBody1 0x7f04025a
int attr textAppearanceBody2 0x7f04025b
int attr textAppearanceButton 0x7f04025c
int attr textAppearanceCaption 0x7f04025d
int attr textAppearanceHeadline1 0x7f04025e
int attr textAppearanceHeadline2 0x7f04025f
int attr textAppearanceHeadline3 0x7f040260
int attr textAppearanceHeadline4 0x7f040261
int attr textAppearanceHeadline5 0x7f040262
int attr textAppearanceHeadline6 0x7f040263
int attr textAppearanceLargePopupMenu 0x7f040264
int attr textAppearanceListItem 0x7f040265
int attr textAppearanceListItemSecondary 0x7f040266
int attr textAppearanceListItemSmall 0x7f040267
int attr textAppearanceOverline 0x7f040268
int attr textAppearancePopupMenuHeader 0x7f040269
int attr textAppearanceSearchResultSubtitle 0x7f04026a
int attr textAppearanceSearchResultTitle 0x7f04026b
int attr textAppearanceSmallPopupMenu 0x7f04026c
int attr textAppearanceSubtitle1 0x7f04026d
int attr textAppearanceSubtitle2 0x7f04026e
int attr textColorAlertDialogListItem 0x7f04026f
int attr textColorError 0x7f040270
int attr textColorSearchUrl 0x7f040271
int attr textEndPadding 0x7f040272
int attr textInputStyle 0x7f040273
int attr textStartPadding 0x7f040274
int attr theme 0x7f040275
int attr thickness 0x7f040276
int attr thumbTextPadding 0x7f040277
int attr thumbTint 0x7f040278
int attr thumbTintMode 0x7f040279
int attr tickMark 0x7f04027a
int attr tickMarkTint 0x7f04027b
int attr tickMarkTintMode 0x7f04027c
int attr tint 0x7f04027d
int attr tintMode 0x7f04027e
int attr title 0x7f04027f
int attr titleEnabled 0x7f040280
int attr titleMargin 0x7f040281
int attr titleMarginBottom 0x7f040282
int attr titleMarginEnd 0x7f040283
int attr titleMarginStart 0x7f040284
int attr titleMarginTop 0x7f040285
int attr titleMargins 0x7f040286
int attr titleTextAppearance 0x7f040287
int attr titleTextColor 0x7f040288
int attr titleTextStyle 0x7f040289
int attr toolbarId 0x7f04028a
int attr toolbarNavigationButtonStyle 0x7f04028b
int attr toolbarStyle 0x7f04028c
int attr tooltipForegroundColor 0x7f04028d
int attr tooltipFrameBackground 0x7f04028e
int attr tooltipText 0x7f04028f
int attr touchAnchorId 0x7f040290
int attr touchAnchorSide 0x7f040291
int attr touchRegionId 0x7f040292
int attr track 0x7f040293
int attr trackTint 0x7f040294
int attr trackTintMode 0x7f040295
int attr transitionDisable 0x7f040296
int attr transitionEasing 0x7f040297
int attr transitionFlags 0x7f040298
int attr transitionPathRotate 0x7f040299
int attr triggerId 0x7f04029a
int attr triggerReceiver 0x7f04029b
int attr triggerSlack 0x7f04029c
int attr ttcIndex 0x7f04029d
int attr underline 0x7f04029e
int attr useCompatPadding 0x7f04029f
int attr viewInflaterClass 0x7f0402a0
int attr visibilityMode 0x7f0402a1
int attr voiceIcon 0x7f0402a2
int attr warmth 0x7f0402a3
int attr waveDecay 0x7f0402a4
int attr waveOffset 0x7f0402a5
int attr wavePeriod 0x7f0402a6
int attr waveShape 0x7f0402a7
int attr waveVariesBy 0x7f0402a8
int attr windowActionBar 0x7f0402a9
int attr windowActionBarOverlay 0x7f0402aa
int attr windowActionModeOverlay 0x7f0402ab
int attr windowFixedHeightMajor 0x7f0402ac
int attr windowFixedHeightMinor 0x7f0402ad
int attr windowFixedWidthMajor 0x7f0402ae
int attr windowFixedWidthMinor 0x7f0402af
int attr windowMinWidthMajor 0x7f0402b0
int attr windowMinWidthMinor 0x7f0402b1
int attr windowNoTitle 0x7f0402b2
int attr zeroPointRoundColor 0x7f0402b3
int attr zeroPointRoundSize 0x7f0402b4
int bool abc_action_bar_embed_tabs 0x7f050001
int bool abc_allow_stacked_button_bar 0x7f050002
int bool abc_config_actionMenuItemAllCaps 0x7f050003
int bool abc_config_closeDialogWhenTouchOutside 0x7f050004
int bool abc_config_showMenuShortcutsWhenKeyboardPresent 0x7f050005
int bool mtrl_btn_textappearance_all_caps 0x7f050006
int color PrimaryColor 0x7f060001
int color PrimaryDarkColor 0x7f060002
int color abc_background_cache_hint_selector_material_dark 0x7f060003
int color abc_background_cache_hint_selector_material_light 0x7f060004
int color abc_btn_colored_borderless_text_material 0x7f060005
int color abc_btn_colored_text_material 0x7f060006
int color abc_color_highlight_material 0x7f060007
int color abc_hint_foreground_material_dark 0x7f060008
int color abc_hint_foreground_material_light 0x7f060009
int color abc_input_method_navigation_guard 0x7f06000a
int color abc_primary_text_disable_only_material_dark 0x7f06000b
int color abc_primary_text_disable_only_material_light 0x7f06000c
int color abc_primary_text_material_dark 0x7f06000d
int color abc_primary_text_material_light 0x7f06000e
int color abc_search_url_text 0x7f06000f
int color abc_search_url_text_normal 0x7f060010
int color abc_search_url_text_pressed 0x7f060011
int color abc_search_url_text_selected 0x7f060012
int color abc_secondary_text_material_dark 0x7f060013
int color abc_secondary_text_material_light 0x7f060014
int color abc_tint_btn_checkable 0x7f060015
int color abc_tint_default 0x7f060016
int color abc_tint_edittext 0x7f060017
int color abc_tint_seek_thumb 0x7f060018
int color abc_tint_spinner 0x7f060019
int color abc_tint_switch_thumb 0x7f06001a
int color abc_tint_switch_track 0x7f06001b
int color accent_material_dark 0x7f06001c
int color accent_material_light 0x7f06001d
int color aliceblue 0x7f06001e
int color antiquewhite 0x7f06001f
int color aqua 0x7f060020
int color aquamarine 0x7f060021
int color azure 0x7f060022
int color back_gray 0x7f060023
int color back_gray_light 0x7f060024
int color back_red 0x7f060025
int color background_floating_material_dark 0x7f060026
int color background_floating_material_light 0x7f060027
int color background_material_dark 0x7f060028
int color background_material_light 0x7f060029
int color base_grey_very_light 0x7f06002a
int color beige 0x7f06002b
int color bg_color 0x7f06002c
int color bisque 0x7f06002d
int color black 0x7f06002e
int color blanchedalmond 0x7f06002f
int color blue 0x7f060030
int color blueviolet 0x7f060031
int color bright_foreground_disabled_material_dark 0x7f060032
int color bright_foreground_disabled_material_light 0x7f060033
int color bright_foreground_inverse_material_dark 0x7f060034
int color bright_foreground_inverse_material_light 0x7f060035
int color bright_foreground_material_dark 0x7f060036
int color bright_foreground_material_light 0x7f060037
int color brown 0x7f060038
int color burlywood 0x7f060039
int color button_material_dark 0x7f06003a
int color button_material_light 0x7f06003b
int color cadetblue 0x7f06003c
int color cardview_dark_background 0x7f06003d
int color cardview_light_background 0x7f06003e
int color cardview_shadow_end_color 0x7f06003f
int color cardview_shadow_start_color 0x7f060040
int color cc 0x7f060041
int color chartreuse 0x7f060042
int color chocolate 0x7f060043
int color col1 0x7f060044
int color col2 0x7f060045
int color col3 0x7f060046
int color col4 0x7f060047
int color colorAccent 0x7f060048
int color colorPrimary 0x7f060049
int color colorPrimaryDark 0x7f06004a
int color color_00000000 0x7f06004b
int color color_aa1a1b1b 0x7f06004c
int color color_dad9db 0x7f06004d
int color color_top_select_back 0x7f06004e
int color color_top_select_text 0x7f06004f
int color color_top_text 0x7f060050
int color color_yellow 0x7f060051
int color contents_text 0x7f060052
int color coral 0x7f060053
int color cornflowerblue 0x7f060054
int color cornsilk 0x7f060055
int color crimson 0x7f060056
int color cyan 0x7f060057
int color darkblue 0x7f060058
int color darkcyan 0x7f060059
int color darkgoldenrod 0x7f06005a
int color darkgray 0x7f06005b
int color darkgreen 0x7f06005c
int color darkgrey 0x7f06005d
int color darkkhaki 0x7f06005e
int color darkmagenta 0x7f06005f
int color darkolivegreen 0x7f060060
int color darkorange 0x7f060061
int color darkorchid 0x7f060062
int color darkred 0x7f060063
int color darksalmon 0x7f060064
int color darkseagreen 0x7f060065
int color darkslateblue 0x7f060066
int color darkslategray 0x7f060067
int color darkslategrey 0x7f060068
int color darkturquoise 0x7f060069
int color darkviolet 0x7f06006a
int color data_gray 0x7f06006b
int color deep_gray 0x7f06006c
int color deeppink 0x7f06006d
int color deepskyblue 0x7f06006e
int color design_bottom_navigation_shadow_color 0x7f06006f
int color design_default_color_primary 0x7f060070
int color design_default_color_primary_dark 0x7f060071
int color design_error 0x7f060072
int color design_fab_shadow_end_color 0x7f060073
int color design_fab_shadow_mid_color 0x7f060074
int color design_fab_shadow_start_color 0x7f060075
int color design_fab_stroke_end_inner_color 0x7f060076
int color design_fab_stroke_end_outer_color 0x7f060077
int color design_fab_stroke_top_inner_color 0x7f060078
int color design_fab_stroke_top_outer_color 0x7f060079
int color design_snackbar_background_color 0x7f06007a
int color design_textinput_error_color_dark 0x7f06007b
int color design_textinput_error_color_light 0x7f06007c
int color design_tint_password_toggle 0x7f06007d
int color dim_foreground_disabled_material_dark 0x7f06007e
int color dim_foreground_disabled_material_light 0x7f06007f
int color dim_foreground_material_dark 0x7f060080
int color dim_foreground_material_light 0x7f060081
int color dimgray 0x7f060082
int color dimgrey 0x7f060083
int color divide_gray 0x7f060084
int color dodgerblue 0x7f060085
int color encode_view 0x7f060086
int color error_color_material_dark 0x7f060087
int color error_color_material_light 0x7f060088
int color firebrick 0x7f060089
int color floralwhite 0x7f06008a
int color foreground_material_dark 0x7f06008b
int color foreground_material_light 0x7f06008c
int color forestgreen 0x7f06008d
int color fragment_title_back 0x7f06008e
int color fuchsia 0x7f06008f
int color gainsboro 0x7f060090
int color ghostwhite 0x7f060091
int color gold 0x7f060092
int color goldenrod 0x7f060093
int color gray 0x7f060094
int color green 0x7f060095
int color green_color 0x7f060096
int color greenyellow 0x7f060097
int color grey 0x7f060098
int color grgray 0x7f060099
int color header 0x7f06009a
int color help_button_view 0x7f06009b
int color help_view 0x7f06009c
int color high_light_color 0x7f06009d
int color highlighted_text_material_dark 0x7f06009e
int color highlighted_text_material_light 0x7f06009f
int color honeydew 0x7f0600a0
int color hotpink 0x7f0600a1
int color iedBackGround 0x7f0600a2
int color indianred 0x7f0600a3
int color indigo 0x7f0600a4
int color ivory 0x7f0600a5
int color khaki 0x7f0600a6
int color lavender 0x7f0600a7
int color lavenderblush 0x7f0600a8
int color lawngreen 0x7f0600a9
int color lemonchiffon 0x7f0600aa
int color lightblue 0x7f0600ab
int color lightcoral 0x7f0600ac
int color lightcyan 0x7f0600ad
int color lightgoldenrodyellow 0x7f0600ae
int color lightgray 0x7f0600af
int color lightgreen 0x7f0600b0
int color lightgrey 0x7f0600b1
int color lightpink 0x7f0600b2
int color lightsalmon 0x7f0600b3
int color lightseagreen 0x7f0600b4
int color lightskyblue 0x7f0600b5
int color lightslategray 0x7f0600b6
int color lightslategrey 0x7f0600b7
int color lightsteelblue 0x7f0600b8
int color lightyellow 0x7f0600b9
int color lime 0x7f0600ba
int color limegreen 0x7f0600bb
int color linen 0x7f0600bc
int color magenta 0x7f0600bd
int color main_back 0x7f0600be
int color maroon 0x7f0600bf
int color material_blue_grey_800 0x7f0600c0
int color material_blue_grey_900 0x7f0600c1
int color material_blue_grey_950 0x7f0600c2
int color material_deep_teal_200 0x7f0600c3
int color material_deep_teal_500 0x7f0600c4
int color material_grey_100 0x7f0600c5
int color material_grey_300 0x7f0600c6
int color material_grey_50 0x7f0600c7
int color material_grey_600 0x7f0600c8
int color material_grey_800 0x7f0600c9
int color material_grey_850 0x7f0600ca
int color material_grey_900 0x7f0600cb
int color mediumaquamarine 0x7f0600cc
int color mediumblue 0x7f0600cd
int color mediumorchid 0x7f0600ce
int color mediumpurple 0x7f0600cf
int color mediumseagreen 0x7f0600d0
int color mediumslateblue 0x7f0600d1
int color mediumspringgreen 0x7f0600d2
int color mediumturquoise 0x7f0600d3
int color mediumvioletred 0x7f0600d4
int color midnightblue 0x7f0600d5
int color mintcream 0x7f0600d6
int color mistyrose 0x7f0600d7
int color moccasin 0x7f0600d8
int color mtrl_bottom_nav_colored_item_tint 0x7f0600d9
int color mtrl_bottom_nav_item_tint 0x7f0600da
int color mtrl_btn_bg_color_disabled 0x7f0600db
int color mtrl_btn_bg_color_selector 0x7f0600dc
int color mtrl_btn_ripple_color 0x7f0600dd
int color mtrl_btn_stroke_color_selector 0x7f0600de
int color mtrl_btn_text_btn_ripple_color 0x7f0600df
int color mtrl_btn_text_color_disabled 0x7f0600e0
int color mtrl_btn_text_color_selector 0x7f0600e1
int color mtrl_btn_transparent_bg_color 0x7f0600e2
int color mtrl_chip_background_color 0x7f0600e3
int color mtrl_chip_close_icon_tint 0x7f0600e4
int color mtrl_chip_ripple_color 0x7f0600e5
int color mtrl_chip_text_color 0x7f0600e6
int color mtrl_fab_ripple_color 0x7f0600e7
int color mtrl_scrim_color 0x7f0600e8
int color mtrl_tabs_colored_ripple_color 0x7f0600e9
int color mtrl_tabs_icon_color_selector 0x7f0600ea
int color mtrl_tabs_icon_color_selector_colored 0x7f0600eb
int color mtrl_tabs_legacy_text_color_selector 0x7f0600ec
int color mtrl_tabs_ripple_color 0x7f0600ed
int color mtrl_text_btn_text_color_selector 0x7f0600ee
int color mtrl_textinput_default_box_stroke_color 0x7f0600ef
int color mtrl_textinput_disabled_color 0x7f0600f0
int color mtrl_textinput_filled_box_default_background_color 0x7f0600f1
int color mtrl_textinput_hovered_box_stroke_color 0x7f0600f2
int color navajowhite 0x7f0600f3
int color navpage 0x7f0600f4
int color navy 0x7f0600f5
int color notification_action_color_filter 0x7f0600f6
int color notification_icon_bg_color 0x7f0600f7
int color notification_material_background_media_default_color 0x7f0600f8
int color oldlace 0x7f0600f9
int color olive 0x7f0600fa
int color olivedrab 0x7f0600fb
int color orage_back 0x7f0600fc
int color orange 0x7f0600fd
int color orangered 0x7f0600fe
int color orchid 0x7f0600ff
int color palegoldenrod 0x7f060100
int color palegreen 0x7f060101
int color paleturquoise 0x7f060102
int color palevioletred 0x7f060103
int color papayawhip 0x7f060104
int color peachpuff 0x7f060105
int color peru 0x7f060106
int color phone_background 0x7f060107
int color pink 0x7f060108
int color plum 0x7f060109
int color possible_result_points 0x7f06010a
int color powderblue 0x7f06010b
int color primary_dark_material_dark 0x7f06010c
int color primary_dark_material_light 0x7f06010d
int color primary_material_dark 0x7f06010e
int color primary_material_light 0x7f06010f
int color primary_text_default_material_dark 0x7f060110
int color primary_text_default_material_light 0x7f060111
int color primary_text_disabled_material_dark 0x7f060112
int color primary_text_disabled_material_light 0x7f060113
int color purple 0x7f060114
int color red 0x7f060115
int color result_image_border 0x7f060116
int color result_minor_text 0x7f060117
int color result_points 0x7f060118
int color result_text 0x7f060119
int color result_view 0x7f06011a
int color ripple_material_dark 0x7f06011b
int color ripple_material_light 0x7f06011c
int color rosybrown 0x7f06011d
int color royalblue 0x7f06011e
int color saddlebrown 0x7f06011f
int color salmon 0x7f060120
int color sandybrown 0x7f060121
int color sbc_header_text 0x7f060122
int color sbc_header_view 0x7f060123
int color sbc_layout_view 0x7f060124
int color sbc_list_item 0x7f060125
int color sbc_page_number_text 0x7f060126
int color sbc_snippet_text 0x7f060127
int color seaShell 0x7f060128
int color seagreen 0x7f060129
int color secondary_text_default_material_dark 0x7f06012a
int color secondary_text_default_material_light 0x7f06012b
int color secondary_text_disabled_material_dark 0x7f06012c
int color secondary_text_disabled_material_light 0x7f06012d
int color share_text 0x7f06012e
int color share_view 0x7f06012f
int color sienna 0x7f060130
int color silver 0x7f060131
int color skyblue 0x7f060132
int color slateblue 0x7f060133
int color slategray 0x7f060134
int color snow 0x7f060135
int color springgreen 0x7f060136
int color status_text 0x7f060137
int color status_view 0x7f060138
int color steelblue 0x7f060139
int color switch_thumb_disabled_material_dark 0x7f06013a
int color switch_thumb_disabled_material_light 0x7f06013b
int color switch_thumb_material_dark 0x7f06013c
int color switch_thumb_material_light 0x7f06013d
int color switch_thumb_normal_material_dark 0x7f06013e
int color switch_thumb_normal_material_light 0x7f06013f
int color tan 0x7f060140
int color teal 0x7f060141
int color text_black 0x7f060142
int color text_black_light 0x7f060143
int color text_gray 0x7f060144
int color text_gray_deep 0x7f060145
int color text_gray_light 0x7f060146
int color text_orange 0x7f060147
int color text_red 0x7f060148
int color text_white 0x7f060149
int color text_yellow 0x7f06014a
int color thistle 0x7f06014b
int color title_back 0x7f06014c
int color title_background 0x7f06014d
int color title_color 0x7f06014e
int color title_leght_back 0x7f06014f
int color tomato 0x7f060150
int color tooltip_background_dark 0x7f060151
int color tooltip_background_light 0x7f060152
int color transparent 0x7f060153
int color turquoise 0x7f060154
int color viewfinder_frame 0x7f060155
int color viewfinder_laser 0x7f060156
int color viewfinder_mask 0x7f060157
int color violet 0x7f060158
int color wheat 0x7f060159
int color white 0x7f06015a
int color whitesmoke 0x7f06015b
int color yellow 0x7f06015c
int dimen abc_action_bar_content_inset_material 0x7f070001
int dimen abc_action_bar_content_inset_with_nav 0x7f070002
int dimen abc_action_bar_default_height_material 0x7f070003
int dimen abc_action_bar_default_padding_end_material 0x7f070004
int dimen abc_action_bar_default_padding_start_material 0x7f070005
int dimen abc_action_bar_elevation_material 0x7f070006
int dimen abc_action_bar_icon_vertical_padding_material 0x7f070007
int dimen abc_action_bar_overflow_padding_end_material 0x7f070008
int dimen abc_action_bar_overflow_padding_start_material 0x7f070009
int dimen abc_action_bar_progress_bar_size 0x7f07000a
int dimen abc_action_bar_stacked_max_height 0x7f07000b
int dimen abc_action_bar_stacked_tab_max_width 0x7f07000c
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f07000d
int dimen abc_action_bar_subtitle_top_margin_material 0x7f07000e
int dimen abc_action_button_min_height_material 0x7f07000f
int dimen abc_action_button_min_width_material 0x7f070010
int dimen abc_action_button_min_width_overflow_material 0x7f070011
int dimen abc_alert_dialog_button_bar_height 0x7f070012
int dimen abc_alert_dialog_button_dimen 0x7f070013
int dimen abc_button_inset_horizontal_material 0x7f070014
int dimen abc_button_inset_vertical_material 0x7f070015
int dimen abc_button_padding_horizontal_material 0x7f070016
int dimen abc_button_padding_vertical_material 0x7f070017
int dimen abc_cascading_menus_min_smallest_width 0x7f070018
int dimen abc_config_prefDialogWidth 0x7f070019
int dimen abc_control_corner_material 0x7f07001a
int dimen abc_control_inset_material 0x7f07001b
int dimen abc_control_padding_material 0x7f07001c
int dimen abc_dialog_corner_radius_material 0x7f07001d
int dimen abc_dialog_fixed_height_major 0x7f07001e
int dimen abc_dialog_fixed_height_minor 0x7f07001f
int dimen abc_dialog_fixed_width_major 0x7f070020
int dimen abc_dialog_fixed_width_minor 0x7f070021
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f070022
int dimen abc_dialog_list_padding_top_no_title 0x7f070023
int dimen abc_dialog_min_width_major 0x7f070024
int dimen abc_dialog_min_width_minor 0x7f070025
int dimen abc_dialog_padding_material 0x7f070026
int dimen abc_dialog_padding_top_material 0x7f070027
int dimen abc_dialog_title_divider_material 0x7f070028
int dimen abc_disabled_alpha_material_dark 0x7f070029
int dimen abc_disabled_alpha_material_light 0x7f07002a
int dimen abc_dropdownitem_icon_width 0x7f07002b
int dimen abc_dropdownitem_text_padding_left 0x7f07002c
int dimen abc_dropdownitem_text_padding_right 0x7f07002d
int dimen abc_edit_text_inset_bottom_material 0x7f07002e
int dimen abc_edit_text_inset_horizontal_material 0x7f07002f
int dimen abc_edit_text_inset_top_material 0x7f070030
int dimen abc_floating_window_z 0x7f070031
int dimen abc_list_item_padding_horizontal_material 0x7f070032
int dimen abc_panel_menu_list_width 0x7f070033
int dimen abc_progress_bar_height_material 0x7f070034
int dimen abc_search_view_preferred_height 0x7f070035
int dimen abc_search_view_preferred_width 0x7f070036
int dimen abc_seekbar_track_background_height_material 0x7f070037
int dimen abc_seekbar_track_progress_height_material 0x7f070038
int dimen abc_select_dialog_padding_start_material 0x7f070039
int dimen abc_switch_padding 0x7f07003a
int dimen abc_text_size_body_1_material 0x7f07003b
int dimen abc_text_size_body_2_material 0x7f07003c
int dimen abc_text_size_button_material 0x7f07003d
int dimen abc_text_size_caption_material 0x7f07003e
int dimen abc_text_size_display_1_material 0x7f07003f
int dimen abc_text_size_display_2_material 0x7f070040
int dimen abc_text_size_display_3_material 0x7f070041
int dimen abc_text_size_display_4_material 0x7f070042
int dimen abc_text_size_headline_material 0x7f070043
int dimen abc_text_size_large_material 0x7f070044
int dimen abc_text_size_medium_material 0x7f070045
int dimen abc_text_size_menu_header_material 0x7f070046
int dimen abc_text_size_menu_material 0x7f070047
int dimen abc_text_size_small_material 0x7f070048
int dimen abc_text_size_subhead_material 0x7f070049
int dimen abc_text_size_subtitle_material_toolbar 0x7f07004a
int dimen abc_text_size_title_material 0x7f07004b
int dimen abc_text_size_title_material_toolbar 0x7f07004c
int dimen activity_horizontal_margin 0x7f07004d
int dimen activity_margin_bottom 0x7f07004e
int dimen activity_margin_lelt 0x7f07004f
int dimen activity_margin_right 0x7f070050
int dimen activity_margin_top 0x7f070051
int dimen card_margin 0x7f070052
int dimen cardview_compat_inset_shadow 0x7f070053
int dimen cardview_default_elevation 0x7f070054
int dimen cardview_default_radius 0x7f070055
int dimen compat_button_inset_horizontal_material 0x7f070056
int dimen compat_button_inset_vertical_material 0x7f070057
int dimen compat_button_padding_horizontal_material 0x7f070058
int dimen compat_button_padding_vertical_material 0x7f070059
int dimen compat_control_corner_material 0x7f07005a
int dimen compat_notification_large_icon_max_height 0x7f07005b
int dimen compat_notification_large_icon_max_width 0x7f07005c
int dimen design_appbar_elevation 0x7f07005d
int dimen design_bottom_navigation_active_item_max_width 0x7f07005e
int dimen design_bottom_navigation_active_item_min_width 0x7f07005f
int dimen design_bottom_navigation_active_text_size 0x7f070060
int dimen design_bottom_navigation_elevation 0x7f070061
int dimen design_bottom_navigation_height 0x7f070062
int dimen design_bottom_navigation_icon_size 0x7f070063
int dimen design_bottom_navigation_item_max_width 0x7f070064
int dimen design_bottom_navigation_item_min_width 0x7f070065
int dimen design_bottom_navigation_margin 0x7f070066
int dimen design_bottom_navigation_shadow_height 0x7f070067
int dimen design_bottom_navigation_text_size 0x7f070068
int dimen design_bottom_sheet_modal_elevation 0x7f070069
int dimen design_bottom_sheet_peek_height_min 0x7f07006a
int dimen design_fab_border_width 0x7f07006b
int dimen design_fab_elevation 0x7f07006c
int dimen design_fab_image_size 0x7f07006d
int dimen design_fab_size_mini 0x7f07006e
int dimen design_fab_size_normal 0x7f07006f
int dimen design_fab_translation_z_hovered_focused 0x7f070070
int dimen design_fab_translation_z_pressed 0x7f070071
int dimen design_navigation_elevation 0x7f070072
int dimen design_navigation_icon_padding 0x7f070073
int dimen design_navigation_icon_size 0x7f070074
int dimen design_navigation_item_horizontal_padding 0x7f070075
int dimen design_navigation_item_icon_padding 0x7f070076
int dimen design_navigation_max_width 0x7f070077
int dimen design_navigation_padding_bottom 0x7f070078
int dimen design_navigation_separator_vertical_padding 0x7f070079
int dimen design_snackbar_action_inline_max_width 0x7f07007a
int dimen design_snackbar_background_corner_radius 0x7f07007b
int dimen design_snackbar_elevation 0x7f07007c
int dimen design_snackbar_extra_spacing_horizontal 0x7f07007d
int dimen design_snackbar_max_width 0x7f07007e
int dimen design_snackbar_min_width 0x7f07007f
int dimen design_snackbar_padding_horizontal 0x7f070080
int dimen design_snackbar_padding_vertical 0x7f070081
int dimen design_snackbar_padding_vertical_2lines 0x7f070082
int dimen design_snackbar_text_size 0x7f070083
int dimen design_tab_max_width 0x7f070084
int dimen design_tab_scrollable_min_width 0x7f070085
int dimen design_tab_text_size 0x7f070086
int dimen design_tab_text_size_2line 0x7f070087
int dimen design_textinput_caption_translate_y 0x7f070088
int dimen disabled_alpha_material_dark 0x7f070089
int dimen disabled_alpha_material_light 0x7f07008a
int dimen dp_10 0x7f07008b
int dimen dp_16 0x7f07008c
int dimen dp_2 0x7f07008d
int dimen dp_200 0x7f07008e
int dimen dp_24 0x7f07008f
int dimen dp_32 0x7f070090
int dimen dp_4 0x7f070091
int dimen dp_48 0x7f070092
int dimen dp_56 0x7f070093
int dimen dp_64 0x7f070094
int dimen dp_8 0x7f070095
int dimen enter_detaile_height 0x7f070096
int dimen enter_detaile_width 0x7f070097
int dimen fab_margin 0x7f070098
int dimen fastscroll_default_thickness 0x7f070099
int dimen fastscroll_margin 0x7f07009a
int dimen fastscroll_minimum_range 0x7f07009b
int dimen gl_text 0x7f07009c
int dimen highlight_alpha_material_colored 0x7f07009d
int dimen highlight_alpha_material_dark 0x7f07009e
int dimen highlight_alpha_material_light 0x7f07009f
int dimen hint_alpha_material_dark 0x7f0700a0
int dimen hint_alpha_material_light 0x7f0700a1
int dimen hint_pressed_alpha_material_dark 0x7f0700a2
int dimen hint_pressed_alpha_material_light 0x7f0700a3
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f0700a4
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f0700a5
int dimen item_touch_helper_swipe_escape_velocity 0x7f0700a6
int dimen mtrl_bottomappbar_fabOffsetEndMode 0x7f0700a7
int dimen mtrl_bottomappbar_fab_cradle_margin 0x7f0700a8
int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x7f0700a9
int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x7f0700aa
int dimen mtrl_bottomappbar_height 0x7f0700ab
int dimen mtrl_btn_corner_radius 0x7f0700ac
int dimen mtrl_btn_dialog_btn_min_width 0x7f0700ad
int dimen mtrl_btn_disabled_elevation 0x7f0700ae
int dimen mtrl_btn_disabled_z 0x7f0700af
int dimen mtrl_btn_elevation 0x7f0700b0
int dimen mtrl_btn_focused_z 0x7f0700b1
int dimen mtrl_btn_hovered_z 0x7f0700b2
int dimen mtrl_btn_icon_btn_padding_left 0x7f0700b3
int dimen mtrl_btn_icon_padding 0x7f0700b4
int dimen mtrl_btn_inset 0x7f0700b5
int dimen mtrl_btn_letter_spacing 0x7f0700b6
int dimen mtrl_btn_padding_bottom 0x7f0700b7
int dimen mtrl_btn_padding_left 0x7f0700b8
int dimen mtrl_btn_padding_right 0x7f0700b9
int dimen mtrl_btn_padding_top 0x7f0700ba
int dimen mtrl_btn_pressed_z 0x7f0700bb
int dimen mtrl_btn_stroke_size 0x7f0700bc
int dimen mtrl_btn_text_btn_icon_padding 0x7f0700bd
int dimen mtrl_btn_text_btn_padding_left 0x7f0700be
int dimen mtrl_btn_text_btn_padding_right 0x7f0700bf
int dimen mtrl_btn_text_size 0x7f0700c0
int dimen mtrl_btn_z 0x7f0700c1
int dimen mtrl_card_elevation 0x7f0700c2
int dimen mtrl_card_spacing 0x7f0700c3
int dimen mtrl_chip_pressed_translation_z 0x7f0700c4
int dimen mtrl_chip_text_size 0x7f0700c5
int dimen mtrl_fab_elevation 0x7f0700c6
int dimen mtrl_fab_translation_z_hovered_focused 0x7f0700c7
int dimen mtrl_fab_translation_z_pressed 0x7f0700c8
int dimen mtrl_navigation_elevation 0x7f0700c9
int dimen mtrl_navigation_item_horizontal_padding 0x7f0700ca
int dimen mtrl_navigation_item_icon_padding 0x7f0700cb
int dimen mtrl_snackbar_background_corner_radius 0x7f0700cc
int dimen mtrl_snackbar_margin 0x7f0700cd
int dimen mtrl_textinput_box_bottom_offset 0x7f0700ce
int dimen mtrl_textinput_box_corner_radius_medium 0x7f0700cf
int dimen mtrl_textinput_box_corner_radius_small 0x7f0700d0
int dimen mtrl_textinput_box_label_cutout_padding 0x7f0700d1
int dimen mtrl_textinput_box_padding_end 0x7f0700d2
int dimen mtrl_textinput_box_stroke_width_default 0x7f0700d3
int dimen mtrl_textinput_box_stroke_width_focused 0x7f0700d4
int dimen mtrl_textinput_outline_box_expanded_padding 0x7f0700d5
int dimen mtrl_toolbar_default_height 0x7f0700d6
int dimen notification_action_icon_size 0x7f0700d7
int dimen notification_action_text_size 0x7f0700d8
int dimen notification_big_circle_margin 0x7f0700d9
int dimen notification_content_margin_start 0x7f0700da
int dimen notification_large_icon_height 0x7f0700db
int dimen notification_large_icon_width 0x7f0700dc
int dimen notification_main_column_padding_top 0x7f0700dd
int dimen notification_media_narrow_margin 0x7f0700de
int dimen notification_right_icon_size 0x7f0700df
int dimen notification_right_side_padding_top 0x7f0700e0
int dimen notification_small_icon_background_padding 0x7f0700e1
int dimen notification_small_icon_size_as_large 0x7f0700e2
int dimen notification_subtext_size 0x7f0700e3
int dimen notification_top_pad 0x7f0700e4
int dimen notification_top_pad_large_text 0x7f0700e5
int dimen px_1 0x7f0700e6
int dimen sp_10 0x7f0700e7
int dimen sp_12 0x7f0700e8
int dimen sp_14 0x7f0700e9
int dimen sp_16 0x7f0700ea
int dimen sp_18 0x7f0700eb
int dimen sp_tapbottom 0x7f0700ec
int dimen sp_text1 0x7f0700ed
int dimen sp_text2 0x7f0700ee
int dimen sp_text_header 0x7f0700ef
int dimen subtitle_corner_radius 0x7f0700f0
int dimen subtitle_outline_width 0x7f0700f1
int dimen subtitle_shadow_offset 0x7f0700f2
int dimen subtitle_shadow_radius 0x7f0700f3
int dimen tooltip_corner_radius 0x7f0700f4
int dimen tooltip_horizontal_padding 0x7f0700f5
int dimen tooltip_margin 0x7f0700f6
int dimen tooltip_precise_anchor_extra_offset 0x7f0700f7
int dimen tooltip_precise_anchor_threshold 0x7f0700f8
int dimen tooltip_vertical_padding 0x7f0700f9
int dimen tooltip_y_offset_non_touch 0x7f0700fa
int dimen tooltip_y_offset_touch 0x7f0700fb
int drawable abc_ab_share_pack_mtrl_alpha 0x7f080001
int drawable abc_action_bar_item_background_material 0x7f080002
int drawable abc_btn_borderless_material 0x7f080003
int drawable abc_btn_check_material 0x7f080004
int drawable abc_btn_check_to_on_mtrl_000 0x7f080005
int drawable abc_btn_check_to_on_mtrl_015 0x7f080006
int drawable abc_btn_colored_material 0x7f080007
int drawable abc_btn_default_mtrl_shape 0x7f080008
int drawable abc_btn_radio_material 0x7f080009
int drawable abc_btn_radio_to_on_mtrl_000 0x7f08000a
int drawable abc_btn_radio_to_on_mtrl_015 0x7f08000b
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f08000c
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f08000d
int drawable abc_cab_background_internal_bg 0x7f08000e
int drawable abc_cab_background_top_material 0x7f08000f
int drawable abc_cab_background_top_mtrl_alpha 0x7f080010
int drawable abc_control_background_material 0x7f080011
int drawable abc_dialog_material_background 0x7f080012
int drawable abc_edit_text_material 0x7f080013
int drawable abc_ic_ab_back_material 0x7f080014
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f080015
int drawable abc_ic_clear_material 0x7f080016
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f080017
int drawable abc_ic_go_search_api_material 0x7f080018
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f080019
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f08001a
int drawable abc_ic_menu_overflow_material 0x7f08001b
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f08001c
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f08001d
int drawable abc_ic_menu_share_mtrl_alpha 0x7f08001e
int drawable abc_ic_search_api_material 0x7f08001f
int drawable abc_ic_star_black_16dp 0x7f080020
int drawable abc_ic_star_black_36dp 0x7f080021
int drawable abc_ic_star_black_48dp 0x7f080022
int drawable abc_ic_star_half_black_16dp 0x7f080023
int drawable abc_ic_star_half_black_36dp 0x7f080024
int drawable abc_ic_star_half_black_48dp 0x7f080025
int drawable abc_ic_voice_search_api_material 0x7f080026
int drawable abc_item_background_holo_dark 0x7f080027
int drawable abc_item_background_holo_light 0x7f080028
int drawable abc_list_divider_material 0x7f080029
int drawable abc_list_divider_mtrl_alpha 0x7f08002a
int drawable abc_list_focused_holo 0x7f08002b
int drawable abc_list_longpressed_holo 0x7f08002c
int drawable abc_list_pressed_holo_dark 0x7f08002d
int drawable abc_list_pressed_holo_light 0x7f08002e
int drawable abc_list_selector_background_transition_holo_dark 0x7f08002f
int drawable abc_list_selector_background_transition_holo_light 0x7f080030
int drawable abc_list_selector_disabled_holo_dark 0x7f080031
int drawable abc_list_selector_disabled_holo_light 0x7f080032
int drawable abc_list_selector_holo_dark 0x7f080033
int drawable abc_list_selector_holo_light 0x7f080034
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f080035
int drawable abc_popup_background_mtrl_mult 0x7f080036
int drawable abc_ratingbar_indicator_material 0x7f080037
int drawable abc_ratingbar_material 0x7f080038
int drawable abc_ratingbar_small_material 0x7f080039
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f08003a
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f08003b
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f08003c
int drawable abc_scrubber_primary_mtrl_alpha 0x7f08003d
int drawable abc_scrubber_track_mtrl_alpha 0x7f08003e
int drawable abc_seekbar_thumb_material 0x7f08003f
int drawable abc_seekbar_tick_mark_material 0x7f080040
int drawable abc_seekbar_track_material 0x7f080041
int drawable abc_spinner_mtrl_am_alpha 0x7f080042
int drawable abc_spinner_textfield_background_material 0x7f080043
int drawable abc_switch_thumb_material 0x7f080044
int drawable abc_switch_track_mtrl_alpha 0x7f080045
int drawable abc_tab_indicator_material 0x7f080046
int drawable abc_tab_indicator_mtrl_alpha 0x7f080047
int drawable abc_text_cursor_material 0x7f080048
int drawable abc_text_select_handle_left_mtrl_dark 0x7f080049
int drawable abc_text_select_handle_left_mtrl_light 0x7f08004a
int drawable abc_text_select_handle_middle_mtrl_dark 0x7f08004b
int drawable abc_text_select_handle_middle_mtrl_light 0x7f08004c
int drawable abc_text_select_handle_right_mtrl_dark 0x7f08004d
int drawable abc_text_select_handle_right_mtrl_light 0x7f08004e
int drawable abc_textfield_activated_mtrl_alpha 0x7f08004f
int drawable abc_textfield_default_mtrl_alpha 0x7f080050
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f080051
int drawable abc_textfield_search_default_mtrl_alpha 0x7f080052
int drawable abc_textfield_search_material 0x7f080053
int drawable abc_vector_test 0x7f080054
int drawable actionbar_tab_indicator 0x7f080055
int drawable avd_hide_password 0x7f080056
int drawable avd_hide_password_1 0x7f080057
int drawable avd_hide_password_2 0x7f080058
int drawable avd_hide_password_3 0x7f080059
int drawable avd_show_password 0x7f08005a
int drawable avd_show_password_1 0x7f08005b
int drawable avd_show_password_2 0x7f08005c
int drawable avd_show_password_3 0x7f08005d
int drawable background_material 0x7f08005e
int drawable binfile 0x7f08005f
int drawable border 0x7f080060
int drawable butterfly 0x7f080061
int drawable camera 0x7f080062
int drawable check_table 0x7f080063
int drawable cmp_two 0x7f080064
int drawable cmp_twoperson 0x7f080065
int drawable cmp_updown 0x7f080066
int drawable common_button 0x7f080067
int drawable design_bottom_navigation_item_background 0x7f080068
int drawable design_fab_background 0x7f080069
int drawable design_ic_visibility 0x7f08006a
int drawable design_ic_visibility_off 0x7f08006b
int drawable design_password_eye 0x7f08006c
int drawable design_snackbar_background 0x7f08006d
int drawable earth01 0x7f08006e
int drawable earth02 0x7f08006f
int drawable earth03 0x7f080070
int drawable file_icon_default 0x7f080071
int drawable filterbtn2_style 0x7f080072
int drawable fivestar_yelow 0x7f080073
int drawable flag_01 0x7f080074
int drawable flag_02 0x7f080075
int drawable flag_filterbtn_style 0x7f080076
int drawable flag_tabbar 0x7f080077
int drawable flower 0x7f080078
int drawable folder 0x7f080079
int drawable folder_1 0x7f08007a
int drawable gridview_style 0x7f08007b
int drawable ic_android_black_24dp 0x7f08007c
int drawable ic_delete_black 0x7f08007d
int drawable ic_drafts_black 0x7f08007e
int drawable ic_email_black 0x7f08007f
int drawable ic_enter 0x7f080080
int drawable ic_enter2 0x7f080081
int drawable ic_error_black 0x7f080082
int drawable ic_home_black 0x7f080083
int drawable ic_inbox_black 0x7f080084
int drawable ic_launcher 0x7f080085
int drawable ic_launcher0 0x7f080086
int drawable ic_mtrl_chip_checked_black 0x7f080087
int drawable ic_mtrl_chip_checked_circle 0x7f080088
int drawable ic_mtrl_chip_close_circle 0x7f080089
int drawable ic_ok 0x7f08008a
int drawable ic_send_black 0x7f08008b
int drawable ic_star_black 0x7f08008c
int drawable ic_star_rate_black 0x7f08008d
int drawable ic_svg_close 0x7f08008e
int drawable icon_favo_p 0x7f08008f
int drawable icon_user_p 0x7f080090
int drawable icon_write_p 0x7f080091
int drawable ied_fc_close_rt 0x7f080092
int drawable ied_fc_open_rt 0x7f080093
int drawable ied_fc_open_tr 0x7f080094
int drawable ied_lc_r 0x7f080095
int drawable ied_lc_rt 0x7f080096
int drawable ied_lc_t 0x7f080097
int drawable ied_lc_tr 0x7f080098
int drawable ied_na 0x7f080099
int drawable ied_pair_r 0x7f08009a
int drawable ied_pair_t 0x7f08009b
int drawable ied_rj45 0x7f08009c
int drawable ied_sc_rt 0x7f08009d
int drawable ied_sc_tr 0x7f08009e
int drawable ied_st_rt 0x7f08009f
int drawable ied_st_tr 0x7f0800a0
int drawable item_selector 0x7f0800a1
int drawable item_table_spliter01 0x7f0800a2
int drawable item_table_spliter02 0x7f0800a3
int drawable listview_style 0x7f0800a4
int drawable map_bigger 0x7f0800a5
int drawable map_filled_size 0x7f0800a6
int drawable map_filled_size_h 0x7f0800a7
int drawable map_original_size 0x7f0800a8
int drawable map_original_size_w 0x7f0800a9
int drawable map_smaller 0x7f0800aa
int drawable mtrl_snackbar_background 0x7f0800ab
int drawable mtrl_tabs_default_indicator 0x7f0800ac
int drawable muchlight 0x7f0800ad
int drawable navigate_next_48px 0x7f0800ae
int drawable navigation_empty_icon 0x7f0800af
int drawable next 0x7f0800b0
int drawable nextbtn_style 0x7f0800b1
int drawable notification_action_background 0x7f0800b2
int drawable notification_bg 0x7f0800b3
int drawable notification_bg_low 0x7f0800b4
int drawable notification_bg_low_normal 0x7f0800b5
int drawable notification_bg_low_pressed 0x7f0800b6
int drawable notification_bg_normal 0x7f0800b7
int drawable notification_bg_normal_pressed 0x7f0800b8
int drawable notification_icon_background 0x7f0800b9
int drawable notification_template_icon_bg 0x7f0800ba
int drawable notification_template_icon_low_bg 0x7f0800bb
int drawable notification_tile_bg 0x7f0800bc
int drawable notify_panel_notification_icon_bg 0x7f0800bd
int drawable pic_left_arrow 0x7f0800be
int drawable profile 0x7f0800bf
int drawable scan_light 0x7f0800c0
int drawable scdfile 0x7f0800c1
int drawable selector_elevation 0x7f0800c2
int drawable setting01 0x7f0800c3
int drawable setting_blue 0x7f0800c4
int drawable setting_feather 0x7f0800c5
int drawable setting_light 0x7f0800c6
int drawable setting_pen 0x7f0800c7
int drawable shape_circle_blue 0x7f0800c8
int drawable smile01 0x7f0800c9
int drawable start 0x7f0800ca
int drawable state_press_white 0x7f0800cb
int drawable switch_fc_down_rt 0x7f0800cc
int drawable switch_fc_down_tr 0x7f0800cd
int drawable switch_fc_up_rt 0x7f0800ce
int drawable switch_fc_up_tr 0x7f0800cf
int drawable switch_lc_down_r 0x7f0800d0
int drawable switch_lc_down_rt 0x7f0800d1
int drawable switch_lc_down_t 0x7f0800d2
int drawable switch_lc_down_tr 0x7f0800d3
int drawable switch_lc_up_r 0x7f0800d4
int drawable switch_lc_up_rt 0x7f0800d5
int drawable switch_lc_up_t 0x7f0800d6
int drawable switch_lc_up_tr 0x7f0800d7
int drawable switch_on 0x7f0800d8
int drawable switch_pair_r_down 0x7f0800d9
int drawable switch_pair_r_up 0x7f0800da
int drawable switch_pair_t_down 0x7f0800db
int drawable switch_pair_t_up 0x7f0800dc
int drawable switch_rj45_down 0x7f0800dd
int drawable switch_rj45_up 0x7f0800de
int drawable switch_sc_down_rt 0x7f0800df
int drawable switch_sc_down_tr 0x7f0800e0
int drawable switch_sc_up_rt 0x7f0800e1
int drawable switch_sc_up_tr 0x7f0800e2
int drawable switch_st_down_rt 0x7f0800e3
int drawable switch_st_down_tr 0x7f0800e4
int drawable switch_st_up_rt 0x7f0800e5
int drawable switch_st_up_tr 0x7f0800e6
int drawable swithc_lc_down_r 0x7f0800e7
int drawable tab_selected 0x7f0800e8
int drawable tab_selected_pressed 0x7f0800e9
int drawable tab_unselected 0x7f0800ea
int drawable tab_unselected_pressed 0x7f0800eb
int drawable taginfo_textcolor_selector 0x7f0800ec
int drawable text_color_selector_menu 0x7f0800ed
int drawable text_x 0x7f0800ee
int drawable tooltip_frame_dark 0x7f0800ef
int drawable tooltip_frame_light 0x7f0800f0
int drawable tree_ec 0x7f0800f1
int drawable tree_ex 0x7f0800f2
int drawable visualization 0x7f0800f3
int drawable visualization1 0x7f0800f4
int drawable wifi_icon 0x7f0800f5
int drawable xmlfile 0x7f0800f6
int id APPID_tv 0x7f0b0001
int id DIAdr 0x7f0b0002
int id DIAdr_tv 0x7f0b0003
int id DIDesc 0x7f0b0004
int id DIDesc_tv 0x7f0b0005
int id DOAdr 0x7f0b0006
int id DOAdr_tv 0x7f0b0007
int id DODesc 0x7f0b0008
int id DODesc_tv 0x7f0b0009
int id DelNotMchTV 0x7f0b000a
int id NO_DEBUG 0x7f0b000b
int id Num_tv 0x7f0b000c
int id SHOW_ALL 0x7f0b000d
int id SHOW_PATH 0x7f0b000e
int id SHOW_PROGRESS 0x7f0b000f
int id Txt_Path 0x7f0b0010
int id WhiteSpaceText 0x7f0b0011
int id about 0x7f0b0012
int id accelerate 0x7f0b0013
int id action0 0x7f0b0014
int id action_bar 0x7f0b0015
int id action_bar_activity_content 0x7f0b0016
int id action_bar_container 0x7f0b0017
int id action_bar_root 0x7f0b0018
int id action_bar_spinner 0x7f0b0019
int id action_bar_subtitle 0x7f0b001a
int id action_bar_title 0x7f0b001b
int id action_container 0x7f0b001c
int id action_context_bar 0x7f0b001d
int id action_divider 0x7f0b001e
int id action_image 0x7f0b001f
int id action_menu_divider 0x7f0b0020
int id action_menu_presenter 0x7f0b0021
int id action_mode_bar 0x7f0b0022
int id action_mode_bar_stub 0x7f0b0023
int id action_mode_close_button 0x7f0b0024
int id action_text 0x7f0b0025
int id actions 0x7f0b0026
int id activity_add_src_by_others 0x7f0b0027
int id activity_chooser_view_content 0x7f0b0028
int id activity_main 0x7f0b0029
int id activity_second 0x7f0b002a
int id add 0x7f0b002b
int id alertTitle 0x7f0b002c
int id aligned 0x7f0b002d
int id all 0x7f0b002e
int id always 0x7f0b002f
int id animateToEnd 0x7f0b0030
int id animateToStart 0x7f0b0031
int id asConfigured 0x7f0b0032
int id async 0x7f0b0033
int id auto 0x7f0b0034
int id autoComplete 0x7f0b0035
int id autoCompleteToEnd 0x7f0b0036
int id autoCompleteToStart 0x7f0b0037
int id auto_focus 0x7f0b0038
int id baseline 0x7f0b0039
int id beginning 0x7f0b003a
int id blocking 0x7f0b003b
int id bottom 0x7f0b003c
int id bounce 0x7f0b003d
int id btnGyTestZoomingView 0x7f0b003e
int id btnIedBackView 0x7f0b003f
int id btnOpenCubicleBackPanelView 0x7f0b0040
int id btnOpenCubicleView 0x7f0b0041
int id btnOpenIEDsWholeCircuitView 0x7f0b0042
int id btnVRview 0x7f0b0043
int id btn_Up 0x7f0b0044
int id btn_closeTagInfo 0x7f0b0045
int id btn_jumpToODF 0x7f0b0046
int id btn_jumpToOptFiberViewDlg 0x7f0b0047
int id btn_jumpToTheOtherDevice 0x7f0b0048
int id btn_jumpToTheWholeCircle 0x7f0b0049
int id buttonPanel 0x7f0b004a
int id cancel_action 0x7f0b004b
int id center 0x7f0b004c
int id center_horizontal 0x7f0b004d
int id center_vertical 0x7f0b004e
int id chain 0x7f0b004f
int id checkbox 0x7f0b0050
int id chnCnt_tv 0x7f0b0051
int id chronometer 0x7f0b0052
int id clip_horizontal 0x7f0b0053
int id clip_vertical 0x7f0b0054
int id collapseActionView 0x7f0b0055
int id companyTitle 0x7f0b0056
int id company_text 0x7f0b0057
int id container 0x7f0b0058
int id content 0x7f0b0059
int id contentPanel 0x7f0b005a
int id coordinator 0x7f0b005b
int id copyright_text 0x7f0b005c
int id cos 0x7f0b005d
int id ctrlHeaderTV 0x7f0b005e
int id cubicle 0x7f0b005f
int id cubicleBackPanelView 0x7f0b0060
int id cubicleFrontView 0x7f0b0061
int id cubicleName 0x7f0b0062
int id cubicle_detail 0x7f0b0063
int id cubicle_other 0x7f0b0064
int id cubicle_other_detail 0x7f0b0065
int id curSCD 0x7f0b0066
int id curSCDNameTV 0x7f0b0067
int id custom 0x7f0b0068
int id customPanel 0x7f0b0069
int id datasetBtnGroup 0x7f0b006a
int id dataset_tv 0x7f0b006b
int id decelerate 0x7f0b006c
int id decelerateAndComplete 0x7f0b006d
int id decode 0x7f0b006e
int id decode_failed 0x7f0b006f
int id decode_succeeded 0x7f0b0070
int id decor_content_parent 0x7f0b0071
int id default_activity_button 0x7f0b0072
int id deltaRelative 0x7f0b0073
int id desc 0x7f0b0074
int id descTV 0x7f0b0075
int id design_bottom_sheet 0x7f0b0076
int id design_menu_item_action_area 0x7f0b0077
int id design_menu_item_action_area_stub 0x7f0b0078
int id design_menu_item_text 0x7f0b0079
int id design_navigation_view 0x7f0b007a
int id detail_btn 0x7f0b007b
int id detail_tv 0x7f0b007c
int id device 0x7f0b007d
int id deviceId 0x7f0b007e
int id device_back 0x7f0b007f
int id device_back_view 0x7f0b0080
int id device_enc 0x7f0b0081
int id disableHome 0x7f0b0082
int id dragDown 0x7f0b0083
int id dragEnd 0x7f0b0084
int id dragLeft 0x7f0b0085
int id dragRight 0x7f0b0086
int id dragStart 0x7f0b0087
int id dragUp 0x7f0b0088
int id draggableTV 0x7f0b0089
int id drawer_layout 0x7f0b008a
int id easeIn 0x7f0b008b
int id easeInOut 0x7f0b008c
int id easeOut 0x7f0b008d
int id edit_circle 0x7f0b008e
int id edit_query 0x7f0b008f
int id email 0x7f0b0090
int id encode_failed 0x7f0b0091
int id encode_succeeded 0x7f0b0092
int id end 0x7f0b0093
int id end_padder 0x7f0b0094
int id enterAlways 0x7f0b0095
int id enterAlwaysCollapsed 0x7f0b0096
int id exitUntilCollapsed 0x7f0b0097
int id expand_activities_button 0x7f0b0098
int id expanded_menu 0x7f0b0099
int id export 0x7f0b009a
int id filePath_textview 0x7f0b009b
int id files_fragment_layout 0x7f0b009c
int id filesize_textview 0x7f0b009d
int id fill 0x7f0b009e
int id fill_horizontal 0x7f0b009f
int id fill_vertical 0x7f0b00a0
int id filled 0x7f0b00a1
int id filterBtn_01 0x7f0b00a2
int id filterBtn_02 0x7f0b00a3
int id filterEdit 0x7f0b00a4
int id filterTV 0x7f0b00a5
int id filter_layout 0x7f0b00a6
int id filter_radioGroup1 0x7f0b00a7
int id filter_radioGroup2 0x7f0b00a8
int id find_file_listview 0x7f0b00a9
int id find_result_label 0x7f0b00aa
int id fixed 0x7f0b00ab
int id fl_my_container 0x7f0b00ac
int id fl_zxing_container 0x7f0b00ad
int id flashlight_btn 0x7f0b00ae
int id flip 0x7f0b00af
int id flowtab_layout 0x7f0b00b0
int id forever 0x7f0b00b1
int id fragment1 0x7f0b00b2
int id fragment_layout 0x7f0b00b3
int id frame 0x7f0b00b4
int id ghost_view 0x7f0b00b5
int id glView 0x7f0b00b6
int id gone 0x7f0b00b7
int id gridLayout 0x7f0b00b8
int id gridview 0x7f0b00b9
int id group_divider 0x7f0b00ba
int id gyTestZoomingView 0x7f0b00bb
int id header_layout 0x7f0b00bc
int id home 0x7f0b00bd
int id homeAsUp 0x7f0b00be
int id honorRequest 0x7f0b00bf
int id horizontalScrollView1 0x7f0b00c0
int id hzView 0x7f0b00c1
int id icon 0x7f0b00c2
int id icon_File 0x7f0b00c3
int id icon_group 0x7f0b00c4
int id id_treenode_icon 0x7f0b00c5
int id id_treenode_label 0x7f0b00c6
int id iedCbListView 0x7f0b00c7
int id iedlist_layout 0x7f0b00c8
int id iedsWholeCircuitView 0x7f0b00c9
int id iedsWholeCircuitView2 0x7f0b00ca
int id iedtabbar_radioGroup 0x7f0b00cb
int id iedtree_lv 0x7f0b00cc
int id ifRoom 0x7f0b00cd
int id ignore 0x7f0b00ce
int id ignoreRequest 0x7f0b00cf
int id image 0x7f0b00d0
int id imageView1 0x7f0b00d1
int id imageView2 0x7f0b00d2
int id imageview 0x7f0b00d3
int id img_qr_code 0x7f0b00d4
int id info 0x7f0b00d5
int id intAdrTV 0x7f0b00d6
int id invisible 0x7f0b00d7
int id isCandidate 0x7f0b00d8
int id italic 0x7f0b00d9
int id item_touch_helper_previous_elevation 0x7f0b00da
int id jumpToEnd 0x7f0b00db
int id jumpToStart 0x7f0b00dc
int id labeled 0x7f0b00dd
int id largeLabel 0x7f0b00de
int id launch_product_query 0x7f0b00df
int id layout 0x7f0b00e0
int id left 0x7f0b00e1
int id left_back 0x7f0b00e2
int id line1 0x7f0b00e3
int id line3 0x7f0b00e4
int id linear 0x7f0b00e5
int id listLayout 0x7f0b00e6
int id listMode 0x7f0b00e7
int id list_item 0x7f0b00e8
int id list_view 0x7f0b00e9
int id list_view_substation 0x7f0b00ea
int id listheader_linear 0x7f0b00eb
int id listview 0x7f0b00ec
int id listview2 0x7f0b00ed
int id listviewHeader 0x7f0b00ee
int id listview_cytlMsg 0x7f0b00ef
int id listview_dataset 0x7f0b00f0
int id listview_inputlist 0x7f0b00f1
int id listview_outvchn 0x7f0b00f2
int id ll 0x7f0b00f3
int id ll_attached_features 0x7f0b00f4
int id ll_right 0x7f0b00f5
int id ll_zoom_bar 0x7f0b00f6
int id locale_path_list 0x7f0b00f7
int id login 0x7f0b00f8
int id lv_content 0x7f0b00f9
int id lv_vlan_table 0x7f0b00fa
int id mPath 0x7f0b00fb
int id mView 0x7f0b00fc
int id mapBigger 0x7f0b00fd
int id mapBigger0 0x7f0b00fe
int id mapOriginal 0x7f0b00ff
int id mapOriginal0 0x7f0b0100
int id mapOriginalFilled 0x7f0b0101
int id mapSmaller 0x7f0b0102
int id mapSmaller0 0x7f0b0103
int id masked 0x7f0b0104
int id media_actions 0x7f0b0105
int id menu1 0x7f0b0106
int id menu2 0x7f0b0107
int id menu3 0x7f0b0108
int id menu_about 0x7f0b0109
int id menu_all 0x7f0b010a
int id menu_item_one 0x7f0b010b
int id menu_item_search_view 0x7f0b010c
int id menu_item_two 0x7f0b010d
int id menu_layout_left 0x7f0b010e
int id menu_list 0x7f0b010f
int id menu_map 0x7f0b0110
int id message 0x7f0b0111
int id middle 0x7f0b0112
int id mini 0x7f0b0113
int id moresetting_button 0x7f0b0114
int id motion_base 0x7f0b0115
int id mtrl_child_content_container 0x7f0b0116
int id mtrl_internal_children_alpha_tag 0x7f0b0117
int id multiply 0x7f0b0118
int id name 0x7f0b0119
int id navigation_header_container 0x7f0b011a
int id never 0x7f0b011b
int id nextBtn 0x7f0b011c
int id none 0x7f0b011d
int id normal 0x7f0b011e
int id notification_background 0x7f0b011f
int id notification_main_column 0x7f0b0120
int id notification_main_column_container 0x7f0b0121
int id num 0x7f0b0122
int id numTV 0x7f0b0123
int id odfListView 0x7f0b0124
int id odfLogicView 0x7f0b0125
int id odfName 0x7f0b0126
int id odfView 0x7f0b0127
int id operateBar 0x7f0b0128
int id opticalEndPort 0x7f0b0129
int id opticalEndUnitName 0x7f0b012a
int id opticalStartPort 0x7f0b012b
int id opticalStartUnitName 0x7f0b012c
int id otherOdf 0x7f0b012d
int id outCtrl_tv 0x7f0b012e
int id outline 0x7f0b012f
int id packed 0x7f0b0130
int id pager 0x7f0b0131
int id parallax 0x7f0b0132
int id parent 0x7f0b0133
int id parentPanel 0x7f0b0134
int id parentRelative 0x7f0b0135
int id parent_matrix 0x7f0b0136
int id path 0x7f0b0137
int id pathRelative 0x7f0b0138
int id percent 0x7f0b0139
int id pin 0x7f0b013a
int id port_info_list 0x7f0b013b
int id position 0x7f0b013c
int id postLayout 0x7f0b013d
int id preBtn 0x7f0b013e
int id preview_view 0x7f0b013f
int id productTitle_01 0x7f0b0140
int id product_text 0x7f0b0141
int id profile_image 0x7f0b0142
int id progressBar 0x7f0b0143
int id progress_circular 0x7f0b0144
int id progress_horizontal 0x7f0b0145
int id qhlView 0x7f0b0146
int id qr_code_info 0x7f0b0147
int id qr_code_listView 0x7f0b0148
int id quit 0x7f0b0149
int id radio 0x7f0b014a
int id radioGroup 0x7f0b014b
int id readDB 0x7f0b014c
int id rectangles 0x7f0b014d
int id recyclerView 0x7f0b014e
int id region 0x7f0b014f
int id region_detail 0x7f0b0150
int id region_other 0x7f0b0151
int id region_other_detail 0x7f0b0152
int id restart_preview 0x7f0b0153
int id return_scan_result 0x7f0b0154
int id reverseSawtooth 0x7f0b0155
int id right 0x7f0b0156
int id right_icon 0x7f0b0157
int id right_side 0x7f0b0158
int id right_tv 0x7f0b0159
int id roundProgressBar 0x7f0b015a
int id save_image_matrix 0x7f0b015b
int id save_non_transition_alpha 0x7f0b015c
int id save_scale_type 0x7f0b015d
int id sawtooth 0x7f0b015e
int id screen 0x7f0b015f
int id scroll 0x7f0b0160
int id scrollIndicatorDown 0x7f0b0161
int id scrollIndicatorUp 0x7f0b0162
int id scrollView 0x7f0b0163
int id scrollable 0x7f0b0164
int id search_badge 0x7f0b0165
int id search_bar 0x7f0b0166
int id search_book_contents_failed 0x7f0b0167
int id search_book_contents_succeeded 0x7f0b0168
int id search_button 0x7f0b0169
int id search_close_btn 0x7f0b016a
int id search_edit_frame 0x7f0b016b
int id search_go_btn 0x7f0b016c
int id search_mag_icon 0x7f0b016d
int id search_plate 0x7f0b016e
int id search_src_text 0x7f0b016f
int id search_voice_btn 0x7f0b0170
int id select_dialog_listview 0x7f0b0171
int id selected 0x7f0b0172
int id shortcut 0x7f0b0173
int id showCustom 0x7f0b0174
int id showHome 0x7f0b0175
int id showTitle 0x7f0b0176
int id sin 0x7f0b0177
int id smallLabel 0x7f0b0178
int id snackbar_action 0x7f0b0179
int id snackbar_text 0x7f0b017a
int id snap 0x7f0b017b
int id spacer 0x7f0b017c
int id spinner 0x7f0b017d
int id spinner_input 0x7f0b017e
int id spinner_outCtrl 0x7f0b017f
int id spline 0x7f0b0180
int id split_action_bar 0x7f0b0181
int id spread 0x7f0b0182
int id spread_inside 0x7f0b0183
int id square 0x7f0b0184
int id src_atop 0x7f0b0185
int id src_in 0x7f0b0186
int id src_over 0x7f0b0187
int id standard 0x7f0b0188
int id start 0x7f0b0189
int id startHorizontal 0x7f0b018a
int id startVertical 0x7f0b018b
int id staticLayout 0x7f0b018c
int id staticPostLayout 0x7f0b018d
int id status_bar_latest_event_content 0x7f0b018e
int id statusbarutil_fake_status_bar_view 0x7f0b018f
int id statusbarutil_translucent_view 0x7f0b0190
int id step 0x7f0b0191
int id stop 0x7f0b0192
int id stretch 0x7f0b0193
int id submenuarrow 0x7f0b0194
int id submit_area 0x7f0b0195
int id switchConnView1 0x7f0b0196
int id tabMode 0x7f0b0197
int id tab_title 0x7f0b0198
int id tagInfoTitleBar 0x7f0b0199
int id tag_transition_group 0x7f0b019a
int id tag_unhandled_key_event_manager 0x7f0b019b
int id tag_unhandled_key_listeners 0x7f0b019c
int id tb_toolbar 0x7f0b019d
int id text 0x7f0b019e
int id text2 0x7f0b019f
int id textSpacerNoButtons 0x7f0b01a0
int id textSpacerNoTitle 0x7f0b01a1
int id textView 0x7f0b01a2
int id textView1 0x7f0b01a3
int id text_input_password_toggle 0x7f0b01a4
int id textinput_counter 0x7f0b01a5
int id textinput_error 0x7f0b01a6
int id textinput_helper_text 0x7f0b01a7
int id time 0x7f0b01a8
int id title 0x7f0b01a9
int id titleDividerNoCustom 0x7f0b01aa
int id title_layout 0x7f0b01ab
int id title_lln 0x7f0b01ac
int id title_rel 0x7f0b01ad
int id title_template 0x7f0b01ae
int id title_topLevel 0x7f0b01af
int id tl_tab 0x7f0b01b0
int id toolbar 0x7f0b01b1
int id toolbar1 0x7f0b01b2
int id toolbar2 0x7f0b01b3
int id top 0x7f0b01b4
int id topPanel 0x7f0b01b5
int id touch_outside 0x7f0b01b6
int id transition_current_scene 0x7f0b01b7
int id transition_layout_save 0x7f0b01b8
int id transition_position 0x7f0b01b9
int id transition_scene_layoutid_cache 0x7f0b01ba
int id transition_transform 0x7f0b01bb
int id triangle 0x7f0b01bc
int id tuzhi_ll 0x7f0b01bd
int id tv 0x7f0b01be
int id tv1 0x7f0b01bf
int id tv2 0x7f0b01c0
int id tv3 0x7f0b01c1
int id tv4 0x7f0b01c2
int id tv6 0x7f0b01c3
int id tvNum 0x7f0b01c4
int id tv_FCDA 0x7f0b01c5
int id tv_appId 0x7f0b01c6
int id tv_cable_name 0x7f0b01c7
int id tv_cable_num_core_no 0x7f0b01c8
int id tv_center 0x7f0b01c9
int id tv_content 0x7f0b01ca
int id tv_desc 0x7f0b01cb
int id tv_direction 0x7f0b01cc
int id tv_direction1 0x7f0b01cd
int id tv_direction2 0x7f0b01ce
int id tv_div 0x7f0b01cf
int id tv_end_ied_name 0x7f0b01d0
int id tv_extRef 0x7f0b01d1
int id tv_fileName 0x7f0b01d2
int id tv_index 0x7f0b01d3
int id tv_left 0x7f0b01d4
int id tv_num 0x7f0b01d5
int id tv_opline 0x7f0b01d6
int id tv_port1 0x7f0b01d7
int id tv_port2 0x7f0b01d8
int id tv_port_info1 0x7f0b01d9
int id tv_port_info2 0x7f0b01da
int id tv_right 0x7f0b01db
int id tv_signal_type1 0x7f0b01dc
int id tv_signal_type2 0x7f0b01dd
int id tv_start_ied_name 0x7f0b01de
int id tv_switch_port 0x7f0b01df
int id tv_tag_gb_fr 0x7f0b01e0
int id tv_tag_gb_no 0x7f0b01e1
int id tv_tag_gb_to 0x7f0b01e2
int id tv_tips 0x7f0b01e3
int id tv_title 0x7f0b01e4
int id tv_type 0x7f0b01e5
int id tv_unitName 0x7f0b01e6
int id tv_virLinkinfo 0x7f0b01e7
int id tv_vlan_id 0x7f0b01e8
int id tvparamAdr 0x7f0b01e9
int id uniform 0x7f0b01ea
int id unlabeled 0x7f0b01eb
int id up 0x7f0b01ec
int id useLogo 0x7f0b01ed
int id username 0x7f0b01ee
int id value 0x7f0b01ef
int id viewContent 0x7f0b01f0
int id viewGroup 0x7f0b01f1
int id view_offset_helper 0x7f0b01f2
int id viewfinder_view 0x7f0b01f3
int id viewpager 0x7f0b01f4
int id virRealCircuitView 0x7f0b01f5
int id virRealCircuitView_v2 0x7f0b01f6
int id virView 0x7f0b01f7
int id virtal_real_circuit_view_v2 0x7f0b01f8
int id visible 0x7f0b01f9
int id vp_content 0x7f0b01fa
int id vrCircuitView 0x7f0b01fb
int id vrCircuitView001 0x7f0b01fc
int id vrCircuitView002 0x7f0b01fd
int id wifi_tv 0x7f0b01fe
int id withText 0x7f0b01ff
int id wlView 0x7f0b0200
int id wrap 0x7f0b0201
int id wrap_content 0x7f0b0202
int id xsView 0x7f0b0203
int integer abc_config_activityDefaultDur 0x7f0c0001
int integer abc_config_activityShortDur 0x7f0c0002
int integer app_bar_elevation_anim_duration 0x7f0c0003
int integer bottom_sheet_slide_duration 0x7f0c0004
int integer cancel_button_image_alpha 0x7f0c0005
int integer config_tooltipAnimTime 0x7f0c0006
int integer design_snackbar_text_max_lines 0x7f0c0007
int integer design_tab_indicator_anim_duration_ms 0x7f0c0008
int integer hide_password_duration 0x7f0c0009
int integer mtrl_btn_anim_delay_ms 0x7f0c000a
int integer mtrl_btn_anim_duration_ms 0x7f0c000b
int integer mtrl_chip_anim_duration 0x7f0c000c
int integer mtrl_tab_indicator_anim_duration_ms 0x7f0c000d
int integer show_password_duration 0x7f0c000e
int integer status_bar_notification_info_maxnum 0x7f0c000f
int interpolator mtrl_fast_out_linear_in 0x7f0d0001
int interpolator mtrl_fast_out_slow_in 0x7f0d0002
int interpolator mtrl_linear 0x7f0d0003
int interpolator mtrl_linear_out_slow_in 0x7f0d0004
int layout abc_action_bar_title_item 0x7f0e0001
int layout abc_action_bar_up_container 0x7f0e0002
int layout abc_action_bar_view_list_nav_layout 0x7f0e0003
int layout abc_action_menu_item_layout 0x7f0e0004
int layout abc_action_menu_layout 0x7f0e0005
int layout abc_action_mode_bar 0x7f0e0006
int layout abc_action_mode_close_item_material 0x7f0e0007
int layout abc_activity_chooser_view 0x7f0e0008
int layout abc_activity_chooser_view_list_item 0x7f0e0009
int layout abc_alert_dialog_button_bar_material 0x7f0e000a
int layout abc_alert_dialog_material 0x7f0e000b
int layout abc_alert_dialog_title_material 0x7f0e000c
int layout abc_cascading_menu_item_layout 0x7f0e000d
int layout abc_dialog_title_material 0x7f0e000e
int layout abc_expanded_menu_layout 0x7f0e000f
int layout abc_list_menu_item_checkbox 0x7f0e0010
int layout abc_list_menu_item_icon 0x7f0e0011
int layout abc_list_menu_item_layout 0x7f0e0012
int layout abc_list_menu_item_radio 0x7f0e0013
int layout abc_popup_menu_header_item_layout 0x7f0e0014
int layout abc_popup_menu_item_layout 0x7f0e0015
int layout abc_screen_content_include 0x7f0e0016
int layout abc_screen_simple 0x7f0e0017
int layout abc_screen_simple_overlay_action_mode 0x7f0e0018
int layout abc_screen_toolbar 0x7f0e0019
int layout abc_search_dropdown_item_icons_2line 0x7f0e001a
int layout abc_search_view 0x7f0e001b
int layout abc_select_dialog_material 0x7f0e001c
int layout abc_tooltip 0x7f0e001d
int layout about 0x7f0e001e
int layout activity_cable_and_core_layout 0x7f0e001f
int layout activity_center_node 0x7f0e0020
int layout activity_cubicle_back_panel_view_show 0x7f0e0021
int layout activity_cubicle_list 0x7f0e0022
int layout activity_cubicle_view_show 0x7f0e0023
int layout activity_cubicle_visual 0x7f0e0024
int layout activity_dev_files_show 0x7f0e0025
int layout activity_gl 0x7f0e0026
int layout activity_gl_connect 0x7f0e0027
int layout activity_glwl_detail 0x7f0e0028
int layout activity_glwl_label 0x7f0e0029
int layout activity_glwl_label_dlg 0x7f0e002a
int layout activity_gq 0x7f0e002b
int layout activity_gy_test_zooming 0x7f0e002c
int layout activity_ied_back_panel_view 0x7f0e002d
int layout activity_ied_visual 0x7f0e002e
int layout activity_ied_visual_new 0x7f0e002f
int layout activity_iedonly 0x7f0e0030
int layout activity_ieds_whole_circuit 0x7f0e0031
int layout activity_login 0x7f0e0032
int layout activity_main 0x7f0e0033
int layout activity_odf 0x7f0e0034
int layout activity_odf_logic 0x7f0e0035
int layout activity_odf_view 0x7f0e0036
int layout activity_optical_fiber_visual 0x7f0e0037
int layout activity_optical_info 0x7f0e0038
int layout activity_plans_files_show 0x7f0e0039
int layout activity_region_list 0x7f0e003a
int layout activity_second 0x7f0e003b
int layout activity_substation 0x7f0e003c
int layout activity_switch_vlan_show 0x7f0e003d
int layout activity_tab_layout_top 0x7f0e003e
int layout activity_test 0x7f0e003f
int layout activity_top_bar_base 0x7f0e0040
int layout activity_vir_ports_links 0x7f0e0041
int layout activity_vrcircle_view 0x7f0e0042
int layout activity_wl 0x7f0e0043
int layout camera 0x7f0e0044
int layout content_fragment 0x7f0e0045
int layout content_vir_ports_links 0x7f0e0046
int layout ctrlmsg_layout 0x7f0e0047
int layout custom_tab 0x7f0e0048
int layout dataset_list_layout 0x7f0e0049
int layout design_bottom_navigation_item 0x7f0e004a
int layout design_bottom_sheet_dialog 0x7f0e004b
int layout design_layout_snackbar 0x7f0e004c
int layout design_layout_snackbar_include 0x7f0e004d
int layout design_layout_tab_icon 0x7f0e004e
int layout design_layout_tab_text 0x7f0e004f
int layout design_menu_item_action_area 0x7f0e0050
int layout design_navigation_item 0x7f0e0051
int layout design_navigation_item_header 0x7f0e0052
int layout design_navigation_item_separator 0x7f0e0053
int layout design_navigation_item_subheader 0x7f0e0054
int layout design_navigation_menu 0x7f0e0055
int layout design_navigation_menu_item 0x7f0e0056
int layout design_text_input_password_icon 0x7f0e0057
int layout device_backpanel_layout 0x7f0e0058
int layout device_backpanel_view_layout 0x7f0e0059
int layout device_portabout_frament 0x7f0e005a
int layout find_sdcard_file_item_layout 0x7f0e005b
int layout find_sdcard_file_layout 0x7f0e005c
int layout fragment_capture 0x7f0e005d
int layout fragment_odf_logic 0x7f0e005e
int layout fragment_odf_new_view 0x7f0e005f
int layout fragment_odf_tier_new_view 0x7f0e0060
int layout fragment_odf_tier_new_view_include 0x7f0e0061
int layout fragment_odf_tier_view 0x7f0e0062
int layout fragment_odf_view 0x7f0e0063
int layout fragment_tab_content 0x7f0e0064
int layout fragment_tab_list 0x7f0e0065
int layout gl_info 0x7f0e0066
int layout gl_title 0x7f0e0067
int layout gl_title_1 0x7f0e0068
int layout gq_info 0x7f0e0069
int layout grid_cell 0x7f0e006a
int layout header 0x7f0e006b
int layout ied_baseactivity_layout 0x7f0e006c
int layout importscl_activity 0x7f0e006d
int layout include_filter_layout 0x7f0e006e
int layout include_flowtab_layout 0x7f0e006f
int layout include_svctrl 0x7f0e0070
int layout include_toolbar 0x7f0e0071
int layout inputlist_layout 0x7f0e0072
int layout layout1 0x7f0e0073
int layout layout2 0x7f0e0074
int layout layout3 0x7f0e0075
int layout layout4 0x7f0e0076
int layout layout_baseactivity_ied 0x7f0e0077
int layout layout_common_title 0x7f0e0078
int layout layout_datasetv2 0x7f0e0079
int layout layout_iedactivity 0x7f0e007a
int layout layout_iedlist 0x7f0e007b
int layout layout_iedmsg 0x7f0e007c
int layout layout_inputs 0x7f0e007d
int layout layout_item_filepath 0x7f0e007e
int layout layout_svrecvv2 0x7f0e007f
int layout layout_svsend 0x7f0e0080
int layout layout_svsendv2 0x7f0e0081
int layout layout_title 0x7f0e0082
int layout layout_title_cable 0x7f0e0083
int layout layout_visual_ied 0x7f0e0084
int layout list_header_cubicle 0x7f0e0085
int layout list_item 0x7f0e0086
int layout list_item_cubicle 0x7f0e0087
int layout list_item_cubicle_fill 0x7f0e0088
int layout list_item_cubicle_filld 0x7f0e0089
int layout list_item_view 0x7f0e008a
int layout list_item_vlantable 0x7f0e008b
int layout list_row 0x7f0e008c
int layout middle_ide_layout 0x7f0e008d
int layout mtrl_layout_snackbar 0x7f0e008e
int layout mtrl_layout_snackbar_include 0x7f0e008f
int layout my_camera 0x7f0e0090
int layout notification_action 0x7f0e0091
int layout notification_action_tombstone 0x7f0e0092
int layout notification_media_action 0x7f0e0093
int layout notification_media_cancel_action 0x7f0e0094
int layout notification_template_big_media 0x7f0e0095
int layout notification_template_big_media_custom 0x7f0e0096
int layout notification_template_big_media_narrow 0x7f0e0097
int layout notification_template_big_media_narrow_custom 0x7f0e0098
int layout notification_template_custom_big 0x7f0e0099
int layout notification_template_icon_group 0x7f0e009a
int layout notification_template_lines_media 0x7f0e009b
int layout notification_template_media 0x7f0e009c
int layout notification_template_media_custom 0x7f0e009d
int layout notification_template_part_chronometer 0x7f0e009e
int layout notification_template_part_time 0x7f0e009f
int layout odf_item 0x7f0e00a0
int layout opt_help 0x7f0e00a1
int layout outdataset_list_layout 0x7f0e00a2
int layout outvchn_list_layout 0x7f0e00a3
int layout popupview 0x7f0e00a4
int layout port_ctrlbolck_layout 0x7f0e00a5
int layout port_ctrlbolck_virlink 0x7f0e00a6
int layout port_odf_link_item 0x7f0e00a7
int layout progress_layout 0x7f0e00a8
int layout qr_code_info_item 0x7f0e00a9
int layout qr_code_info_item_gb 0x7f0e00aa
int layout qr_code_info_layout 0x7f0e00ab
int layout radio_gr_2 0x7f0e00ac
int layout radio_gr_3 0x7f0e00ad
int layout row_chnsend 0x7f0e00ae
int layout row_ctrl_chnmsg 0x7f0e00af
int layout row_ctrlmsg 0x7f0e00b0
int layout row_dataset 0x7f0e00b1
int layout row_datasetv2 0x7f0e00b2
int layout row_iedlist 0x7f0e00b3
int layout row_iedmsg 0x7f0e00b4
int layout row_inputlist 0x7f0e00b5
int layout row_outdataset 0x7f0e00b6
int layout row_outvchn 0x7f0e00b7
int layout row_rptlist 0x7f0e00b8
int layout row_svcrtl 0x7f0e00b9
int layout row_svcrtlv2 0x7f0e00ba
int layout row_svrecvv2 0x7f0e00bb
int layout rptlist_layout 0x7f0e00bc
int layout sample_cubicle_view 0x7f0e00bd
int layout select_dialog_item_material 0x7f0e00be
int layout select_dialog_multichoice_material 0x7f0e00bf
int layout select_dialog_singlechoice_material 0x7f0e00c0
int layout spcd_activity_main 0x7f0e00c1
int layout support_simple_spinner_dropdown_item 0x7f0e00c2
int layout switch_conn_layout 0x7f0e00c3
int layout td_fragment2 0x7f0e00c4
int layout toolbar 0x7f0e00c5
int layout vir_ports_lnk_childview 0x7f0e00c6
int layout vir_ports_lnk_childview_dlg 0x7f0e00c7
int layout virportslinks_dlgstyle_layout 0x7f0e00c8
int layout virportslinks_layout 0x7f0e00c9
int layout visualscl_activity 0x7f0e00ca
int layout visualscl_activity2 0x7f0e00cb
int layout visualscl_activity3 0x7f0e00cc
int layout vr_circuit_layout 0x7f0e00cd
int layout wifitip_layout 0x7f0e00ce
int menu menu_ied_activity 0x7f0f0001
int menu menu_main 0x7f0f0002
int menu toobar 0x7f0f0003
int mipmap ic_launcher 0x7f100001
int mipmap ic_launcher_round 0x7f100002
int mipmap indicator_input_error 0x7f100003
int mipmap pic_arrow_down 0x7f100004
int mipmap pic_arrow_down_1 0x7f100005
int mipmap pic_left_arrow 0x7f100006
int mipmap pic_more 0x7f100007
int mipmap pic_photo 0x7f100008
int raw auto_substation 0x7f130001
int raw beep 0x7f130002
int raw spcd_20190521 0x7f130003
int string ATIED 0x7f140001
int string BOOLEAN 0x7f140002
int string CRC 0x7f140003
int string DIAdr 0x7f140004
int string DIDesc 0x7f140005
int string DO 0x7f140006
int string DOAdr 0x7f140007
int string DODesc 0x7f140008
int string Dbpos 0x7f140009
int string DlgIfSave 0x7f14000a
int string Quality 0x7f14000b
int string Timestamp 0x7f14000c
int string abc_action_bar_home_description 0x7f14000d
int string abc_action_bar_home_description_format 0x7f14000e
int string abc_action_bar_home_subtitle_description_format 0x7f14000f
int string abc_action_bar_up_description 0x7f140010
int string abc_action_menu_overflow_description 0x7f140011
int string abc_action_mode_done 0x7f140012
int string abc_activity_chooser_view_see_all 0x7f140013
int string abc_activitychooserview_choose_application 0x7f140014
int string abc_capital_off 0x7f140015
int string abc_capital_on 0x7f140016
int string abc_font_family_body_1_material 0x7f140017
int string abc_font_family_body_2_material 0x7f140018
int string abc_font_family_button_material 0x7f140019
int string abc_font_family_caption_material 0x7f14001a
int string abc_font_family_display_1_material 0x7f14001b
int string abc_font_family_display_2_material 0x7f14001c
int string abc_font_family_display_3_material 0x7f14001d
int string abc_font_family_display_4_material 0x7f14001e
int string abc_font_family_headline_material 0x7f14001f
int string abc_font_family_menu_material 0x7f140020
int string abc_font_family_subhead_material 0x7f140021
int string abc_font_family_title_material 0x7f140022
int string abc_menu_alt_shortcut_label 0x7f140023
int string abc_menu_ctrl_shortcut_label 0x7f140024
int string abc_menu_delete_shortcut_label 0x7f140025
int string abc_menu_enter_shortcut_label 0x7f140026
int string abc_menu_function_shortcut_label 0x7f140027
int string abc_menu_meta_shortcut_label 0x7f140028
int string abc_menu_shift_shortcut_label 0x7f140029
int string abc_menu_space_shortcut_label 0x7f14002a
int string abc_menu_sym_shortcut_label 0x7f14002b
int string abc_prepend_shortcut_label 0x7f14002c
int string abc_search_hint 0x7f14002d
int string abc_searchview_description_clear 0x7f14002e
int string abc_searchview_description_query 0x7f14002f
int string abc_searchview_description_search 0x7f140030
int string abc_searchview_description_submit 0x7f140031
int string abc_searchview_description_voice 0x7f140032
int string abc_shareactionprovider_share_with 0x7f140033
int string abc_shareactionprovider_share_with_application 0x7f140034
int string abc_toolbar_collapse_description 0x7f140035
int string action_settings 0x7f140036
int string all 0x7f140037
int string all1 0x7f140038
int string app_name 0x7f140039
int string app_name2 0x7f14003a
int string app_namejiangxi 0x7f14003b
int string app_title 0x7f14003c
int string appbar_scrolling_view_behavior 0x7f14003d
int string bottom_sheet_behavior 0x7f14003e
int string busU 0x7f14003f
int string cantFindTheFile 0x7f140040
int string character_counter_content_description 0x7f140041
int string character_counter_pattern 0x7f140042
int string chnCnt 0x7f140043
int string clear 0x7f140044
int string close 0x7f140045
int string companyMsg 0x7f140046
int string compareFile 0x7f140047
int string construction 0x7f140048
int string content 0x7f140049
int string crc 0x7f14004a
int string curFile 0x7f14004b
int string curSCD 0x7f14004c
int string current 0x7f14004d
int string daType 0x7f14004e
int string dataInput 0x7f14004f
int string dataset 0x7f140050
int string datasetDesc 0x7f140051
int string desc 0x7f140052
int string detail 0x7f140053
int string detailMsg 0x7f140054
int string deviceType 0x7f140055
int string device_model 0x7f140056
int string devide 0x7f140057
int string doTake 0x7f140058
int string doiDesc 0x7f140059
int string exit_tips 0x7f14005a
int string fab_transformation_scrim_behavior 0x7f14005b
int string fab_transformation_sheet_behavior 0x7f14005c
int string filter 0x7f14005d
int string footer_navigation_tag 0x7f14005e
int string gap 0x7f14005f
int string gse_receive 0x7f140060
int string gse_send 0x7f140061
int string hello_blank_fragment 0x7f140062
int string hello_world 0x7f140063
int string hide_bottom_view_on_scroll_behavior 0x7f140064
int string ied_des 0x7f140065
int string ied_name 0x7f140066
int string ifReadLastSetting 0x7f140067
int string importFile 0x7f140068
int string importscl_activity_title 0x7f140069
int string intAdr 0x7f14006a
int string intent_action_consistencycheck_activity 0x7f14006b
int string intent_action_importscl_activity 0x7f14006c
int string intent_action_sclcmp_activity 0x7f14006d
int string intent_action_sclvisual_activity 0x7f14006e
int string intent_action_staticscheck_activity 0x7f14006f
int string intent_action_virtualcheck_activity 0x7f140070
int string intent_action_xmldisplay_activity 0x7f140071
int string ip1 0x7f140072
int string ip2 0x7f140073
int string issue 0x7f140074
int string item 0x7f140075
int string lnDesc 0x7f140076
int string main_activity_title 0x7f140077
int string manufacturer 0x7f140078
int string measure 0x7f140079
int string measureI 0x7f14007a
int string midI 0x7f14007b
int string midU 0x7f14007c
int string more_setting 0x7f14007d
int string move_animation_tag 0x7f14007e
int string mtrl_chip_close_icon_content_description 0x7f14007f
int string name 0x7f140080
int string next 0x7f140081
int string next2 0x7f140082
int string no 0x7f140083
int string num 0x7f140084
int string other 0x7f140085
int string outCtrl 0x7f140086
int string paramAdr 0x7f140087
int string parseFailTips 0x7f140088
int string parseFileList 0x7f140089
int string parseIng 0x7f14008a
int string parseOver 0x7f14008b
int string parseScl_tips 0x7f14008c
int string parseScl_tips2 0x7f14008d
int string passageCnt 0x7f14008e
int string password_toggle_content_description 0x7f14008f
int string path_password_eye 0x7f140090
int string path_password_eye_mask_strike_through 0x7f140091
int string path_password_eye_mask_visible 0x7f140092
int string path_password_strike_through 0x7f140093
int string phase 0x7f140094
int string previous 0x7f140095
int string previous2 0x7f140096
int string productMsg 0x7f140097
int string progress 0x7f140098
int string promtion 0x7f140099
int string protect 0x7f14009a
int string protectI 0x7f14009b
int string read_ini_file 0x7f14009c
int string receive 0x7f14009d
int string sampling 0x7f14009e
int string saveScl_tips 0x7f14009f
int string saveScl_tips2 0x7f1400a0
int string save_ini_file 0x7f1400a1
int string searchResult 0x7f1400a2
int string search_menu_title 0x7f1400a3
int string send 0x7f1400a4
int string status_bar_notification_info_overflow 0x7f1400a5
int string str_odf_view 0x7f1400a6
int string str_opposite_dev 0x7f1400a7
int string str_phy_whole_cir_view 0x7f1400a8
int string str_tag_view 0x7f1400a9
int string sv_receive 0x7f1400aa
int string sv_send 0x7f1400ab
int string title_activity_cubicle_back_panel_view_show 0x7f1400ac
int string title_activity_top_bar_base 0x7f1400ad
int string title_activity_vir_ports_links 0x7f1400ae
int string toolBar1_input 0x7f1400af
int string toolBar1_svs 0x7f1400b0
int string toolBar2_input 0x7f1400b1
int string toolBar2_svs 0x7f1400b2
int string toolBar3_svs 0x7f1400b3
int string type 0x7f1400b4
int string typeH 0x7f1400b5
int string version 0x7f1400b6
int string vertage 0x7f1400b7
int string voltage 0x7f1400b8
int string voltageLv 0x7f1400b9
int string waitForData 0x7f1400ba
int string yes 0x7f1400bb
int string zeroOrder 0x7f1400bc
int style AlertDialog_AppCompat 0x7f150001
int style AlertDialog_AppCompat_Light 0x7f150002
int style AnimationFromButtom 0x7f150003
int style AnimationFromTop 0x7f150004
int style AnimationUpPopup 0x7f150005
int style Animation_AppCompat_Dialog 0x7f150006
int style Animation_AppCompat_DropDownUp 0x7f150007
int style Animation_AppCompat_Tooltip 0x7f150008
int style Animation_Design_BottomSheetDialog 0x7f150009
int style AppBaseTheme 0x7f15000a
int style AppTheme 0x7f15000b
int style AppThemeLNoAction 0x7f15000c
int style AppTheme_ActionBar 0x7f15000d
int style AppTheme_AppBarOverlay 0x7f15000e
int style AppTheme_NoActionBar 0x7f15000f
int style AppTheme_PopupOverlay 0x7f150010
int style Base_AlertDialog_AppCompat 0x7f150011
int style Base_AlertDialog_AppCompat_Light 0x7f150012
int style Base_Animation_AppCompat_Dialog 0x7f150013
int style Base_Animation_AppCompat_DropDownUp 0x7f150014
int style Base_Animation_AppCompat_Tooltip 0x7f150015
int style Base_CardView 0x7f150016
int style Base_DialogWindowTitleBackground_AppCompat 0x7f150017
int style Base_DialogWindowTitle_AppCompat 0x7f150018
int style Base_TextAppearance_AppCompat 0x7f150019
int style Base_TextAppearance_AppCompat_Body1 0x7f15001a
int style Base_TextAppearance_AppCompat_Body2 0x7f15001b
int style Base_TextAppearance_AppCompat_Button 0x7f15001c
int style Base_TextAppearance_AppCompat_Caption 0x7f15001d
int style Base_TextAppearance_AppCompat_Display1 0x7f15001e
int style Base_TextAppearance_AppCompat_Display2 0x7f15001f
int style Base_TextAppearance_AppCompat_Display3 0x7f150020
int style Base_TextAppearance_AppCompat_Display4 0x7f150021
int style Base_TextAppearance_AppCompat_Headline 0x7f150022
int style Base_TextAppearance_AppCompat_Inverse 0x7f150023
int style Base_TextAppearance_AppCompat_Large 0x7f150024
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f150025
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f150026
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f150027
int style Base_TextAppearance_AppCompat_Medium 0x7f150028
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f150029
int style Base_TextAppearance_AppCompat_Menu 0x7f15002a
int style Base_TextAppearance_AppCompat_SearchResult 0x7f15002b
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f15002c
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f15002d
int style Base_TextAppearance_AppCompat_Small 0x7f15002e
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f15002f
int style Base_TextAppearance_AppCompat_Subhead 0x7f150030
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f150031
int style Base_TextAppearance_AppCompat_Title 0x7f150032
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f150033
int style Base_TextAppearance_AppCompat_Tooltip 0x7f150034
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f150035
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f150036
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f150037
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f150038
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f150039
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f15003a
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f15003b
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f15003c
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f15003d
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f15003e
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f15003f
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f150040
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f150041
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f150042
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f150043
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f150044
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f150045
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f150046
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f150047
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f150048
int style Base_ThemeOverlay_AppCompat 0x7f150049
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f15004a
int style Base_ThemeOverlay_AppCompat_Dark 0x7f15004b
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f15004c
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f15004d
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f15004e
int style Base_ThemeOverlay_AppCompat_Light 0x7f15004f
int style Base_ThemeOverlay_MaterialComponents_Dialog 0x7f150050
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f150051
int style Base_Theme_AppCompat 0x7f150052
int style Base_Theme_AppCompat_CompactMenu 0x7f150053
int style Base_Theme_AppCompat_Dialog 0x7f150054
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f150055
int style Base_Theme_AppCompat_Dialog_Alert 0x7f150056
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f150057
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f150058
int style Base_Theme_AppCompat_Light 0x7f150059
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f15005a
int style Base_Theme_AppCompat_Light_Dialog 0x7f15005b
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f15005c
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f15005d
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f15005e
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f15005f
int style Base_Theme_MaterialComponents 0x7f150060
int style Base_Theme_MaterialComponents_Bridge 0x7f150061
int style Base_Theme_MaterialComponents_CompactMenu 0x7f150062
int style Base_Theme_MaterialComponents_Dialog 0x7f150063
int style Base_Theme_MaterialComponents_DialogWhenLarge 0x7f150064
int style Base_Theme_MaterialComponents_Dialog_Alert 0x7f150065
int style Base_Theme_MaterialComponents_Dialog_FixedSize 0x7f150066
int style Base_Theme_MaterialComponents_Dialog_MinWidth 0x7f150067
int style Base_Theme_MaterialComponents_Light 0x7f150068
int style Base_Theme_MaterialComponents_Light_Bridge 0x7f150069
int style Base_Theme_MaterialComponents_Light_DarkActionBar 0x7f15006a
int style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f15006b
int style Base_Theme_MaterialComponents_Light_Dialog 0x7f15006c
int style Base_Theme_MaterialComponents_Light_DialogWhenLarge 0x7f15006d
int style Base_Theme_MaterialComponents_Light_Dialog_Alert 0x7f15006e
int style Base_Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f15006f
int style Base_Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f150070
int style Base_V11_ThemeOverlay_AppCompat_Dialog 0x7f150071
int style Base_V11_Theme_AppCompat_Dialog 0x7f150072
int style Base_V11_Theme_AppCompat_Light_Dialog 0x7f150073
int style Base_V12_Widget_AppCompat_AutoCompleteTextView 0x7f150074
int style Base_V12_Widget_AppCompat_EditText 0x7f150075
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog 0x7f150076
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f150077
int style Base_V14_Theme_MaterialComponents 0x7f150078
int style Base_V14_Theme_MaterialComponents_Bridge 0x7f150079
int style Base_V14_Theme_MaterialComponents_Dialog 0x7f15007a
int style Base_V14_Theme_MaterialComponents_Light 0x7f15007b
int style Base_V14_Theme_MaterialComponents_Light_Bridge 0x7f15007c
int style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f15007d
int style Base_V14_Theme_MaterialComponents_Light_Dialog 0x7f15007e
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f15007f
int style Base_V21_Theme_AppCompat 0x7f150080
int style Base_V21_Theme_AppCompat_Dialog 0x7f150081
int style Base_V21_Theme_AppCompat_Light 0x7f150082
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f150083
int style Base_V22_Theme_AppCompat 0x7f150084
int style Base_V22_Theme_AppCompat_Light 0x7f150085
int style Base_V23_Theme_AppCompat 0x7f150086
int style Base_V23_Theme_AppCompat_Light 0x7f150087
int style Base_V26_Theme_AppCompat 0x7f150088
int style Base_V26_Theme_AppCompat_Light 0x7f150089
int style Base_V26_Widget_AppCompat_Toolbar 0x7f15008a
int style Base_V28_Theme_AppCompat 0x7f15008b
int style Base_V28_Theme_AppCompat_Light 0x7f15008c
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f15008d
int style Base_V7_Theme_AppCompat 0x7f15008e
int style Base_V7_Theme_AppCompat_Dialog 0x7f15008f
int style Base_V7_Theme_AppCompat_Light 0x7f150090
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f150091
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f150092
int style Base_V7_Widget_AppCompat_EditText 0x7f150093
int style Base_V7_Widget_AppCompat_Toolbar 0x7f150094
int style Base_Widget_AppCompat_ActionBar 0x7f150095
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f150096
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f150097
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f150098
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f150099
int style Base_Widget_AppCompat_ActionButton 0x7f15009a
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f15009b
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f15009c
int style Base_Widget_AppCompat_ActionMode 0x7f15009d
int style Base_Widget_AppCompat_ActivityChooserView 0x7f15009e
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f15009f
int style Base_Widget_AppCompat_Button 0x7f1500a0
int style Base_Widget_AppCompat_ButtonBar 0x7f1500a1
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f1500a2
int style Base_Widget_AppCompat_Button_Borderless 0x7f1500a3
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f1500a4
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f1500a5
int style Base_Widget_AppCompat_Button_Colored 0x7f1500a6
int style Base_Widget_AppCompat_Button_Small 0x7f1500a7
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f1500a8
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f1500a9
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f1500aa
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f1500ab
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f1500ac
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f1500ad
int style Base_Widget_AppCompat_EditText 0x7f1500ae
int style Base_Widget_AppCompat_ImageButton 0x7f1500af
int style Base_Widget_AppCompat_Light_ActionBar 0x7f1500b0
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f1500b1
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f1500b2
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f1500b3
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f1500b4
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f1500b5
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f1500b6
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f1500b7
int style Base_Widget_AppCompat_ListMenuView 0x7f1500b8
int style Base_Widget_AppCompat_ListPopupWindow 0x7f1500b9
int style Base_Widget_AppCompat_ListView 0x7f1500ba
int style Base_Widget_AppCompat_ListView_DropDown 0x7f1500bb
int style Base_Widget_AppCompat_ListView_Menu 0x7f1500bc
int style Base_Widget_AppCompat_PopupMenu 0x7f1500bd
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f1500be
int style Base_Widget_AppCompat_PopupWindow 0x7f1500bf
int style Base_Widget_AppCompat_ProgressBar 0x7f1500c0
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f1500c1
int style Base_Widget_AppCompat_RatingBar 0x7f1500c2
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f1500c3
int style Base_Widget_AppCompat_RatingBar_Small 0x7f1500c4
int style Base_Widget_AppCompat_SearchView 0x7f1500c5
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f1500c6
int style Base_Widget_AppCompat_SeekBar 0x7f1500c7
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f1500c8
int style Base_Widget_AppCompat_Spinner 0x7f1500c9
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f1500ca
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f1500cb
int style Base_Widget_AppCompat_Toolbar 0x7f1500cc
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f1500cd
int style Base_Widget_Design_AppBarLayout 0x7f1500ce
int style Base_Widget_Design_TabLayout 0x7f1500cf
int style Base_Widget_MaterialComponents_Chip 0x7f1500d0
int style Base_Widget_MaterialComponents_TextInputEditText 0x7f1500d1
int style Base_Widget_MaterialComponents_TextInputLayout 0x7f1500d2
int style CardView 0x7f1500d3
int style CardView_Dark 0x7f1500d4
int style CardView_Light 0x7f1500d5
int style CustomActionBarTheme 0x7f1500d6
int style DialogStyleInActivity 0x7f1500d7
int style FirHeaderTVStyle 0x7f1500d8
int style HeaderTVStyle 0x7f1500d9
int style HeaderTVStyle0 0x7f1500da
int style ListTVStyle 0x7f1500db
int style MyActionBar 0x7f1500dc
int style MyActionBarTabText 0x7f1500dd
int style MyActionBarTabs 0x7f1500de
int style MyActionBarTitleText 0x7f1500df
int style Platform_AppCompat 0x7f1500e0
int style Platform_AppCompat_Light 0x7f1500e1
int style Platform_MaterialComponents 0x7f1500e2
int style Platform_MaterialComponents_Dialog 0x7f1500e3
int style Platform_MaterialComponents_Light 0x7f1500e4
int style Platform_MaterialComponents_Light_Dialog 0x7f1500e5
int style Platform_ThemeOverlay_AppCompat 0x7f1500e6
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f1500e7
int style Platform_ThemeOverlay_AppCompat_Light 0x7f1500e8
int style Platform_V11_AppCompat 0x7f1500e9
int style Platform_V11_AppCompat_Light 0x7f1500ea
int style Platform_V14_AppCompat 0x7f1500eb
int style Platform_V14_AppCompat_Light 0x7f1500ec
int style Platform_V21_AppCompat 0x7f1500ed
int style Platform_V21_AppCompat_Light 0x7f1500ee
int style Platform_V25_AppCompat 0x7f1500ef
int style Platform_V25_AppCompat_Light 0x7f1500f0
int style Platform_Widget_AppCompat_Spinner 0x7f1500f1
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f1500f2
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f1500f3
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f1500f4
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f1500f5
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f1500f6
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f1500f7
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f1500f8
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f1500f9
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f1500fa
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f1500fb
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f1500fc
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f1500fd
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f1500fe
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f1500ff
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f150100
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f150101
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f150102
int style TabBar_Button 0x7f150103
int style TabBar_TV 0x7f150104
int style TabStyle 0x7f150105
int style TextAppearance_AppCompat 0x7f150106
int style TextAppearance_AppCompat_Body1 0x7f150107
int style TextAppearance_AppCompat_Body2 0x7f150108
int style TextAppearance_AppCompat_Button 0x7f150109
int style TextAppearance_AppCompat_Caption 0x7f15010a
int style TextAppearance_AppCompat_Display1 0x7f15010b
int style TextAppearance_AppCompat_Display2 0x7f15010c
int style TextAppearance_AppCompat_Display3 0x7f15010d
int style TextAppearance_AppCompat_Display4 0x7f15010e
int style TextAppearance_AppCompat_Headline 0x7f15010f
int style TextAppearance_AppCompat_Inverse 0x7f150110
int style TextAppearance_AppCompat_Large 0x7f150111
int style TextAppearance_AppCompat_Large_Inverse 0x7f150112
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f150113
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f150114
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f150115
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f150116
int style TextAppearance_AppCompat_Medium 0x7f150117
int style TextAppearance_AppCompat_Medium_Inverse 0x7f150118
int style TextAppearance_AppCompat_Menu 0x7f150119
int style TextAppearance_AppCompat_Notification 0x7f15011a
int style TextAppearance_AppCompat_Notification_Info 0x7f15011b
int style TextAppearance_AppCompat_Notification_Info_Media 0x7f15011c
int style TextAppearance_AppCompat_Notification_Line2 0x7f15011d
int style TextAppearance_AppCompat_Notification_Line2_Media 0x7f15011e
int style TextAppearance_AppCompat_Notification_Media 0x7f15011f
int style TextAppearance_AppCompat_Notification_Time 0x7f150120
int style TextAppearance_AppCompat_Notification_Time_Media 0x7f150121
int style TextAppearance_AppCompat_Notification_Title 0x7f150122
int style TextAppearance_AppCompat_Notification_Title_Media 0x7f150123
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f150124
int style TextAppearance_AppCompat_SearchResult_Title 0x7f150125
int style TextAppearance_AppCompat_Small 0x7f150126
int style TextAppearance_AppCompat_Small_Inverse 0x7f150127
int style TextAppearance_AppCompat_Subhead 0x7f150128
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f150129
int style TextAppearance_AppCompat_Title 0x7f15012a
int style TextAppearance_AppCompat_Title_Inverse 0x7f15012b
int style TextAppearance_AppCompat_Tooltip 0x7f15012c
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f15012d
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f15012e
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f15012f
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f150130
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f150131
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f150132
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f150133
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f150134
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f150135
int style TextAppearance_AppCompat_Widget_Button 0x7f150136
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f150137
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f150138
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f150139
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f15013a
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f15013b
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f15013c
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f15013d
int style TextAppearance_AppCompat_Widget_Switch 0x7f15013e
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f15013f
int style TextAppearance_Compat_Notification 0x7f150140
int style TextAppearance_Compat_Notification_Info 0x7f150141
int style TextAppearance_Compat_Notification_Info_Media 0x7f150142
int style TextAppearance_Compat_Notification_Line2 0x7f150143
int style TextAppearance_Compat_Notification_Line2_Media 0x7f150144
int style TextAppearance_Compat_Notification_Media 0x7f150145
int style TextAppearance_Compat_Notification_Time 0x7f150146
int style TextAppearance_Compat_Notification_Time_Media 0x7f150147
int style TextAppearance_Compat_Notification_Title 0x7f150148
int style TextAppearance_Compat_Notification_Title_Media 0x7f150149
int style TextAppearance_Design_CollapsingToolbar_Expanded 0x7f15014a
int style TextAppearance_Design_Counter 0x7f15014b
int style TextAppearance_Design_Counter_Overflow 0x7f15014c
int style TextAppearance_Design_Error 0x7f15014d
int style TextAppearance_Design_HelperText 0x7f15014e
int style TextAppearance_Design_Hint 0x7f15014f
int style TextAppearance_Design_Snackbar_Message 0x7f150150
int style TextAppearance_Design_Tab 0x7f150151
int style TextAppearance_MaterialComponents_Body1 0x7f150152
int style TextAppearance_MaterialComponents_Body2 0x7f150153
int style TextAppearance_MaterialComponents_Button 0x7f150154
int style TextAppearance_MaterialComponents_Caption 0x7f150155
int style TextAppearance_MaterialComponents_Chip 0x7f150156
int style TextAppearance_MaterialComponents_Headline1 0x7f150157
int style TextAppearance_MaterialComponents_Headline2 0x7f150158
int style TextAppearance_MaterialComponents_Headline3 0x7f150159
int style TextAppearance_MaterialComponents_Headline4 0x7f15015a
int style TextAppearance_MaterialComponents_Headline5 0x7f15015b
int style TextAppearance_MaterialComponents_Headline6 0x7f15015c
int style TextAppearance_MaterialComponents_Overline 0x7f15015d
int style TextAppearance_MaterialComponents_Subtitle1 0x7f15015e
int style TextAppearance_MaterialComponents_Subtitle2 0x7f15015f
int style TextAppearance_MaterialComponents_Tab 0x7f150160
int style TextAppearance_StatusBar_EventContent 0x7f150161
int style TextAppearance_StatusBar_EventContent_Info 0x7f150162
int style TextAppearance_StatusBar_EventContent_Line2 0x7f150163
int style TextAppearance_StatusBar_EventContent_Time 0x7f150164
int style TextAppearance_StatusBar_EventContent_Title 0x7f150165
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f150166
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f150167
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f150168
int style TextStyle 0x7f150169
int style ThemeOverlay_AppCompat 0x7f15016a
int style ThemeOverlay_AppCompat_ActionBar 0x7f15016b
int style ThemeOverlay_AppCompat_Dark 0x7f15016c
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f15016d
int style ThemeOverlay_AppCompat_Dialog 0x7f15016e
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f15016f
int style ThemeOverlay_AppCompat_Light 0x7f150170
int style ThemeOverlay_MaterialComponents 0x7f150171
int style ThemeOverlay_MaterialComponents_ActionBar 0x7f150172
int style ThemeOverlay_MaterialComponents_Dark 0x7f150173
int style ThemeOverlay_MaterialComponents_Dark_ActionBar 0x7f150174
int style ThemeOverlay_MaterialComponents_Dialog 0x7f150175
int style ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f150176
int style ThemeOverlay_MaterialComponents_Light 0x7f150177
int style ThemeOverlay_MaterialComponents_TextInputEditText 0x7f150178
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox 0x7f150179
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f15017a
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox 0x7f15017b
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f15017c
int style Theme_AppCompat 0x7f15017d
int style Theme_AppCompat_CompactMenu 0x7f15017e
int style Theme_AppCompat_DayNight 0x7f15017f
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f150180
int style Theme_AppCompat_DayNight_Dialog 0x7f150181
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f150182
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f150183
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f150184
int style Theme_AppCompat_DayNight_NoActionBar 0x7f150185
int style Theme_AppCompat_Dialog 0x7f150186
int style Theme_AppCompat_DialogWhenLarge 0x7f150187
int style Theme_AppCompat_Dialog_Alert 0x7f150188
int style Theme_AppCompat_Dialog_MinWidth 0x7f150189
int style Theme_AppCompat_Light 0x7f15018a
int style Theme_AppCompat_Light_DarkActionBar 0x7f15018b
int style Theme_AppCompat_Light_Dialog 0x7f15018c
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f15018d
int style Theme_AppCompat_Light_Dialog_Alert 0x7f15018e
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f15018f
int style Theme_AppCompat_Light_NoActionBar 0x7f150190
int style Theme_AppCompat_NoActionBar 0x7f150191
int style Theme_Design 0x7f150192
int style Theme_Design_BottomSheetDialog 0x7f150193
int style Theme_Design_Light 0x7f150194
int style Theme_Design_Light_BottomSheetDialog 0x7f150195
int style Theme_Design_Light_NoActionBar 0x7f150196
int style Theme_Design_NoActionBar 0x7f150197
int style Theme_MaterialComponents 0x7f150198
int style Theme_MaterialComponents_BottomSheetDialog 0x7f150199
int style Theme_MaterialComponents_Bridge 0x7f15019a
int style Theme_MaterialComponents_CompactMenu 0x7f15019b
int style Theme_MaterialComponents_Dialog 0x7f15019c
int style Theme_MaterialComponents_DialogWhenLarge 0x7f15019d
int style Theme_MaterialComponents_Dialog_Alert 0x7f15019e
int style Theme_MaterialComponents_Dialog_MinWidth 0x7f15019f
int style Theme_MaterialComponents_Light 0x7f1501a0
int style Theme_MaterialComponents_Light_BottomSheetDialog 0x7f1501a1
int style Theme_MaterialComponents_Light_Bridge 0x7f1501a2
int style Theme_MaterialComponents_Light_DarkActionBar 0x7f1501a3
int style Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f1501a4
int style Theme_MaterialComponents_Light_Dialog 0x7f1501a5
int style Theme_MaterialComponents_Light_DialogWhenLarge 0x7f1501a6
int style Theme_MaterialComponents_Light_Dialog_Alert 0x7f1501a7
int style Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f1501a8
int style Theme_MaterialComponents_Light_NoActionBar 0x7f1501a9
int style Theme_MaterialComponents_Light_NoActionBar_Bridge 0x7f1501aa
int style Theme_MaterialComponents_NoActionBar 0x7f1501ab
int style Theme_MaterialComponents_NoActionBar_Bridge 0x7f1501ac
int style Widget_AppCompat_ActionBar 0x7f1501ad
int style Widget_AppCompat_ActionBar_Solid 0x7f1501ae
int style Widget_AppCompat_ActionBar_TabBar 0x7f1501af
int style Widget_AppCompat_ActionBar_TabText 0x7f1501b0
int style Widget_AppCompat_ActionBar_TabView 0x7f1501b1
int style Widget_AppCompat_ActionButton 0x7f1501b2
int style Widget_AppCompat_ActionButton_CloseMode 0x7f1501b3
int style Widget_AppCompat_ActionButton_Overflow 0x7f1501b4
int style Widget_AppCompat_ActionMode 0x7f1501b5
int style Widget_AppCompat_ActivityChooserView 0x7f1501b6
int style Widget_AppCompat_AutoCompleteTextView 0x7f1501b7
int style Widget_AppCompat_Button 0x7f1501b8
int style Widget_AppCompat_ButtonBar 0x7f1501b9
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f1501ba
int style Widget_AppCompat_Button_Borderless 0x7f1501bb
int style Widget_AppCompat_Button_Borderless_Colored 0x7f1501bc
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f1501bd
int style Widget_AppCompat_Button_Colored 0x7f1501be
int style Widget_AppCompat_Button_Small 0x7f1501bf
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f1501c0
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f1501c1
int style Widget_AppCompat_CompoundButton_Switch 0x7f1501c2
int style Widget_AppCompat_DrawerArrowToggle 0x7f1501c3
int style Widget_AppCompat_DropDownItem_Spinner 0x7f1501c4
int style Widget_AppCompat_EditText 0x7f1501c5
int style Widget_AppCompat_ImageButton 0x7f1501c6
int style Widget_AppCompat_Light_ActionBar 0x7f1501c7
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f1501c8
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f1501c9
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f1501ca
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f1501cb
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f1501cc
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f1501cd
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f1501ce
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f1501cf
int style Widget_AppCompat_Light_ActionButton 0x7f1501d0
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f1501d1
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f1501d2
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f1501d3
int style Widget_AppCompat_Light_ActivityChooserView 0x7f1501d4
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f1501d5
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f1501d6
int style Widget_AppCompat_Light_ListPopupWindow 0x7f1501d7
int style Widget_AppCompat_Light_ListView_DropDown 0x7f1501d8
int style Widget_AppCompat_Light_PopupMenu 0x7f1501d9
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f1501da
int style Widget_AppCompat_Light_SearchView 0x7f1501db
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f1501dc
int style Widget_AppCompat_ListMenuView 0x7f1501dd
int style Widget_AppCompat_ListPopupWindow 0x7f1501de
int style Widget_AppCompat_ListView 0x7f1501df
int style Widget_AppCompat_ListView_DropDown 0x7f1501e0
int style Widget_AppCompat_ListView_Menu 0x7f1501e1
int style Widget_AppCompat_NotificationActionContainer 0x7f1501e2
int style Widget_AppCompat_NotificationActionText 0x7f1501e3
int style Widget_AppCompat_PopupMenu 0x7f1501e4
int style Widget_AppCompat_PopupMenu_Overflow 0x7f1501e5
int style Widget_AppCompat_PopupWindow 0x7f1501e6
int style Widget_AppCompat_ProgressBar 0x7f1501e7
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f1501e8
int style Widget_AppCompat_RatingBar 0x7f1501e9
int style Widget_AppCompat_RatingBar_Indicator 0x7f1501ea
int style Widget_AppCompat_RatingBar_Small 0x7f1501eb
int style Widget_AppCompat_SearchView 0x7f1501ec
int style Widget_AppCompat_SearchView_ActionBar 0x7f1501ed
int style Widget_AppCompat_SeekBar 0x7f1501ee
int style Widget_AppCompat_SeekBar_Discrete 0x7f1501ef
int style Widget_AppCompat_Spinner 0x7f1501f0
int style Widget_AppCompat_Spinner_DropDown 0x7f1501f1
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f1501f2
int style Widget_AppCompat_Spinner_Underlined 0x7f1501f3
int style Widget_AppCompat_TextView_SpinnerItem 0x7f1501f4
int style Widget_AppCompat_Toolbar 0x7f1501f5
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f1501f6
int style Widget_Compat_NotificationActionContainer 0x7f1501f7
int style Widget_Compat_NotificationActionText 0x7f1501f8
int style Widget_Design_AppBarLayout 0x7f1501f9
int style Widget_Design_BottomNavigationView 0x7f1501fa
int style Widget_Design_BottomSheet_Modal 0x7f1501fb
int style Widget_Design_CollapsingToolbar 0x7f1501fc
int style Widget_Design_CoordinatorLayout 0x7f1501fd
int style Widget_Design_FloatingActionButton 0x7f1501fe
int style Widget_Design_NavigationView 0x7f1501ff
int style Widget_Design_ScrimInsetsFrameLayout 0x7f150200
int style Widget_Design_Snackbar 0x7f150201
int style Widget_Design_TabLayout 0x7f150202
int style Widget_Design_TextInputLayout 0x7f150203
int style Widget_MaterialComponents_BottomAppBar 0x7f150204
int style Widget_MaterialComponents_BottomAppBar_Colored 0x7f150205
int style Widget_MaterialComponents_BottomNavigationView 0x7f150206
int style Widget_MaterialComponents_BottomNavigationView_Colored 0x7f150207
int style Widget_MaterialComponents_BottomSheet_Modal 0x7f150208
int style Widget_MaterialComponents_Button 0x7f150209
int style Widget_MaterialComponents_Button_Icon 0x7f15020a
int style Widget_MaterialComponents_Button_OutlinedButton 0x7f15020b
int style Widget_MaterialComponents_Button_OutlinedButton_Icon 0x7f15020c
int style Widget_MaterialComponents_Button_TextButton 0x7f15020d
int style Widget_MaterialComponents_Button_TextButton_Dialog 0x7f15020e
int style Widget_MaterialComponents_Button_TextButton_Dialog_Icon 0x7f15020f
int style Widget_MaterialComponents_Button_TextButton_Icon 0x7f150210
int style Widget_MaterialComponents_Button_UnelevatedButton 0x7f150211
int style Widget_MaterialComponents_Button_UnelevatedButton_Icon 0x7f150212
int style Widget_MaterialComponents_CardView 0x7f150213
int style Widget_MaterialComponents_ChipGroup 0x7f150214
int style Widget_MaterialComponents_Chip_Action 0x7f150215
int style Widget_MaterialComponents_Chip_Choice 0x7f150216
int style Widget_MaterialComponents_Chip_Entry 0x7f150217
int style Widget_MaterialComponents_Chip_Filter 0x7f150218
int style Widget_MaterialComponents_FloatingActionButton 0x7f150219
int style Widget_MaterialComponents_NavigationView 0x7f15021a
int style Widget_MaterialComponents_Snackbar 0x7f15021b
int style Widget_MaterialComponents_Snackbar_FullWidth 0x7f15021c
int style Widget_MaterialComponents_TabLayout 0x7f15021d
int style Widget_MaterialComponents_TabLayout_Colored 0x7f15021e
int style Widget_MaterialComponents_TextInputEditText_FilledBox 0x7f15021f
int style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f150220
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox 0x7f150221
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f150222
int style Widget_MaterialComponents_TextInputLayout_FilledBox 0x7f150223
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense 0x7f150224
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox 0x7f150225
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense 0x7f150226
int style Widget_MaterialComponents_Toolbar 0x7f150227
int style Widget_Support_CoordinatorLayout 0x7f150228
int style activityTheme 0x7f150229
int style custom_dialog 0x7f15022a
int style filterBtn_01 0x7f15022b
int style filterBtn_02 0x7f15022c
int style my_actionbar_style 0x7f15022d
int style nextBtn 0x7f15022e
int style style_user_seldia 0x7f15022f
int style tabbar_01 0x7f150230
int style tag_view_dialog 0x7f150231
int style tipStyle 0x7f150232
int[] styleable ActionBar { 0x7f040039, 0x7f04003a, 0x7f04003b, 0x7f0400a9, 0x7f0400aa, 0x7f0400ab, 0x7f0400ac, 0x7f0400ad, 0x7f0400ae, 0x7f0400c6, 0x7f0400d2, 0x7f0400d3, 0x7f0400e3, 0x7f040126, 0x7f04012b, 0x7f040130, 0x7f040131, 0x7f040133, 0x7f04013d, 0x7f040150, 0x7f0401ab, 0x7f0401cc, 0x7f0401ef, 0x7f0401f3, 0x7f0401f4, 0x7f040232, 0x7f040235, 0x7f04027f, 0x7f040289 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x10100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView {  }
int[] styleable ActionMode { 0x7f040039, 0x7f04003a, 0x7f04008f, 0x7f040126, 0x7f040235, 0x7f040289 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f0400ed, 0x7f04013e }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable AlertDialog { 0x10100f2, 0x7f040060, 0x7f040061, 0x7f0401a2, 0x7f0401a3, 0x7f0401c9, 0x7f040216, 0x7f040217 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
int styleable AnimatedStateListDrawableCompat_android_constantSize 0
int styleable AnimatedStateListDrawableCompat_android_dither 1
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 2
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 3
int styleable AnimatedStateListDrawableCompat_android_variablePadding 4
int styleable AnimatedStateListDrawableCompat_android_visible 5
int[] styleable AnimatedStateListDrawableItem { 0x1010199, 0x10100d0 }
int styleable AnimatedStateListDrawableItem_android_drawable 0
int styleable AnimatedStateListDrawableItem_android_id 1
int[] styleable AnimatedStateListDrawableTransition { 0x1010199, 0x101044a, 0x101044b, 0x1010449 }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_fromId 1
int styleable AnimatedStateListDrawableTransition_android_reversible 2
int styleable AnimatedStateListDrawableTransition_android_toId 3
int[] styleable AppBarLayout { 0x10100d4, 0x1010540, 0x101048f, 0x7f0400e3, 0x7f0400ee, 0x7f04019c }
int styleable AppBarLayout_android_background 0
int styleable AppBarLayout_android_keyboardNavigationCluster 1
int styleable AppBarLayout_android_touchscreenBlocksFocus 2
int styleable AppBarLayout_elevation 3
int styleable AppBarLayout_expanded 4
int styleable AppBarLayout_liftOnScroll 5
int[] styleable AppBarLayoutStates { 0x7f040227, 0x7f040228, 0x7f040229, 0x7f04022a }
int styleable AppBarLayoutStates_state_collapsed 0
int styleable AppBarLayoutStates_state_collapsible 1
int styleable AppBarLayoutStates_state_liftable 2
int styleable AppBarLayoutStates_state_lifted 3
int[] styleable AppBarLayout_Layout { 0x7f04019a, 0x7f04019b }
int styleable AppBarLayout_Layout_layout_scrollFlags 0
int styleable AppBarLayout_Layout_layout_scrollInterpolator 1
int[] styleable AppCompatImageView { 0x1010119, 0x7f040223, 0x7f04027d, 0x7f04027e }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x1010142, 0x7f04027a, 0x7f04027b, 0x7f04027c }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x101016e, 0x1010393, 0x101016f, 0x1010170, 0x1010392, 0x101016d, 0x1010034 }
int styleable AppCompatTextHelper_android_drawableBottom 0
int styleable AppCompatTextHelper_android_drawableEnd 1
int styleable AppCompatTextHelper_android_drawableLeft 2
int styleable AppCompatTextHelper_android_drawableRight 3
int styleable AppCompatTextHelper_android_drawableStart 4
int styleable AppCompatTextHelper_android_drawableTop 5
int styleable AppCompatTextHelper_android_textAppearance 6
int[] styleable AppCompatTextView { 0x1010034, 0x7f040033, 0x7f040034, 0x7f040035, 0x7f040036, 0x7f040037, 0x7f040101, 0x7f040117, 0x7f04015a, 0x7f04019e, 0x7f040259 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_firstBaselineToTopHeight 6
int styleable AppCompatTextView_fontFamily 7
int styleable AppCompatTextView_lastBaselineToBottomHeight 8
int styleable AppCompatTextView_lineHeight 9
int styleable AppCompatTextView_textAllCaps 10
int[] styleable AppCompatTheme { 0x7f040001, 0x7f040002, 0x7f040003, 0x7f040004, 0x7f040005, 0x7f040006, 0x7f040007, 0x7f040008, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c, 0x7f04000d, 0x7f04000f, 0x7f040010, 0x7f040011, 0x7f040012, 0x7f040013, 0x7f040014, 0x7f040015, 0x7f040016, 0x7f040017, 0x7f040018, 0x7f040019, 0x7f04001a, 0x7f04001b, 0x7f04001c, 0x7f04001d, 0x7f04001e, 0x7f04001f, 0x7f040022, 0x7f040023, 0x7f040024, 0x7f040025, 0x7f040026, 0x10100ae, 0x1010057, 0x7f040032, 0x7f04004b, 0x7f04005a, 0x7f04005b, 0x7f04005c, 0x7f04005d, 0x7f04005e, 0x7f040062, 0x7f040063, 0x7f04006e, 0x7f040073, 0x7f040095, 0x7f040096, 0x7f040097, 0x7f040098, 0x7f040099, 0x7f04009a, 0x7f04009b, 0x7f04009c, 0x7f04009d, 0x7f04009f, 0x7f0400b6, 0x7f0400cf, 0x7f0400d0, 0x7f0400d1, 0x7f0400d4, 0x7f0400d6, 0x7f0400dd, 0x7f0400de, 0x7f0400e0, 0x7f0400e1, 0x7f0400e2, 0x7f040130, 0x7f04013c, 0x7f0401a0, 0x7f0401a1, 0x7f0401a4, 0x7f0401a5, 0x7f0401a6, 0x7f0401a7, 0x7f0401a8, 0x7f0401a9, 0x7f0401aa, 0x7f0401dc, 0x7f0401dd, 0x7f0401de, 0x7f0401ee, 0x7f0401f0, 0x7f0401f7, 0x7f0401f8, 0x7f0401f9, 0x7f0401fa, 0x7f04020d, 0x7f04020e, 0x7f04020f, 0x7f040210, 0x7f04021f, 0x7f040220, 0x7f040239, 0x7f040264, 0x7f040265, 0x7f040266, 0x7f040267, 0x7f040269, 0x7f04026a, 0x7f04026b, 0x7f04026c, 0x7f04026f, 0x7f040271, 0x7f04028b, 0x7f04028c, 0x7f04028d, 0x7f04028e, 0x7f0402a0, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b1, 0x7f0402b2 }
int styleable AppCompatTheme_actionBarDivider 0
int styleable AppCompatTheme_actionBarItemBackground 1
int styleable AppCompatTheme_actionBarPopupTheme 2
int styleable AppCompatTheme_actionBarSize 3
int styleable AppCompatTheme_actionBarSplitStyle 4
int styleable AppCompatTheme_actionBarStyle 5
int styleable AppCompatTheme_actionBarTabBarStyle 6
int styleable AppCompatTheme_actionBarTabStyle 7
int styleable AppCompatTheme_actionBarTabTextStyle 8
int styleable AppCompatTheme_actionBarTheme 9
int styleable AppCompatTheme_actionBarWidgetTheme 10
int styleable AppCompatTheme_actionButtonStyle 11
int styleable AppCompatTheme_actionDropDownStyle 12
int styleable AppCompatTheme_actionMenuTextAppearance 13
int styleable AppCompatTheme_actionMenuTextColor 14
int styleable AppCompatTheme_actionModeBackground 15
int styleable AppCompatTheme_actionModeCloseButtonStyle 16
int styleable AppCompatTheme_actionModeCloseDrawable 17
int styleable AppCompatTheme_actionModeCopyDrawable 18
int styleable AppCompatTheme_actionModeCutDrawable 19
int styleable AppCompatTheme_actionModeFindDrawable 20
int styleable AppCompatTheme_actionModePasteDrawable 21
int styleable AppCompatTheme_actionModePopupWindowStyle 22
int styleable AppCompatTheme_actionModeSelectAllDrawable 23
int styleable AppCompatTheme_actionModeShareDrawable 24
int styleable AppCompatTheme_actionModeSplitBackground 25
int styleable AppCompatTheme_actionModeStyle 26
int styleable AppCompatTheme_actionModeWebSearchDrawable 27
int styleable AppCompatTheme_actionOverflowButtonStyle 28
int styleable AppCompatTheme_actionOverflowMenuStyle 29
int styleable AppCompatTheme_activityChooserViewStyle 30
int styleable AppCompatTheme_alertDialogButtonGroupStyle 31
int styleable AppCompatTheme_alertDialogCenterButtons 32
int styleable AppCompatTheme_alertDialogStyle 33
int styleable AppCompatTheme_alertDialogTheme 34
int styleable AppCompatTheme_android_windowAnimationStyle 35
int styleable AppCompatTheme_android_windowIsFloating 36
int styleable AppCompatTheme_autoCompleteTextViewStyle 37
int styleable AppCompatTheme_borderlessButtonStyle 38
int styleable AppCompatTheme_buttonBarButtonStyle 39
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 40
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 41
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 42
int styleable AppCompatTheme_buttonBarStyle 43
int styleable AppCompatTheme_buttonStyle 44
int styleable AppCompatTheme_buttonStyleSmall 45
int styleable AppCompatTheme_checkboxStyle 46
int styleable AppCompatTheme_checkedTextViewStyle 47
int styleable AppCompatTheme_colorAccent 48
int styleable AppCompatTheme_colorBackgroundFloating 49
int styleable AppCompatTheme_colorButtonNormal 50
int styleable AppCompatTheme_colorControlActivated 51
int styleable AppCompatTheme_colorControlHighlight 52
int styleable AppCompatTheme_colorControlNormal 53
int styleable AppCompatTheme_colorError 54
int styleable AppCompatTheme_colorPrimary 55
int styleable AppCompatTheme_colorPrimaryDark 56
int styleable AppCompatTheme_colorSwitchThumbNormal 57
int styleable AppCompatTheme_controlBackground 58
int styleable AppCompatTheme_dialogCornerRadius 59
int styleable AppCompatTheme_dialogPreferredPadding 60
int styleable AppCompatTheme_dialogTheme 61
int styleable AppCompatTheme_dividerHorizontal 62
int styleable AppCompatTheme_dividerVertical 63
int styleable AppCompatTheme_dropDownListViewStyle 64
int styleable AppCompatTheme_dropdownListPreferredItemHeight 65
int styleable AppCompatTheme_editTextBackground 66
int styleable AppCompatTheme_editTextColor 67
int styleable AppCompatTheme_editTextStyle 68
int styleable AppCompatTheme_homeAsUpIndicator 69
int styleable AppCompatTheme_imageButtonStyle 70
int styleable AppCompatTheme_listChoiceBackgroundIndicator 71
int styleable AppCompatTheme_listDividerAlertDialog 72
int styleable AppCompatTheme_listMenuViewStyle 73
int styleable AppCompatTheme_listPopupWindowStyle 74
int styleable AppCompatTheme_listPreferredItemHeight 75
int styleable AppCompatTheme_listPreferredItemHeightLarge 76
int styleable AppCompatTheme_listPreferredItemHeightSmall 77
int styleable AppCompatTheme_listPreferredItemPaddingLeft 78
int styleable AppCompatTheme_listPreferredItemPaddingRight 79
int styleable AppCompatTheme_panelBackground 80
int styleable AppCompatTheme_panelMenuListTheme 81
int styleable AppCompatTheme_panelMenuListWidth 82
int styleable AppCompatTheme_popupMenuStyle 83
int styleable AppCompatTheme_popupWindowStyle 84
int styleable AppCompatTheme_radioButtonStyle 85
int styleable AppCompatTheme_ratingBarStyle 86
int styleable AppCompatTheme_ratingBarStyleIndicator 87
int styleable AppCompatTheme_ratingBarStyleSmall 88
int styleable AppCompatTheme_searchViewStyle 89
int styleable AppCompatTheme_seekBarStyle 90
int styleable AppCompatTheme_selectableItemBackground 91
int styleable AppCompatTheme_selectableItemBackgroundBorderless 92
int styleable AppCompatTheme_spinnerDropDownItemStyle 93
int styleable AppCompatTheme_spinnerStyle 94
int styleable AppCompatTheme_switchStyle 95
int styleable AppCompatTheme_textAppearanceLargePopupMenu 96
int styleable AppCompatTheme_textAppearanceListItem 97
int styleable AppCompatTheme_textAppearanceListItemSecondary 98
int styleable AppCompatTheme_textAppearanceListItemSmall 99
int styleable AppCompatTheme_textAppearancePopupMenuHeader 100
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 101
int styleable AppCompatTheme_textAppearanceSearchResultTitle 102
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 103
int styleable AppCompatTheme_textColorAlertDialogListItem 104
int styleable AppCompatTheme_textColorSearchUrl 105
int styleable AppCompatTheme_toolbarNavigationButtonStyle 106
int styleable AppCompatTheme_toolbarStyle 107
int styleable AppCompatTheme_tooltipForegroundColor 108
int styleable AppCompatTheme_tooltipFrameBackground 109
int styleable AppCompatTheme_viewInflaterClass 110
int styleable AppCompatTheme_windowActionBar 111
int styleable AppCompatTheme_windowActionBarOverlay 112
int styleable AppCompatTheme_windowActionModeOverlay 113
int styleable AppCompatTheme_windowFixedHeightMajor 114
int styleable AppCompatTheme_windowFixedHeightMinor 115
int styleable AppCompatTheme_windowFixedWidthMajor 116
int styleable AppCompatTheme_windowFixedWidthMinor 117
int styleable AppCompatTheme_windowMinWidthMajor 118
int styleable AppCompatTheme_windowMinWidthMinor 119
int styleable AppCompatTheme_windowNoTitle 120
int[] styleable BookmarkTabLayout { 0x7f040048, 0x7f040049 }
int styleable BookmarkTabLayout_bookmark_tab_selected 0
int styleable BookmarkTabLayout_bookmark_tab_unselected 1
int[] styleable BottomAppBar { 0x7f04003c, 0x7f0400f6, 0x7f0400f7, 0x7f0400f8, 0x7f0400f9, 0x7f04012c }
int styleable BottomAppBar_backgroundTint 0
int styleable BottomAppBar_fabAlignmentMode 1
int styleable BottomAppBar_fabCradleMargin 2
int styleable BottomAppBar_fabCradleRoundedCornerRadius 3
int styleable BottomAppBar_fabCradleVerticalOffset 4
int styleable BottomAppBar_hideOnScroll 5
int[] styleable BottomNavigationView { 0x7f0400e3, 0x7f04014a, 0x7f04014c, 0x7f04014e, 0x7f04014f, 0x7f040153, 0x7f040154, 0x7f040155, 0x7f040159, 0x7f0401b7 }
int styleable BottomNavigationView_elevation 0
int styleable BottomNavigationView_itemBackground 1
int styleable BottomNavigationView_itemHorizontalTranslationEnabled 2
int styleable BottomNavigationView_itemIconSize 3
int styleable BottomNavigationView_itemIconTint 4
int styleable BottomNavigationView_itemTextAppearanceActive 5
int styleable BottomNavigationView_itemTextAppearanceInactive 6
int styleable BottomNavigationView_itemTextColor 7
int styleable BottomNavigationView_labelVisibilityMode 8
int styleable BottomNavigationView_menu 9
int[] styleable BottomSheetBehavior_Layout { 0x7f040043, 0x7f040044, 0x7f040046, 0x7f040047 }
int styleable BottomSheetBehavior_Layout_behavior_fitToContents 0
int styleable BottomSheetBehavior_Layout_behavior_hideable 1
int styleable BottomSheetBehavior_Layout_behavior_peekHeight 2
int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 3
int[] styleable ButtonBarLayout { 0x7f040027 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable CardView { 0x1010140, 0x101013f, 0x7f040066, 0x7f040067, 0x7f040068, 0x7f040069, 0x7f04006a, 0x7f04006b, 0x7f0400af, 0x7f0400b0, 0x7f0400b1, 0x7f0400b2, 0x7f0400b3 }
int styleable CardView_android_minHeight 0
int styleable CardView_android_minWidth 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable Chip { 0x10101e5, 0x10100ab, 0x101011f, 0x101014f, 0x1010034, 0x7f040070, 0x7f040071, 0x7f040072, 0x7f040074, 0x7f040075, 0x7f040076, 0x7f040078, 0x7f040079, 0x7f04007a, 0x7f04007b, 0x7f04007c, 0x7f04007d, 0x7f040082, 0x7f040083, 0x7f040084, 0x7f040088, 0x7f040089, 0x7f04008a, 0x7f04008b, 0x7f04008c, 0x7f04008d, 0x7f04008e, 0x7f04012a, 0x7f040134, 0x7f040138, 0x7f040201, 0x7f040213, 0x7f040272, 0x7f040274 }
int styleable Chip_android_checkable 0
int styleable Chip_android_ellipsize 1
int styleable Chip_android_maxWidth 2
int styleable Chip_android_text 3
int styleable Chip_android_textAppearance 4
int styleable Chip_checkedIcon 5
int styleable Chip_checkedIconEnabled 6
int styleable Chip_checkedIconVisible 7
int styleable Chip_chipBackgroundColor 8
int styleable Chip_chipCornerRadius 9
int styleable Chip_chipEndPadding 10
int styleable Chip_chipIcon 11
int styleable Chip_chipIconEnabled 12
int styleable Chip_chipIconSize 13
int styleable Chip_chipIconTint 14
int styleable Chip_chipIconVisible 15
int styleable Chip_chipMinHeight 16
int styleable Chip_chipStartPadding 17
int styleable Chip_chipStrokeColor 18
int styleable Chip_chipStrokeWidth 19
int styleable Chip_closeIcon 20
int styleable Chip_closeIconEnabled 21
int styleable Chip_closeIconEndPadding 22
int styleable Chip_closeIconSize 23
int styleable Chip_closeIconStartPadding 24
int styleable Chip_closeIconTint 25
int styleable Chip_closeIconVisible 26
int styleable Chip_hideMotionSpec 27
int styleable Chip_iconEndPadding 28
int styleable Chip_iconStartPadding 29
int styleable Chip_rippleColor 30
int styleable Chip_showMotionSpec 31
int styleable Chip_textEndPadding 32
int styleable Chip_textStartPadding 33
int[] styleable ChipGroup { 0x7f04006f, 0x7f04007e, 0x7f04007f, 0x7f040080, 0x7f040218, 0x7f040219 }
int styleable ChipGroup_checkedChip 0
int styleable ChipGroup_chipSpacing 1
int styleable ChipGroup_chipSpacingHorizontal 2
int styleable ChipGroup_chipSpacingVertical 3
int styleable ChipGroup_singleLine 4
int styleable ChipGroup_singleSelection 5
int[] styleable CollapsingToolbarLayout { 0x7f040092, 0x7f040093, 0x7f0400b4, 0x7f0400ef, 0x7f0400f0, 0x7f0400f1, 0x7f0400f2, 0x7f0400f3, 0x7f0400f4, 0x7f0400f5, 0x7f040208, 0x7f04020a, 0x7f04022c, 0x7f04027f, 0x7f040280, 0x7f04028a }
int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
int styleable CollapsingToolbarLayout_contentScrim 2
int styleable CollapsingToolbarLayout_expandedTitleGravity 3
int styleable CollapsingToolbarLayout_expandedTitleMargin 4
int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 5
int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 6
int styleable CollapsingToolbarLayout_expandedTitleMarginStart 7
int styleable CollapsingToolbarLayout_expandedTitleMarginTop 8
int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 9
int styleable CollapsingToolbarLayout_scrimAnimationDuration 10
int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 11
int styleable CollapsingToolbarLayout_statusBarScrim 12
int styleable CollapsingToolbarLayout_title 13
int styleable CollapsingToolbarLayout_titleEnabled 14
int styleable CollapsingToolbarLayout_toolbarId 15
int[] styleable CollapsingToolbarLayout_Layout { 0x7f040162, 0x7f040163 }
int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
int[] styleable ColorStateListItem { 0x7f040028, 0x101031f, 0x10101a5 }
int styleable ColorStateListItem_alpha 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_color 2
int[] styleable CompoundButton { 0x1010107, 0x7f040064, 0x7f040065 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonTint 1
int styleable CompoundButton_buttonTintMode 2
int[] styleable Constraint { 0x101031f, 0x1010440, 0x10100d0, 0x10100f5, 0x10100fa, 0x10103b6, 0x10100f7, 0x10100f9, 0x10103b5, 0x10100f8, 0x10100f4, 0x1010120, 0x101011f, 0x1010140, 0x101013f, 0x10100c4, 0x1010326, 0x1010327, 0x1010328, 0x1010324, 0x1010325, 0x1010320, 0x1010321, 0x1010322, 0x1010323, 0x10103fa, 0x10100dc, 0x7f04002b, 0x7f04003f, 0x7f040040, 0x7f040041, 0x7f04006d, 0x7f0400a4, 0x7f0400a5, 0x7f0400da, 0x7f040103, 0x7f040104, 0x7f040105, 0x7f040106, 0x7f040107, 0x7f040108, 0x7f040109, 0x7f04010a, 0x7f04010b, 0x7f04010c, 0x7f04010d, 0x7f04010e, 0x7f04010f, 0x7f040111, 0x7f040112, 0x7f040113, 0x7f040114, 0x7f040115, 0x7f040164, 0x7f040165, 0x7f040166, 0x7f040167, 0x7f040168, 0x7f040169, 0x7f04016a, 0x7f04016b, 0x7f04016c, 0x7f04016d, 0x7f04016e, 0x7f04016f, 0x7f040170, 0x7f040171, 0x7f040172, 0x7f040173, 0x7f040174, 0x7f040175, 0x7f040176, 0x7f040177, 0x7f040178, 0x7f040179, 0x7f04017a, 0x7f04017b, 0x7f04017c, 0x7f04017d, 0x7f04017e, 0x7f04017f, 0x7f040180, 0x7f040181, 0x7f040182, 0x7f040183, 0x7f040184, 0x7f040185, 0x7f040186, 0x7f040187, 0x7f040188, 0x7f040189, 0x7f04018a, 0x7f04018b, 0x7f04018c, 0x7f04018d, 0x7f04018f, 0x7f040190, 0x7f040191, 0x7f040192, 0x7f040193, 0x7f040194, 0x7f040195, 0x7f040196, 0x7f0401c3, 0x7f0401c4, 0x7f0401e4, 0x7f0401ec, 0x7f040297, 0x7f040299, 0x7f0402a1 }
int styleable Constraint_android_alpha 0
int styleable Constraint_android_elevation 1
int styleable Constraint_android_id 2
int styleable Constraint_android_layout_height 3
int styleable Constraint_android_layout_marginBottom 4
int styleable Constraint_android_layout_marginEnd 5
int styleable Constraint_android_layout_marginLeft 6
int styleable Constraint_android_layout_marginRight 7
int styleable Constraint_android_layout_marginStart 8
int styleable Constraint_android_layout_marginTop 9
int styleable Constraint_android_layout_width 10
int styleable Constraint_android_maxHeight 11
int styleable Constraint_android_maxWidth 12
int styleable Constraint_android_minHeight 13
int styleable Constraint_android_minWidth 14
int styleable Constraint_android_orientation 15
int styleable Constraint_android_rotation 16
int styleable Constraint_android_rotationX 17
int styleable Constraint_android_rotationY 18
int styleable Constraint_android_scaleX 19
int styleable Constraint_android_scaleY 20
int styleable Constraint_android_transformPivotX 21
int styleable Constraint_android_transformPivotY 22
int styleable Constraint_android_translationX 23
int styleable Constraint_android_translationY 24
int styleable Constraint_android_translationZ 25
int styleable Constraint_android_visibility 26
int styleable Constraint_animate_relativeTo 27
int styleable Constraint_barrierAllowsGoneWidgets 28
int styleable Constraint_barrierDirection 29
int styleable Constraint_barrierMargin 30
int styleable Constraint_chainUseRtl 31
int styleable Constraint_constraint_referenced_ids 32
int styleable Constraint_constraint_referenced_tags 33
int styleable Constraint_drawPath 34
int styleable Constraint_flow_firstHorizontalBias 35
int styleable Constraint_flow_firstHorizontalStyle 36
int styleable Constraint_flow_firstVerticalBias 37
int styleable Constraint_flow_firstVerticalStyle 38
int styleable Constraint_flow_horizontalAlign 39
int styleable Constraint_flow_horizontalBias 40
int styleable Constraint_flow_horizontalGap 41
int styleable Constraint_flow_horizontalStyle 42
int styleable Constraint_flow_lastHorizontalBias 43
int styleable Constraint_flow_lastHorizontalStyle 44
int styleable Constraint_flow_lastVerticalBias 45
int styleable Constraint_flow_lastVerticalStyle 46
int styleable Constraint_flow_maxElementsWrap 47
int styleable Constraint_flow_verticalAlign 48
int styleable Constraint_flow_verticalBias 49
int styleable Constraint_flow_verticalGap 50
int styleable Constraint_flow_verticalStyle 51
int styleable Constraint_flow_wrapMode 52
int styleable Constraint_layout_constrainedHeight 53
int styleable Constraint_layout_constrainedWidth 54
int styleable Constraint_layout_constraintBaseline_creator 55
int styleable Constraint_layout_constraintBaseline_toBaselineOf 56
int styleable Constraint_layout_constraintBottom_creator 57
int styleable Constraint_layout_constraintBottom_toBottomOf 58
int styleable Constraint_layout_constraintBottom_toTopOf 59
int styleable Constraint_layout_constraintCircle 60
int styleable Constraint_layout_constraintCircleAngle 61
int styleable Constraint_layout_constraintCircleRadius 62
int styleable Constraint_layout_constraintDimensionRatio 63
int styleable Constraint_layout_constraintEnd_toEndOf 64
int styleable Constraint_layout_constraintEnd_toStartOf 65
int styleable Constraint_layout_constraintGuide_begin 66
int styleable Constraint_layout_constraintGuide_end 67
int styleable Constraint_layout_constraintGuide_percent 68
int styleable Constraint_layout_constraintHeight_default 69
int styleable Constraint_layout_constraintHeight_max 70
int styleable Constraint_layout_constraintHeight_min 71
int styleable Constraint_layout_constraintHeight_percent 72
int styleable Constraint_layout_constraintHorizontal_bias 73
int styleable Constraint_layout_constraintHorizontal_chainStyle 74
int styleable Constraint_layout_constraintHorizontal_weight 75
int styleable Constraint_layout_constraintLeft_creator 76
int styleable Constraint_layout_constraintLeft_toLeftOf 77
int styleable Constraint_layout_constraintLeft_toRightOf 78
int styleable Constraint_layout_constraintRight_creator 79
int styleable Constraint_layout_constraintRight_toLeftOf 80
int styleable Constraint_layout_constraintRight_toRightOf 81
int styleable Constraint_layout_constraintStart_toEndOf 82
int styleable Constraint_layout_constraintStart_toStartOf 83
int styleable Constraint_layout_constraintTag 84
int styleable Constraint_layout_constraintTop_creator 85
int styleable Constraint_layout_constraintTop_toBottomOf 86
int styleable Constraint_layout_constraintTop_toTopOf 87
int styleable Constraint_layout_constraintVertical_bias 88
int styleable Constraint_layout_constraintVertical_chainStyle 89
int styleable Constraint_layout_constraintVertical_weight 90
int styleable Constraint_layout_constraintWidth_default 91
int styleable Constraint_layout_constraintWidth_max 92
int styleable Constraint_layout_constraintWidth_min 93
int styleable Constraint_layout_constraintWidth_percent 94
int styleable Constraint_layout_editor_absoluteX 95
int styleable Constraint_layout_editor_absoluteY 96
int styleable Constraint_layout_goneMarginBottom 97
int styleable Constraint_layout_goneMarginEnd 98
int styleable Constraint_layout_goneMarginLeft 99
int styleable Constraint_layout_goneMarginRight 100
int styleable Constraint_layout_goneMarginStart 101
int styleable Constraint_layout_goneMarginTop 102
int styleable Constraint_motionProgress 103
int styleable Constraint_motionStagger 104
int styleable Constraint_pathMotionArc 105
int styleable Constraint_pivotAnchor 106
int styleable Constraint_transitionEasing 107
int styleable Constraint_transitionPathRotate 108
int styleable Constraint_visibilityMode 109
int[] styleable ConstraintLayout_Layout { 0x1010440, 0x1010120, 0x101011f, 0x1010140, 0x101013f, 0x10100c4, 0x10100d5, 0x10100d9, 0x10103b4, 0x10100d6, 0x10100d8, 0x10103b3, 0x10100d7, 0x10100dc, 0x7f04003f, 0x7f040040, 0x7f040041, 0x7f04006d, 0x7f0400a1, 0x7f0400a4, 0x7f0400a5, 0x7f040103, 0x7f040104, 0x7f040105, 0x7f040106, 0x7f040107, 0x7f040108, 0x7f040109, 0x7f04010a, 0x7f04010b, 0x7f04010c, 0x7f04010d, 0x7f04010e, 0x7f04010f, 0x7f040111, 0x7f040112, 0x7f040113, 0x7f040114, 0x7f040115, 0x7f04015c, 0x7f040164, 0x7f040165, 0x7f040166, 0x7f040167, 0x7f040168, 0x7f040169, 0x7f04016a, 0x7f04016b, 0x7f04016c, 0x7f04016d, 0x7f04016e, 0x7f04016f, 0x7f040170, 0x7f040171, 0x7f040172, 0x7f040173, 0x7f040174, 0x7f040175, 0x7f040176, 0x7f040177, 0x7f040178, 0x7f040179, 0x7f04017a, 0x7f04017b, 0x7f04017c, 0x7f04017d, 0x7f04017e, 0x7f04017f, 0x7f040180, 0x7f040181, 0x7f040182, 0x7f040183, 0x7f040184, 0x7f040185, 0x7f040186, 0x7f040187, 0x7f040188, 0x7f040189, 0x7f04018a, 0x7f04018b, 0x7f04018c, 0x7f04018d, 0x7f04018f, 0x7f040190, 0x7f040191, 0x7f040192, 0x7f040193, 0x7f040194, 0x7f040195, 0x7f040196, 0x7f040199 }
int styleable ConstraintLayout_Layout_android_elevation 0
int styleable ConstraintLayout_Layout_android_maxHeight 1
int styleable ConstraintLayout_Layout_android_maxWidth 2
int styleable ConstraintLayout_Layout_android_minHeight 3
int styleable ConstraintLayout_Layout_android_minWidth 4
int styleable ConstraintLayout_Layout_android_orientation 5
int styleable ConstraintLayout_Layout_android_padding 6
int styleable ConstraintLayout_Layout_android_paddingBottom 7
int styleable ConstraintLayout_Layout_android_paddingEnd 8
int styleable ConstraintLayout_Layout_android_paddingLeft 9
int styleable ConstraintLayout_Layout_android_paddingRight 10
int styleable ConstraintLayout_Layout_android_paddingStart 11
int styleable ConstraintLayout_Layout_android_paddingTop 12
int styleable ConstraintLayout_Layout_android_visibility 13
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 14
int styleable ConstraintLayout_Layout_barrierDirection 15
int styleable ConstraintLayout_Layout_barrierMargin 16
int styleable ConstraintLayout_Layout_chainUseRtl 17
int styleable ConstraintLayout_Layout_constraintSet 18
int styleable ConstraintLayout_Layout_constraint_referenced_ids 19
int styleable ConstraintLayout_Layout_constraint_referenced_tags 20
int styleable ConstraintLayout_Layout_flow_firstHorizontalBias 21
int styleable ConstraintLayout_Layout_flow_firstHorizontalStyle 22
int styleable ConstraintLayout_Layout_flow_firstVerticalBias 23
int styleable ConstraintLayout_Layout_flow_firstVerticalStyle 24
int styleable ConstraintLayout_Layout_flow_horizontalAlign 25
int styleable ConstraintLayout_Layout_flow_horizontalBias 26
int styleable ConstraintLayout_Layout_flow_horizontalGap 27
int styleable ConstraintLayout_Layout_flow_horizontalStyle 28
int styleable ConstraintLayout_Layout_flow_lastHorizontalBias 29
int styleable ConstraintLayout_Layout_flow_lastHorizontalStyle 30
int styleable ConstraintLayout_Layout_flow_lastVerticalBias 31
int styleable ConstraintLayout_Layout_flow_lastVerticalStyle 32
int styleable ConstraintLayout_Layout_flow_maxElementsWrap 33
int styleable ConstraintLayout_Layout_flow_verticalAlign 34
int styleable ConstraintLayout_Layout_flow_verticalBias 35
int styleable ConstraintLayout_Layout_flow_verticalGap 36
int styleable ConstraintLayout_Layout_flow_verticalStyle 37
int styleable ConstraintLayout_Layout_flow_wrapMode 38
int styleable ConstraintLayout_Layout_layoutDescription 39
int styleable ConstraintLayout_Layout_layout_constrainedHeight 40
int styleable ConstraintLayout_Layout_layout_constrainedWidth 41
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 42
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 43
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 44
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 45
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 46
int styleable ConstraintLayout_Layout_layout_constraintCircle 47
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 48
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 49
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 50
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 51
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 52
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 53
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 54
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 55
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 56
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 57
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 58
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 59
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 60
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 61
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 62
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 63
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 64
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 65
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 66
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 67
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 68
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 69
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 70
int styleable ConstraintLayout_Layout_layout_constraintTag 71
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 72
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 73
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 74
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 75
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 76
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 77
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 78
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 79
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 80
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 81
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 82
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 83
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 84
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 85
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 86
int styleable ConstraintLayout_Layout_layout_goneMarginRight 87
int styleable ConstraintLayout_Layout_layout_goneMarginStart 88
int styleable ConstraintLayout_Layout_layout_goneMarginTop 89
int styleable ConstraintLayout_Layout_layout_optimizationLevel 90
int[] styleable ConstraintLayout_placeholder { 0x7f0400a7, 0x7f0400e4, 0x7f0401ed }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_emptyVisibility 1
int styleable ConstraintLayout_placeholder_placeholder_emptyVisibility 2
int[] styleable ConstraintSet { 0x101031f, 0x1010440, 0x10100d0, 0x10100f5, 0x10100fa, 0x10103b6, 0x10100f7, 0x10100f9, 0x10103b5, 0x10100f8, 0x10100f4, 0x1010120, 0x101011f, 0x1010140, 0x101013f, 0x10100c4, 0x10101b5, 0x10101b6, 0x1010326, 0x1010327, 0x1010328, 0x1010324, 0x1010325, 0x1010320, 0x1010321, 0x1010322, 0x1010323, 0x10103fa, 0x10100dc, 0x7f04002b, 0x7f04003f, 0x7f040040, 0x7f040041, 0x7f04006d, 0x7f0400a4, 0x7f0400a5, 0x7f0400ce, 0x7f0400da, 0x7f040103, 0x7f040104, 0x7f040105, 0x7f040106, 0x7f040107, 0x7f040108, 0x7f040109, 0x7f04010a, 0x7f04010b, 0x7f04010c, 0x7f04010d, 0x7f04010e, 0x7f04010f, 0x7f040111, 0x7f040112, 0x7f040113, 0x7f040114, 0x7f040115, 0x7f040164, 0x7f040165, 0x7f040166, 0x7f040167, 0x7f040168, 0x7f040169, 0x7f04016a, 0x7f04016b, 0x7f04016c, 0x7f04016d, 0x7f04016e, 0x7f04016f, 0x7f040170, 0x7f040171, 0x7f040172, 0x7f040173, 0x7f040174, 0x7f040175, 0x7f040176, 0x7f040177, 0x7f040178, 0x7f040179, 0x7f04017a, 0x7f04017b, 0x7f04017c, 0x7f04017d, 0x7f04017e, 0x7f04017f, 0x7f040180, 0x7f040181, 0x7f040182, 0x7f040183, 0x7f040184, 0x7f040185, 0x7f040186, 0x7f040187, 0x7f040188, 0x7f040189, 0x7f04018a, 0x7f04018b, 0x7f04018c, 0x7f04018d, 0x7f04018f, 0x7f040190, 0x7f040191, 0x7f040192, 0x7f040193, 0x7f040194, 0x7f040195, 0x7f040196, 0x7f0401c3, 0x7f0401c4, 0x7f0401e4, 0x7f0401ec, 0x7f040297, 0x7f040299 }
int styleable ConstraintSet_android_alpha 0
int styleable ConstraintSet_android_elevation 1
int styleable ConstraintSet_android_id 2
int styleable ConstraintSet_android_layout_height 3
int styleable ConstraintSet_android_layout_marginBottom 4
int styleable ConstraintSet_android_layout_marginEnd 5
int styleable ConstraintSet_android_layout_marginLeft 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginStart 8
int styleable ConstraintSet_android_layout_marginTop 9
int styleable ConstraintSet_android_layout_width 10
int styleable ConstraintSet_android_maxHeight 11
int styleable ConstraintSet_android_maxWidth 12
int styleable ConstraintSet_android_minHeight 13
int styleable ConstraintSet_android_minWidth 14
int styleable ConstraintSet_android_orientation 15
int styleable ConstraintSet_android_pivotX 16
int styleable ConstraintSet_android_pivotY 17
int styleable ConstraintSet_android_rotation 18
int styleable ConstraintSet_android_rotationX 19
int styleable ConstraintSet_android_rotationY 20
int styleable ConstraintSet_android_scaleX 21
int styleable ConstraintSet_android_scaleY 22
int styleable ConstraintSet_android_transformPivotX 23
int styleable ConstraintSet_android_transformPivotY 24
int styleable ConstraintSet_android_translationX 25
int styleable ConstraintSet_android_translationY 26
int styleable ConstraintSet_android_translationZ 27
int styleable ConstraintSet_android_visibility 28
int styleable ConstraintSet_animate_relativeTo 29
int styleable ConstraintSet_barrierAllowsGoneWidgets 30
int styleable ConstraintSet_barrierDirection 31
int styleable ConstraintSet_barrierMargin 32
int styleable ConstraintSet_chainUseRtl 33
int styleable ConstraintSet_constraint_referenced_ids 34
int styleable ConstraintSet_constraint_referenced_tags 35
int styleable ConstraintSet_deriveConstraintsFrom 36
int styleable ConstraintSet_drawPath 37
int styleable ConstraintSet_flow_firstHorizontalBias 38
int styleable ConstraintSet_flow_firstHorizontalStyle 39
int styleable ConstraintSet_flow_firstVerticalBias 40
int styleable ConstraintSet_flow_firstVerticalStyle 41
int styleable ConstraintSet_flow_horizontalAlign 42
int styleable ConstraintSet_flow_horizontalBias 43
int styleable ConstraintSet_flow_horizontalGap 44
int styleable ConstraintSet_flow_horizontalStyle 45
int styleable ConstraintSet_flow_lastHorizontalBias 46
int styleable ConstraintSet_flow_lastHorizontalStyle 47
int styleable ConstraintSet_flow_lastVerticalBias 48
int styleable ConstraintSet_flow_lastVerticalStyle 49
int styleable ConstraintSet_flow_maxElementsWrap 50
int styleable ConstraintSet_flow_verticalAlign 51
int styleable ConstraintSet_flow_verticalBias 52
int styleable ConstraintSet_flow_verticalGap 53
int styleable ConstraintSet_flow_verticalStyle 54
int styleable ConstraintSet_flow_wrapMode 55
int styleable ConstraintSet_layout_constrainedHeight 56
int styleable ConstraintSet_layout_constrainedWidth 57
int styleable ConstraintSet_layout_constraintBaseline_creator 58
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 59
int styleable ConstraintSet_layout_constraintBottom_creator 60
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 61
int styleable ConstraintSet_layout_constraintBottom_toTopOf 62
int styleable ConstraintSet_layout_constraintCircle 63
int styleable ConstraintSet_layout_constraintCircleAngle 64
int styleable ConstraintSet_layout_constraintCircleRadius 65
int styleable ConstraintSet_layout_constraintDimensionRatio 66
int styleable ConstraintSet_layout_constraintEnd_toEndOf 67
int styleable ConstraintSet_layout_constraintEnd_toStartOf 68
int styleable ConstraintSet_layout_constraintGuide_begin 69
int styleable ConstraintSet_layout_constraintGuide_end 70
int styleable ConstraintSet_layout_constraintGuide_percent 71
int styleable ConstraintSet_layout_constraintHeight_default 72
int styleable ConstraintSet_layout_constraintHeight_max 73
int styleable ConstraintSet_layout_constraintHeight_min 74
int styleable ConstraintSet_layout_constraintHeight_percent 75
int styleable ConstraintSet_layout_constraintHorizontal_bias 76
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 77
int styleable ConstraintSet_layout_constraintHorizontal_weight 78
int styleable ConstraintSet_layout_constraintLeft_creator 79
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 80
int styleable ConstraintSet_layout_constraintLeft_toRightOf 81
int styleable ConstraintSet_layout_constraintRight_creator 82
int styleable ConstraintSet_layout_constraintRight_toLeftOf 83
int styleable ConstraintSet_layout_constraintRight_toRightOf 84
int styleable ConstraintSet_layout_constraintStart_toEndOf 85
int styleable ConstraintSet_layout_constraintStart_toStartOf 86
int styleable ConstraintSet_layout_constraintTag 87
int styleable ConstraintSet_layout_constraintTop_creator 88
int styleable ConstraintSet_layout_constraintTop_toBottomOf 89
int styleable ConstraintSet_layout_constraintTop_toTopOf 90
int styleable ConstraintSet_layout_constraintVertical_bias 91
int styleable ConstraintSet_layout_constraintVertical_chainStyle 92
int styleable ConstraintSet_layout_constraintVertical_weight 93
int styleable ConstraintSet_layout_constraintWidth_default 94
int styleable ConstraintSet_layout_constraintWidth_max 95
int styleable ConstraintSet_layout_constraintWidth_min 96
int styleable ConstraintSet_layout_constraintWidth_percent 97
int styleable ConstraintSet_layout_editor_absoluteX 98
int styleable ConstraintSet_layout_editor_absoluteY 99
int styleable ConstraintSet_layout_goneMarginBottom 100
int styleable ConstraintSet_layout_goneMarginEnd 101
int styleable ConstraintSet_layout_goneMarginLeft 102
int styleable ConstraintSet_layout_goneMarginRight 103
int styleable ConstraintSet_layout_goneMarginStart 104
int styleable ConstraintSet_layout_goneMarginTop 105
int styleable ConstraintSet_motionProgress 106
int styleable ConstraintSet_motionStagger 107
int styleable ConstraintSet_pathMotionArc 108
int styleable ConstraintSet_pivotAnchor 109
int styleable ConstraintSet_transitionEasing 110
int styleable ConstraintSet_transitionPathRotate 111
int[] styleable CoordinatorLayout { 0x7f040158, 0x7f04022b }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x10100b3, 0x7f04015f, 0x7f040160, 0x7f040161, 0x7f04018e, 0x7f040197, 0x7f040198 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable CubicleView { 0x7f0400e9, 0x7f0400ea, 0x7f0400eb, 0x7f0400ec }
int styleable CubicleView_exampleColor 0
int styleable CubicleView_exampleDimension 1
int styleable CubicleView_exampleDrawable 2
int styleable CubicleView_exampleString 3
int[] styleable CustomAttribute { 0x7f040031, 0x7f0400c0, 0x7f0400c1, 0x7f0400c2, 0x7f0400c3, 0x7f0400c4, 0x7f0400c5, 0x7f0400c7, 0x7f0400c8 }
int styleable CustomAttribute_attributeName 0
int styleable CustomAttribute_customBoolean 1
int styleable CustomAttribute_customColorDrawableValue 2
int styleable CustomAttribute_customColorValue 3
int styleable CustomAttribute_customDimension 4
int styleable CustomAttribute_customFloatValue 5
int styleable CustomAttribute_customIntegerValue 6
int styleable CustomAttribute_customPixelDimension 7
int styleable CustomAttribute_customStringValue 8
int[] styleable DesignTheme { 0x7f04004e, 0x7f04004f, 0x7f040270 }
int styleable DesignTheme_bottomSheetDialogTheme 0
int styleable DesignTheme_bottomSheetStyle 1
int styleable DesignTheme_textColorError 2
int[] styleable DrawerArrowToggle { 0x7f04002f, 0x7f040030, 0x7f04003e, 0x7f040094, 0x7f0400db, 0x7f040123, 0x7f04021e, 0x7f040276 }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable FloatingActionButton { 0x7f04003c, 0x7f04003d, 0x7f04004a, 0x7f0400e3, 0x7f0400fa, 0x7f0400fb, 0x7f04012a, 0x7f040132, 0x7f0401b3, 0x7f0401f2, 0x7f040201, 0x7f040213, 0x7f04029f }
int styleable FloatingActionButton_backgroundTint 0
int styleable FloatingActionButton_backgroundTintMode 1
int styleable FloatingActionButton_borderWidth 2
int styleable FloatingActionButton_elevation 3
int styleable FloatingActionButton_fabCustomSize 4
int styleable FloatingActionButton_fabSize 5
int styleable FloatingActionButton_hideMotionSpec 6
int styleable FloatingActionButton_hoveredFocusedTranslationZ 7
int styleable FloatingActionButton_maxImageSize 8
int styleable FloatingActionButton_pressedTranslationZ 9
int styleable FloatingActionButton_rippleColor 10
int styleable FloatingActionButton_showMotionSpec 11
int styleable FloatingActionButton_useCompatPadding 12
int[] styleable FloatingActionButton_Behavior_Layout { 0x7f040042 }
int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
int[] styleable FlowLayout { 0x7f040151, 0x7f04019f }
int styleable FlowLayout_itemSpacing 0
int styleable FlowLayout_lineSpacing 1
int[] styleable FontFamily { 0x7f040118, 0x7f040119, 0x7f04011a, 0x7f04011b, 0x7f04011c, 0x7f04011d }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x7f040116, 0x7f04011e, 0x7f04011f, 0x7f040120, 0x7f04029d }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontStyle 1
int styleable FontFamilyFont_android_fontVariationSettings 2
int styleable FontFamilyFont_android_fontWeight 3
int styleable FontFamilyFont_android_ttcIndex 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable ForegroundLinearLayout { 0x1010109, 0x1010200, 0x7f040121 }
int styleable ForegroundLinearLayout_android_foreground 0
int styleable ForegroundLinearLayout_android_foregroundGravity 1
int styleable ForegroundLinearLayout_foregroundInsidePadding 2
int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
int styleable GradientColor_android_centerColor 0
int styleable GradientColor_android_centerX 1
int styleable GradientColor_android_centerY 2
int styleable GradientColor_android_endColor 3
int styleable GradientColor_android_endX 4
int styleable GradientColor_android_endY 5
int styleable GradientColor_android_gradientRadius 6
int styleable GradientColor_android_startColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_tileMode 10
int styleable GradientColor_android_type 11
int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable ImageFilterView { 0x7f04002a, 0x7f040059, 0x7f0400b5, 0x7f0400bd, 0x7f0401d7, 0x7f040202, 0x7f040205, 0x7f040207, 0x7f0402a3 }
int styleable ImageFilterView_altSrc 0
int styleable ImageFilterView_brightness 1
int styleable ImageFilterView_contrast 2
int styleable ImageFilterView_crossfade 3
int styleable ImageFilterView_overlay 4
int styleable ImageFilterView_round 5
int styleable ImageFilterView_roundPercent 6
int styleable ImageFilterView_saturation 7
int styleable ImageFilterView_warmth 8
int[] styleable ItemLayout { 0x7f040156 }
int styleable ItemLayout_item_layout 0
int[] styleable ItemView { 0x7f04002e, 0x7f0401ff, 0x7f040221, 0x7f04022d, 0x7f04029e }
int styleable ItemView_array_id 0
int styleable ItemView_relation 1
int styleable ItemView_spinner_array 2
int styleable ItemView_string_array 3
int styleable ItemView_underline 4
int[] styleable KeyAttribute { 0x101031f, 0x1010440, 0x1010326, 0x1010327, 0x1010328, 0x1010324, 0x1010325, 0x1010320, 0x1010321, 0x1010322, 0x1010323, 0x10103fa, 0x7f0400bf, 0x7f040122, 0x7f0401c3, 0x7f0401c5, 0x7f040297, 0x7f040299 }
int styleable KeyAttribute_android_alpha 0
int styleable KeyAttribute_android_elevation 1
int styleable KeyAttribute_android_rotation 2
int styleable KeyAttribute_android_rotationX 3
int styleable KeyAttribute_android_rotationY 4
int styleable KeyAttribute_android_scaleX 5
int styleable KeyAttribute_android_scaleY 6
int styleable KeyAttribute_android_transformPivotX 7
int styleable KeyAttribute_android_transformPivotY 8
int styleable KeyAttribute_android_translationX 9
int styleable KeyAttribute_android_translationY 10
int styleable KeyAttribute_android_translationZ 11
int styleable KeyAttribute_curveFit 12
int styleable KeyAttribute_framePosition 13
int styleable KeyAttribute_motionProgress 14
int styleable KeyAttribute_motionTarget 15
int styleable KeyAttribute_transitionEasing 16
int styleable KeyAttribute_transitionPathRotate 17
int[] styleable KeyCycle { 0x101031f, 0x1010440, 0x1010326, 0x1010327, 0x1010328, 0x1010324, 0x1010325, 0x1010322, 0x1010323, 0x10103fa, 0x7f0400bf, 0x7f040122, 0x7f0401c3, 0x7f0401c5, 0x7f040297, 0x7f040299, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7, 0x7f0402a8 }
int styleable KeyCycle_android_alpha 0
int styleable KeyCycle_android_elevation 1
int styleable KeyCycle_android_rotation 2
int styleable KeyCycle_android_rotationX 3
int styleable KeyCycle_android_rotationY 4
int styleable KeyCycle_android_scaleX 5
int styleable KeyCycle_android_scaleY 6
int styleable KeyCycle_android_translationX 7
int styleable KeyCycle_android_translationY 8
int styleable KeyCycle_android_translationZ 9
int styleable KeyCycle_curveFit 10
int styleable KeyCycle_framePosition 11
int styleable KeyCycle_motionProgress 12
int styleable KeyCycle_motionTarget 13
int styleable KeyCycle_transitionEasing 14
int styleable KeyCycle_transitionPathRotate 15
int styleable KeyCycle_waveOffset 16
int styleable KeyCycle_wavePeriod 17
int styleable KeyCycle_waveShape 18
int styleable KeyCycle_waveVariesBy 19
int[] styleable KeyPosition { 0x7f0400bf, 0x7f0400da, 0x7f040122, 0x7f040157, 0x7f0401c5, 0x7f0401e4, 0x7f0401e7, 0x7f0401e8, 0x7f0401e9, 0x7f0401ea, 0x7f04021a, 0x7f040297 }
int styleable KeyPosition_curveFit 0
int styleable KeyPosition_drawPath 1
int styleable KeyPosition_framePosition 2
int styleable KeyPosition_keyPositionType 3
int styleable KeyPosition_motionTarget 4
int styleable KeyPosition_pathMotionArc 5
int styleable KeyPosition_percentHeight 6
int styleable KeyPosition_percentWidth 7
int styleable KeyPosition_percentX 8
int styleable KeyPosition_percentY 9
int styleable KeyPosition_sizePercent 10
int styleable KeyPosition_transitionEasing 11
int[] styleable KeyTimeCycle { 0x101031f, 0x1010440, 0x1010326, 0x1010327, 0x1010328, 0x1010324, 0x1010325, 0x1010322, 0x1010323, 0x10103fa, 0x7f0400bf, 0x7f040122, 0x7f0401c3, 0x7f0401c5, 0x7f040297, 0x7f040299, 0x7f0402a4, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7 }
int styleable KeyTimeCycle_android_alpha 0
int styleable KeyTimeCycle_android_elevation 1
int styleable KeyTimeCycle_android_rotation 2
int styleable KeyTimeCycle_android_rotationX 3
int styleable KeyTimeCycle_android_rotationY 4
int styleable KeyTimeCycle_android_scaleX 5
int styleable KeyTimeCycle_android_scaleY 6
int styleable KeyTimeCycle_android_translationX 7
int styleable KeyTimeCycle_android_translationY 8
int styleable KeyTimeCycle_android_translationZ 9
int styleable KeyTimeCycle_curveFit 10
int styleable KeyTimeCycle_framePosition 11
int styleable KeyTimeCycle_motionProgress 12
int styleable KeyTimeCycle_motionTarget 13
int styleable KeyTimeCycle_transitionEasing 14
int styleable KeyTimeCycle_transitionPathRotate 15
int styleable KeyTimeCycle_waveDecay 16
int styleable KeyTimeCycle_waveOffset 17
int styleable KeyTimeCycle_wavePeriod 18
int styleable KeyTimeCycle_waveShape 19
int[] styleable KeyTrigger { 0x7f040122, 0x7f0401c5, 0x7f0401c6, 0x7f0401c7, 0x7f0401d0, 0x7f0401d2, 0x7f0401d3, 0x7f04029a, 0x7f04029b, 0x7f04029c }
int styleable KeyTrigger_framePosition 0
int styleable KeyTrigger_motionTarget 1
int styleable KeyTrigger_motion_postLayoutCollision 2
int styleable KeyTrigger_motion_triggerOnCollision 3
int styleable KeyTrigger_onCross 4
int styleable KeyTrigger_onNegativeCross 5
int styleable KeyTrigger_onPositiveCross 6
int styleable KeyTrigger_triggerId 7
int styleable KeyTrigger_triggerReceiver 8
int styleable KeyTrigger_triggerSlack 9
int[] styleable Layout { 0x10100f5, 0x10100fa, 0x10103b6, 0x10100f7, 0x10100f9, 0x10103b5, 0x10100f8, 0x10100f4, 0x10100c4, 0x7f04003f, 0x7f040040, 0x7f040041, 0x7f04006d, 0x7f0400a4, 0x7f0400a5, 0x7f040164, 0x7f040165, 0x7f040166, 0x7f040167, 0x7f040168, 0x7f040169, 0x7f04016a, 0x7f04016b, 0x7f04016c, 0x7f04016d, 0x7f04016e, 0x7f04016f, 0x7f040170, 0x7f040171, 0x7f040172, 0x7f040173, 0x7f040174, 0x7f040175, 0x7f040176, 0x7f040177, 0x7f040178, 0x7f040179, 0x7f04017a, 0x7f04017b, 0x7f04017c, 0x7f04017d, 0x7f04017e, 0x7f04017f, 0x7f040180, 0x7f040181, 0x7f040182, 0x7f040184, 0x7f040185, 0x7f040186, 0x7f040187, 0x7f040188, 0x7f040189, 0x7f04018a, 0x7f04018b, 0x7f04018c, 0x7f04018d, 0x7f04018f, 0x7f040190, 0x7f040191, 0x7f040192, 0x7f040193, 0x7f040194, 0x7f040195, 0x7f040196, 0x7f0401b2, 0x7f0401b5, 0x7f0401b8, 0x7f0401b9 }
int styleable Layout_android_layout_height 0
int styleable Layout_android_layout_marginBottom 1
int styleable Layout_android_layout_marginEnd 2
int styleable Layout_android_layout_marginLeft 3
int styleable Layout_android_layout_marginRight 4
int styleable Layout_android_layout_marginStart 5
int styleable Layout_android_layout_marginTop 6
int styleable Layout_android_layout_width 7
int styleable Layout_android_orientation 8
int styleable Layout_barrierAllowsGoneWidgets 9
int styleable Layout_barrierDirection 10
int styleable Layout_barrierMargin 11
int styleable Layout_chainUseRtl 12
int styleable Layout_constraint_referenced_ids 13
int styleable Layout_constraint_referenced_tags 14
int styleable Layout_layout_constrainedHeight 15
int styleable Layout_layout_constrainedWidth 16
int styleable Layout_layout_constraintBaseline_creator 17
int styleable Layout_layout_constraintBaseline_toBaselineOf 18
int styleable Layout_layout_constraintBottom_creator 19
int styleable Layout_layout_constraintBottom_toBottomOf 20
int styleable Layout_layout_constraintBottom_toTopOf 21
int styleable Layout_layout_constraintCircle 22
int styleable Layout_layout_constraintCircleAngle 23
int styleable Layout_layout_constraintCircleRadius 24
int styleable Layout_layout_constraintDimensionRatio 25
int styleable Layout_layout_constraintEnd_toEndOf 26
int styleable Layout_layout_constraintEnd_toStartOf 27
int styleable Layout_layout_constraintGuide_begin 28
int styleable Layout_layout_constraintGuide_end 29
int styleable Layout_layout_constraintGuide_percent 30
int styleable Layout_layout_constraintHeight_default 31
int styleable Layout_layout_constraintHeight_max 32
int styleable Layout_layout_constraintHeight_min 33
int styleable Layout_layout_constraintHeight_percent 34
int styleable Layout_layout_constraintHorizontal_bias 35
int styleable Layout_layout_constraintHorizontal_chainStyle 36
int styleable Layout_layout_constraintHorizontal_weight 37
int styleable Layout_layout_constraintLeft_creator 38
int styleable Layout_layout_constraintLeft_toLeftOf 39
int styleable Layout_layout_constraintLeft_toRightOf 40
int styleable Layout_layout_constraintRight_creator 41
int styleable Layout_layout_constraintRight_toLeftOf 42
int styleable Layout_layout_constraintRight_toRightOf 43
int styleable Layout_layout_constraintStart_toEndOf 44
int styleable Layout_layout_constraintStart_toStartOf 45
int styleable Layout_layout_constraintTop_creator 46
int styleable Layout_layout_constraintTop_toBottomOf 47
int styleable Layout_layout_constraintTop_toTopOf 48
int styleable Layout_layout_constraintVertical_bias 49
int styleable Layout_layout_constraintVertical_chainStyle 50
int styleable Layout_layout_constraintVertical_weight 51
int styleable Layout_layout_constraintWidth_default 52
int styleable Layout_layout_constraintWidth_max 53
int styleable Layout_layout_constraintWidth_min 54
int styleable Layout_layout_constraintWidth_percent 55
int styleable Layout_layout_editor_absoluteX 56
int styleable Layout_layout_editor_absoluteY 57
int styleable Layout_layout_goneMarginBottom 58
int styleable Layout_layout_goneMarginEnd 59
int styleable Layout_layout_goneMarginLeft 60
int styleable Layout_layout_goneMarginRight 61
int styleable Layout_layout_goneMarginStart 62
int styleable Layout_layout_goneMarginTop 63
int styleable Layout_maxHeight 64
int styleable Layout_maxWidth 65
int styleable Layout_minHeight 66
int styleable Layout_minWidth 67
int[] styleable LinearConstraintLayout { 0x10100c4 }
int styleable LinearConstraintLayout_android_orientation 0
int[] styleable LinearLayoutCompat { 0x1010126, 0x1010127, 0x10100af, 0x10100c4, 0x1010128, 0x7f0400d3, 0x7f0400d5, 0x7f0401b6, 0x7f040212 }
int styleable LinearLayoutCompat_android_baselineAligned 0
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 1
int styleable LinearLayoutCompat_android_gravity 2
int styleable LinearLayoutCompat_android_orientation 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x10100b3, 0x10100f5, 0x1010181, 0x10100f4 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_height 1
int styleable LinearLayoutCompat_Layout_android_layout_weight 2
int styleable LinearLayoutCompat_Layout_android_layout_width 3
int[] styleable ListPopupWindow { 0x10102ac, 0x10102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MaterialButton { 0x10101ba, 0x10101b7, 0x10101b8, 0x10101b9, 0x7f04003c, 0x7f04003d, 0x7f0400b8, 0x7f040133, 0x7f040135, 0x7f040136, 0x7f040137, 0x7f040139, 0x7f04013a, 0x7f040201, 0x7f04022e, 0x7f04022f }
int styleable MaterialButton_android_insetBottom 0
int styleable MaterialButton_android_insetLeft 1
int styleable MaterialButton_android_insetRight 2
int styleable MaterialButton_android_insetTop 3
int styleable MaterialButton_backgroundTint 4
int styleable MaterialButton_backgroundTintMode 5
int styleable MaterialButton_cornerRadius 6
int styleable MaterialButton_icon 7
int styleable MaterialButton_iconGravity 8
int styleable MaterialButton_iconPadding 9
int styleable MaterialButton_iconSize 10
int styleable MaterialButton_iconTint 11
int styleable MaterialButton_iconTintMode 12
int styleable MaterialButton_rippleColor 13
int styleable MaterialButton_strokeColor 14
int styleable MaterialButton_strokeWidth 15
int[] styleable MaterialCardView { 0x7f04022e, 0x7f04022f }
int styleable MaterialCardView_strokeColor 0
int styleable MaterialCardView_strokeWidth 1
int[] styleable MaterialComponentsTheme { 0x7f04004e, 0x7f04004f, 0x7f040077, 0x7f040081, 0x7f040085, 0x7f040095, 0x7f040096, 0x7f04009c, 0x7f04009d, 0x7f04009e, 0x7f0400e2, 0x7f040102, 0x7f0401ad, 0x7f0401ae, 0x7f0401cd, 0x7f040209, 0x7f04021b, 0x7f040251, 0x7f04025a, 0x7f04025b, 0x7f04025c, 0x7f04025d, 0x7f04025e, 0x7f04025f, 0x7f040260, 0x7f040261, 0x7f040262, 0x7f040263, 0x7f040268, 0x7f04026d, 0x7f04026e, 0x7f040273 }
int styleable MaterialComponentsTheme_bottomSheetDialogTheme 0
int styleable MaterialComponentsTheme_bottomSheetStyle 1
int styleable MaterialComponentsTheme_chipGroupStyle 2
int styleable MaterialComponentsTheme_chipStandaloneStyle 3
int styleable MaterialComponentsTheme_chipStyle 4
int styleable MaterialComponentsTheme_colorAccent 5
int styleable MaterialComponentsTheme_colorBackgroundFloating 6
int styleable MaterialComponentsTheme_colorPrimary 7
int styleable MaterialComponentsTheme_colorPrimaryDark 8
int styleable MaterialComponentsTheme_colorSecondary 9
int styleable MaterialComponentsTheme_editTextStyle 10
int styleable MaterialComponentsTheme_floatingActionButtonStyle 11
int styleable MaterialComponentsTheme_materialButtonStyle 12
int styleable MaterialComponentsTheme_materialCardViewStyle 13
int styleable MaterialComponentsTheme_navigationViewStyle 14
int styleable MaterialComponentsTheme_scrimBackground 15
int styleable MaterialComponentsTheme_snackbarButtonStyle 16
int styleable MaterialComponentsTheme_tabStyle 17
int styleable MaterialComponentsTheme_textAppearanceBody1 18
int styleable MaterialComponentsTheme_textAppearanceBody2 19
int styleable MaterialComponentsTheme_textAppearanceButton 20
int styleable MaterialComponentsTheme_textAppearanceCaption 21
int styleable MaterialComponentsTheme_textAppearanceHeadline1 22
int styleable MaterialComponentsTheme_textAppearanceHeadline2 23
int styleable MaterialComponentsTheme_textAppearanceHeadline3 24
int styleable MaterialComponentsTheme_textAppearanceHeadline4 25
int styleable MaterialComponentsTheme_textAppearanceHeadline5 26
int styleable MaterialComponentsTheme_textAppearanceHeadline6 27
int styleable MaterialComponentsTheme_textAppearanceOverline 28
int styleable MaterialComponentsTheme_textAppearanceSubtitle1 29
int styleable MaterialComponentsTheme_textAppearanceSubtitle2 30
int styleable MaterialComponentsTheme_textInputStyle 31
int[] styleable MenuGroup { 0x10101e0, 0x101000e, 0x10100d0, 0x10101de, 0x10101df, 0x1010194 }
int styleable MenuGroup_android_checkableBehavior 0
int styleable MenuGroup_android_enabled 1
int styleable MenuGroup_android_id 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_visible 5
int[] styleable MenuItem { 0x7f04000e, 0x7f040020, 0x7f040021, 0x7f040029, 0x10101e3, 0x10101e5, 0x1010106, 0x101000e, 0x1010002, 0x10100d0, 0x10101de, 0x10101e4, 0x101026f, 0x10101df, 0x10101e1, 0x10101e2, 0x1010194, 0x7f0400a8, 0x7f040139, 0x7f04013a, 0x7f0401cf, 0x7f040211, 0x7f04028f }
int styleable MenuItem_actionLayout 0
int styleable MenuItem_actionProviderClass 1
int styleable MenuItem_actionViewClass 2
int styleable MenuItem_alphabeticModifiers 3
int styleable MenuItem_android_alphabeticShortcut 4
int styleable MenuItem_android_checkable 5
int styleable MenuItem_android_checked 6
int styleable MenuItem_android_enabled 7
int styleable MenuItem_android_icon 8
int styleable MenuItem_android_id 9
int styleable MenuItem_android_menuCategory 10
int styleable MenuItem_android_numericShortcut 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_android_orderInCategory 13
int styleable MenuItem_android_title 14
int styleable MenuItem_android_titleCondensed 15
int styleable MenuItem_android_visible 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x101012f, 0x101012d, 0x1010130, 0x1010131, 0x101012c, 0x101012e, 0x10100ae, 0x7f0401f1, 0x7f040230 }
int styleable MenuView_android_headerBackground 0
int styleable MenuView_android_horizontalDivider 1
int styleable MenuView_android_itemBackground 2
int styleable MenuView_android_itemIconDisabledAlpha 3
int styleable MenuView_android_itemTextAppearance 4
int styleable MenuView_android_verticalDivider 5
int styleable MenuView_android_windowAnimationStyle 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable MockView { 0x7f0401ba, 0x7f0401bb, 0x7f0401bc, 0x7f0401bd, 0x7f0401be, 0x7f0401bf }
int styleable MockView_mock_diagonalsColor 0
int styleable MockView_mock_label 1
int styleable MockView_mock_labelBackgroundColor 2
int styleable MockView_mock_labelColor 3
int styleable MockView_mock_showDiagonals 4
int styleable MockView_mock_showLabel 5
int[] styleable Motion { 0x7f04002b, 0x7f0400da, 0x7f0401c2, 0x7f0401c4, 0x7f0401e4, 0x7f040297 }
int styleable Motion_animate_relativeTo 0
int styleable Motion_drawPath 1
int styleable Motion_motionPathRotate 2
int styleable Motion_motionStagger 3
int styleable Motion_pathMotionArc 4
int styleable Motion_transitionEasing 5
int[] styleable MotionHelper { 0x7f0401d1, 0x7f0401d4 }
int styleable MotionHelper_onHide 0
int styleable MotionHelper_onShow 1
int[] styleable MotionLayout { 0x7f04002c, 0x7f0400be, 0x7f04015c, 0x7f0401c0, 0x7f0401c3, 0x7f040214 }
int styleable MotionLayout_applyMotionScene 0
int styleable MotionLayout_currentState 1
int styleable MotionLayout_layoutDescription 2
int styleable MotionLayout_motionDebug 3
int styleable MotionLayout_motionProgress 4
int styleable MotionLayout_showPaths 5
int[] styleable MotionScene { 0x7f0400c9, 0x7f04015d }
int styleable MotionScene_defaultDuration 0
int styleable MotionScene_layoutDuringTransition 1
int[] styleable MotionTelltales { 0x7f040256, 0x7f040257, 0x7f040258 }
int styleable MotionTelltales_telltales_tailColor 0
int styleable MotionTelltales_telltales_tailScale 1
int styleable MotionTelltales_telltales_velocityMode 2
int[] styleable NavigationView { 0x10100d4, 0x10100dd, 0x101011f, 0x7f0400e3, 0x7f040125, 0x7f04014a, 0x7f04014b, 0x7f04014d, 0x7f04014f, 0x7f040152, 0x7f040155, 0x7f0401b7 }
int styleable NavigationView_android_background 0
int styleable NavigationView_android_fitsSystemWindows 1
int styleable NavigationView_android_maxWidth 2
int styleable NavigationView_elevation 3
int styleable NavigationView_headerLayout 4
int styleable NavigationView_itemBackground 5
int styleable NavigationView_itemHorizontalPadding 6
int styleable NavigationView_itemIconPadding 7
int styleable NavigationView_itemIconTint 8
int styleable NavigationView_itemTextAppearance 9
int styleable NavigationView_itemTextColor 10
int styleable NavigationView_menu 11
int[] styleable OnClick { 0x7f040087, 0x7f040255 }
int styleable OnClick_clickAction 0
int styleable OnClick_targetId 1
int[] styleable OnSwipe { 0x7f0400d7, 0x7f0400d8, 0x7f0400d9, 0x7f04019d, 0x7f0401af, 0x7f0401b4, 0x7f0401c8, 0x7f0401ce, 0x7f0401d5, 0x7f040290, 0x7f040291, 0x7f040292 }
int styleable OnSwipe_dragDirection 0
int styleable OnSwipe_dragScale 1
int styleable OnSwipe_dragThreshold 2
int styleable OnSwipe_limitBoundsTo 3
int styleable OnSwipe_maxAcceleration 4
int styleable OnSwipe_maxVelocity 5
int styleable OnSwipe_moveWhenScrollAtTop 6
int styleable OnSwipe_nestedScrollFlags 7
int styleable OnSwipe_onTouchUp 8
int styleable OnSwipe_touchAnchorId 9
int styleable OnSwipe_touchAnchorSide 10
int styleable OnSwipe_touchRegionId 11
int[] styleable PopupWindow { 0x10102c9, 0x1010176, 0x7f0401d6 }
int styleable PopupWindow_android_popupAnimationStyle 0
int styleable PopupWindow_android_popupBackground 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f040226 }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable PropertySet { 0x101031f, 0x10100dc, 0x7f040183, 0x7f0401c3, 0x7f0402a1 }
int styleable PropertySet_android_alpha 0
int styleable PropertySet_android_visibility 1
int styleable PropertySet_layout_constraintTag 2
int styleable PropertySet_motionProgress 3
int styleable PropertySet_visibilityMode 4
int[] styleable RecycleListView { 0x7f0401d8, 0x7f0401db }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x10100f1, 0x10100c4, 0x7f0400fc, 0x7f0400fd, 0x7f0400fe, 0x7f0400ff, 0x7f040100, 0x7f04015e, 0x7f040200, 0x7f04021d, 0x7f040224 }
int styleable RecyclerView_android_descendantFocusability 0
int styleable RecyclerView_android_orientation 1
int styleable RecyclerView_fastScrollEnabled 2
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 3
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 4
int styleable RecyclerView_fastScrollVerticalThumbDrawable 5
int styleable RecyclerView_fastScrollVerticalTrackDrawable 6
int styleable RecyclerView_layoutManager 7
int styleable RecyclerView_reverseLayout 8
int styleable RecyclerView_spanCount 9
int styleable RecyclerView_stackFromEnd 10
int[] styleable RoundProgressBar { 0x7f0401e6, 0x7f040203, 0x7f040204, 0x7f040206, 0x7f0402b3, 0x7f0402b4 }
int styleable RoundProgressBar_percent 0
int styleable RoundProgressBar_roundBackgroundColor 1
int styleable RoundProgressBar_roundForegroundColor 2
int styleable RoundProgressBar_roundWidth 3
int styleable RoundProgressBar_zeroPointRoundColor 4
int styleable RoundProgressBar_zeroPointRoundSize 5
int[] styleable ScrimInsetsFrameLayout { 0x7f040148 }
int styleable ScrimInsetsFrameLayout_insetForeground 0
int[] styleable ScrollingViewBehavior_Layout { 0x7f040045 }
int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
int[] styleable SearchView { 0x10100da, 0x1010264, 0x1010220, 0x101011f, 0x7f040088, 0x7f0400a0, 0x7f0400ca, 0x7f040124, 0x7f04013b, 0x7f04015b, 0x7f0401f5, 0x7f0401f6, 0x7f04020b, 0x7f04020c, 0x7f040231, 0x7f040236, 0x7f0402a2 }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_imeOptions 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_maxWidth 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable Snackbar { 0x7f04021b, 0x7f04021c }
int styleable Snackbar_snackbarButtonStyle 0
int styleable Snackbar_snackbarStyle 1
int[] styleable SnackbarLayout { 0x101011f, 0x7f0400e3, 0x7f0401b0 }
int styleable SnackbarLayout_android_maxWidth 0
int styleable SnackbarLayout_elevation 1
int styleable SnackbarLayout_maxActionInlineWidth 2
int[] styleable Spinner { 0x1010262, 0x10100b2, 0x1010176, 0x101017b, 0x7f0401ef }
int styleable Spinner_android_dropDownWidth 0
int styleable Spinner_android_entries 1
int styleable Spinner_android_popupBackground 2
int styleable Spinner_android_prompt 3
int styleable Spinner_popupTheme 4
int[] styleable State { 0x10100d0, 0x7f0400a6 }
int styleable State_android_id 0
int styleable State_constraints 1
int[] styleable StateListDrawable { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
int styleable StateListDrawable_android_constantSize 0
int styleable StateListDrawable_android_dither 1
int styleable StateListDrawable_android_enterFadeDuration 2
int styleable StateListDrawable_android_exitFadeDuration 3
int styleable StateListDrawable_android_variablePadding 4
int styleable StateListDrawable_android_visible 5
int[] styleable StateListDrawableItem { 0x1010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable StateSet { 0x7f0400cb }
int styleable StateSet_defaultState 0
int[] styleable SwitchCompat { 0x1010125, 0x1010124, 0x1010142, 0x7f040215, 0x7f040222, 0x7f040237, 0x7f040238, 0x7f04023a, 0x7f040277, 0x7f040278, 0x7f040279, 0x7f040293, 0x7f040294, 0x7f040295 }
int styleable SwitchCompat_android_textOff 0
int styleable SwitchCompat_android_textOn 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable TabItem { 0x1010002, 0x10100f2, 0x101014f }
int styleable TabItem_android_icon 0
int styleable TabItem_android_layout 1
int styleable TabItem_android_text 2
int[] styleable TabLayout { 0x7f04023b, 0x7f04023c, 0x7f04023d, 0x7f04023e, 0x7f04023f, 0x7f040240, 0x7f040241, 0x7f040242, 0x7f040243, 0x7f040244, 0x7f040245, 0x7f040246, 0x7f040247, 0x7f040248, 0x7f040249, 0x7f04024a, 0x7f04024b, 0x7f04024c, 0x7f04024d, 0x7f04024e, 0x7f04024f, 0x7f040250, 0x7f040252, 0x7f040253, 0x7f040254 }
int styleable TabLayout_tabBackground 0
int styleable TabLayout_tabContentStart 1
int styleable TabLayout_tabGravity 2
int styleable TabLayout_tabIconTint 3
int styleable TabLayout_tabIconTintMode 4
int styleable TabLayout_tabIndicator 5
int styleable TabLayout_tabIndicatorAnimationDuration 6
int styleable TabLayout_tabIndicatorColor 7
int styleable TabLayout_tabIndicatorFullWidth 8
int styleable TabLayout_tabIndicatorGravity 9
int styleable TabLayout_tabIndicatorHeight 10
int styleable TabLayout_tabInlineLabel 11
int styleable TabLayout_tabMaxWidth 12
int styleable TabLayout_tabMinWidth 13
int styleable TabLayout_tabMode 14
int styleable TabLayout_tabPadding 15
int styleable TabLayout_tabPaddingBottom 16
int styleable TabLayout_tabPaddingEnd 17
int styleable TabLayout_tabPaddingStart 18
int styleable TabLayout_tabPaddingTop 19
int styleable TabLayout_tabRippleColor 20
int styleable TabLayout_tabSelectedTextColor 21
int styleable TabLayout_tabTextAppearance 22
int styleable TabLayout_tabTextColor 23
int styleable TabLayout_tabUnboundedRipple 24
int[] styleable TextAppearance { 0x10103ac, 0x1010161, 0x1010162, 0x1010163, 0x1010164, 0x1010098, 0x101009a, 0x101009b, 0x1010095, 0x1010097, 0x1010096, 0x7f040117, 0x7f040259 }
int styleable TextAppearance_android_fontFamily 0
int styleable TextAppearance_android_shadowColor 1
int styleable TextAppearance_android_shadowDx 2
int styleable TextAppearance_android_shadowDy 3
int styleable TextAppearance_android_shadowRadius 4
int styleable TextAppearance_android_textColor 5
int styleable TextAppearance_android_textColorHint 6
int styleable TextAppearance_android_textColorLink 7
int styleable TextAppearance_android_textSize 8
int styleable TextAppearance_android_textStyle 9
int styleable TextAppearance_android_typeface 10
int styleable TextAppearance_fontFamily 11
int styleable TextAppearance_textAllCaps 12
int[] styleable TextInputLayout { 0x1010150, 0x101009a, 0x7f040050, 0x7f040051, 0x7f040052, 0x7f040053, 0x7f040054, 0x7f040055, 0x7f040056, 0x7f040057, 0x7f040058, 0x7f0400b9, 0x7f0400ba, 0x7f0400bb, 0x7f0400bc, 0x7f0400e7, 0x7f0400e8, 0x7f040127, 0x7f040128, 0x7f040129, 0x7f04012d, 0x7f04012e, 0x7f04012f, 0x7f0401df, 0x7f0401e0, 0x7f0401e1, 0x7f0401e2, 0x7f0401e3 }
int styleable TextInputLayout_android_hint 0
int styleable TextInputLayout_android_textColorHint 1
int styleable TextInputLayout_boxBackgroundColor 2
int styleable TextInputLayout_boxBackgroundMode 3
int styleable TextInputLayout_boxCollapsedPaddingTop 4
int styleable TextInputLayout_boxCornerRadiusBottomEnd 5
int styleable TextInputLayout_boxCornerRadiusBottomStart 6
int styleable TextInputLayout_boxCornerRadiusTopEnd 7
int styleable TextInputLayout_boxCornerRadiusTopStart 8
int styleable TextInputLayout_boxStrokeColor 9
int styleable TextInputLayout_boxStrokeWidth 10
int styleable TextInputLayout_counterEnabled 11
int styleable TextInputLayout_counterMaxLength 12
int styleable TextInputLayout_counterOverflowTextAppearance 13
int styleable TextInputLayout_counterTextAppearance 14
int styleable TextInputLayout_errorEnabled 15
int styleable TextInputLayout_errorTextAppearance 16
int styleable TextInputLayout_helperText 17
int styleable TextInputLayout_helperTextEnabled 18
int styleable TextInputLayout_helperTextTextAppearance 19
int styleable TextInputLayout_hintAnimationEnabled 20
int styleable TextInputLayout_hintEnabled 21
int styleable TextInputLayout_hintTextAppearance 22
int styleable TextInputLayout_passwordToggleContentDescription 23
int styleable TextInputLayout_passwordToggleDrawable 24
int styleable TextInputLayout_passwordToggleEnabled 25
int styleable TextInputLayout_passwordToggleTint 26
int styleable TextInputLayout_passwordToggleTintMode 27
int[] styleable ThemeEnforcement { 0x1010034, 0x7f0400e5, 0x7f0400e6 }
int styleable ThemeEnforcement_android_textAppearance 0
int styleable ThemeEnforcement_enforceMaterialTheme 1
int styleable ThemeEnforcement_enforceTextAppearance 2
int[] styleable Toolbar { 0x10100af, 0x1010140, 0x7f04005f, 0x7f040090, 0x7f040091, 0x7f0400a9, 0x7f0400aa, 0x7f0400ab, 0x7f0400ac, 0x7f0400ad, 0x7f0400ae, 0x7f0401ab, 0x7f0401ac, 0x7f0401b1, 0x7f0401ca, 0x7f0401cb, 0x7f0401ef, 0x7f040232, 0x7f040233, 0x7f040234, 0x7f04027f, 0x7f040281, 0x7f040282, 0x7f040283, 0x7f040284, 0x7f040285, 0x7f040286, 0x7f040287, 0x7f040288 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_navigationContentDescription 14
int styleable Toolbar_navigationIcon 15
int styleable Toolbar_popupTheme 16
int styleable Toolbar_subtitle 17
int styleable Toolbar_subtitleTextAppearance 18
int styleable Toolbar_subtitleTextColor 19
int styleable Toolbar_title 20
int styleable Toolbar_titleMargin 21
int styleable Toolbar_titleMarginBottom 22
int styleable Toolbar_titleMarginEnd 23
int styleable Toolbar_titleMarginStart 24
int styleable Toolbar_titleMarginTop 25
int styleable Toolbar_titleMargins 26
int styleable Toolbar_titleTextAppearance 27
int styleable Toolbar_titleTextColor 28
int[] styleable Transform { 0x1010440, 0x1010326, 0x1010327, 0x1010328, 0x1010324, 0x1010325, 0x1010320, 0x1010321, 0x1010322, 0x1010323, 0x10103fa }
int styleable Transform_android_elevation 0
int styleable Transform_android_rotation 1
int styleable Transform_android_rotationX 2
int styleable Transform_android_rotationY 3
int styleable Transform_android_scaleX 4
int styleable Transform_android_scaleY 5
int styleable Transform_android_transformPivotX 6
int styleable Transform_android_transformPivotY 7
int styleable Transform_android_translationX 8
int styleable Transform_android_translationY 9
int styleable Transform_android_translationZ 10
int[] styleable Transition { 0x10100d0, 0x7f040038, 0x7f0400a2, 0x7f0400a3, 0x7f0400df, 0x7f04015d, 0x7f0401c1, 0x7f0401e4, 0x7f040225, 0x7f040296, 0x7f040298 }
int styleable Transition_android_id 0
int styleable Transition_autoTransition 1
int styleable Transition_constraintSetEnd 2
int styleable Transition_constraintSetStart 3
int styleable Transition_duration 4
int styleable Transition_layoutDuringTransition 5
int styleable Transition_motionInterpolator 6
int styleable Transition_pathMotionArc 7
int styleable Transition_staggered 8
int styleable Transition_transitionDisable 9
int styleable Transition_transitionFlags 10
int[] styleable Variant { 0x7f0400a6, 0x7f0401fb, 0x7f0401fc, 0x7f0401fd, 0x7f0401fe }
int styleable Variant_constraints 0
int styleable Variant_region_heightLessThan 1
int styleable Variant_region_heightMoreThan 2
int styleable Variant_region_widthLessThan 3
int styleable Variant_region_widthMoreThan 4
int[] styleable View { 0x10100da, 0x1010000, 0x7f0401d9, 0x7f0401da, 0x7f040275 }
int styleable View_android_focusable 0
int styleable View_android_theme 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x10100d4, 0x7f04003c, 0x7f04003d }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewStubCompat { 0x10100d0, 0x10100f3, 0x10100f2 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_inflatedId 1
int styleable ViewStubCompat_android_layout 2
int[] styleable ViewfinderView { 0x7f04013f, 0x7f040140, 0x7f040141, 0x7f040142, 0x7f040143, 0x7f040144, 0x7f040145, 0x7f040146, 0x7f040147 }
int styleable ViewfinderView_inner_corner_color 0
int styleable ViewfinderView_inner_corner_length 1
int styleable ViewfinderView_inner_corner_width 2
int styleable ViewfinderView_inner_height 3
int styleable ViewfinderView_inner_margintop 4
int styleable ViewfinderView_inner_scan_bitmap 5
int styleable ViewfinderView_inner_scan_iscircle 6
int styleable ViewfinderView_inner_scan_speed 7
int styleable ViewfinderView_inner_width 8
