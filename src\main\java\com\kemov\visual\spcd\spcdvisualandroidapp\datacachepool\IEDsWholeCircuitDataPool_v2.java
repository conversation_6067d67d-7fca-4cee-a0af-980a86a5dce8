package com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool;

import android.app.Activity;
import android.content.Context;
import android.graphics.RectF;
import android.util.Log;

import com.kemov.parsescl.KIEDModel;
import com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.DataProcessTools;
import com.kemov.visual.spcd.spcdvisualandroidapp.activity.ied_whole_circuit.HandleForWholeCircuitsFix2;
import com.kemov.visual.spcd.spcdvisualandroidapp.activity.ied_whole_circuit.HandleForWholeCircuitsOptimizeEx;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.OdfSwitchPassBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.UnitConnectBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.bean.WholeCircuitBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_BOARD;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CABLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_CUBICLE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_INTCORE;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_REGION;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_UNIT;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDao;
import com.kemov.visual.spcd.spcdvisualandroidapp.database.dao.BaseDaoImp;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.CircuitConstants_v2;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.CubicleBean_v2;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.DeviceBean_v2;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.LinePort2PortBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.PortBaseBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.PortDirectionType;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.PortsCanvasBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.PortsLocation;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Constants;
import com.share.mycustomviewlib.bean.CtrlBlockBean;
import com.share.mycustomviewlib.bean.DataExchangeCachePool;
import com.share.mycustomviewlib.bean.ExtIedBean;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static com.kemov.parsescl.KIEDModel.TYPE_GS;
import static com.kemov.parsescl.KIEDModel.TYPE_SV;
import static com.kemov.parsescl.KIEDModel.TYPE_SV_GS;

public class IEDsWholeCircuitDataPool_v2 {
    private static final String TAG = "IEDsWholeCircuitDataPoo";
    public static final boolean IS_DEBUG = true;
    public static final int TYPE_GRAPHIC_IED_SWITCH = 3;
    public static final int TYPE_GRAPHIC_ODF = 4;
    public static final int TYPE_GRAPHIC_TX = 2;
    public int graphic_type = TYPE_GRAPHIC_IED_SWITCH;

    public static final int TYPE_FOR_PHYSIC_LINK = 0x1001;
    public static final int TYPE_FOR_PHYSIC_LINK_ALL = 0x1009;

    private static IEDsWholeCircuitDataPool_v2 sInstance = null;

    Context mCtx;
    String dbName;
    //spcd文件模型数据
    List<KM_SPCD_CUBICLE> spcd_cubicles;

    //图形绘制模型数据
    float width,height;
    public List<CubicleBean_v2> graphic_Cubicles = new ArrayList<>();
    Map<Integer,Object> rectFsInfo = new HashMap<>();
    List<RectF> cubicle_rects = new ArrayList<>();
    public List<LinePort2PortBean> linePort2PortBeanList = new ArrayList<>();

    public List<KM_SPCD_PORT> highlightPorts = new ArrayList<>();

    public KM_SPCD_UNIT startUnit = null;
    public KM_SPCD_UNIT endUnit = null;
    int dataForType = TYPE_FOR_PHYSIC_LINK;
    BaseDao<KM_SPCD_REGION,Integer> regionDao = null;

    private IEDsWholeCircuitDataPool_v2(Context mCtx,String dbName) {
        this.mCtx = mCtx;
        this.dbName = dbName;
        regionDao = new BaseDaoImp(mCtx, KM_SPCD_REGION.class, dbName);
    }

    public synchronized static IEDsWholeCircuitDataPool_v2 getInstance(Context mCtx,String dbName) {
        if (sInstance == null) {
            sInstance = new IEDsWholeCircuitDataPool_v2(mCtx,dbName);
        }
        return sInstance;
    }

    //产生数据（物理回路）
    public void setPortId(Context mCtx, String dbName, int portId,int type){
        dataForType = TYPE_FOR_PHYSIC_LINK;
        //拉取liugoujun的数据并组装。
        initData((Activity) mCtx,dbName, portId,type);
    }
    //产生数据(物理全回路)
    public void setPhysicLinkData(Object physicLinkData){
        dataForType = TYPE_FOR_PHYSIC_LINK_ALL;
        List<OdfSwitchPassBean> node_data = (List<OdfSwitchPassBean>) physicLinkData;
        initData2((Activity) mCtx,dbName, node_data);
    }

    //TODO:
    int[] cub_device_size = {2,1,1,2};

    //call after setPortId(...)
    //根据数据计算坐标
    public void initGraphic(float w, float h){
        width = w;
        height = h;

        initCubicleRects(cub_device_size);

        if (dataForType !=TYPE_FOR_PHYSIC_LINK_ALL){
            initGraphicCubicles_RealData();
        } else {
            initGraphicCubicles_RealData_New();
        }
        //initGraphicCubicles();
        initGraphicCables();
        
        updateAllRects();//根据屏柜和装置个数统一设置其RectF
        initOpticalFiber_RealData();//初始化连线
        //initOpticalFiber();//初始化连线
    }

    private void initCubicleRects(final int[] cub_device_size) {
        int lastIndexOfCubicle = cub_device_size.length-1;

        cubicle_rects.clear();
        getCubicleRectFByDeviceNum(lastIndexOfCubicle, cub_device_size);
        /*for (int i = 0; i < cub_device_size.length; i++) {
            cubicle_rects.add(getCubicleRectFByDeviceNum(i, cub_device_size));
        }*/
    }

    private void initGraphicCubicles() {
        graphic_Cubicles.clear();
        for (int i = 0; i < cub_device_size.length; i++) {
            CubicleBean_v2 graphic_Cubicle = new CubicleBean_v2();
            RectF cubicle_rect = cubicle_rects.get(i);
            graphic_Cubicle.setRectF(cubicle_rect);
            graphic_Cubicle.setInfo("第"+i+"个屏柜");

            for (int j = 0; j < cub_device_size[i]; j++) {
                DeviceBean_v2 graphic_Device = new DeviceBean_v2();
                graphic_Device.setInfo("第"+j+"个装置");
                RectF device_rect = getDeviceRectFInCubicleByDeviceIndex(cubicle_rect,j);
                graphic_Device.setRectF(device_rect);

                // TODO: guoyong 2019/11/1  设置装置内部Ports的数据。(需优化结构)
                PortsCanvasBean portsCanvasBean = new PortsCanvasBean();
                // TODO: guoyong 2019/11/1  获取装置内部ports的位置信息。
                PortsLocation portsLocation = PortsLocation.Begin;//PortsLocation.Middle_Through
                if (i==0&& j==0){
                    portsLocation = PortsLocation.Begin;
                } else if (i==cub_device_size.length-1 && j==cub_device_size[i]-1){
                    portsLocation = PortsLocation.End;
                } else {
                    portsLocation = PortsLocation.Middle_Through;
                }

                List<PortBaseBean> ports = new ArrayList<>();
                for (int k = 0; k < 2; k++) {
                    PortBaseBean portBaseBean = new PortBaseBean();
                    //设置端口属性信息
                    portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);
                    portBaseBean.setDirectionType(k==0?PortDirectionType.Tx:PortDirectionType.Rx);
                    if (portsLocation == PortsLocation.Begin){
                        portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);
                        portBaseBean.setDirectionType(k==0?PortDirectionType.Rx:PortDirectionType.Tx);
                    } else if (portsLocation == PortsLocation.End){
                        portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);
                        portBaseBean.setDirectionType(k==0?PortDirectionType.Tx:PortDirectionType.Rx);
                    } else if (portsLocation == PortsLocation.Middle_Through){
                        portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);
                        portBaseBean.setDirectionType(k==0?PortDirectionType.RT:PortDirectionType.RT);
                    }

                    portBaseBean.setRectF(null);
                    portBaseBean.setDetail("3n.1.2-Rx");//设置端口描述信息！！
                    //portBaseBean.initRectWithParentDevRect(device_rect);
                    ports.add(portBaseBean);
                }
                portsCanvasBean.setPorts(ports);//设置ports并初始化其rects
                portsCanvasBean.setPortsLocation(portsLocation);
                portsCanvasBean.intiPortsRects(device_rect);

                graphic_Device.setPortsCanvasBean(portsCanvasBean);
                graphic_Cubicle.addDevices(graphic_Device);

            }
            graphic_Cubicles.add(graphic_Cubicle);
        }

    }

    WholeCircuitBean data = null;
    public List<WholeCircuitBean.CubicleWholeBean> cubicles = null;
    public void initData(Activity mCtx,String dbName, int portId,int type){
        HandleForWholeCircuitsFix2 handleForWholeCircuitsFix2 = new HandleForWholeCircuitsFix2(mCtx, dbName, portId,type);
        data = handleForWholeCircuitsFix2.wholeCircuitBean0;
        if (data == null){
            return;
        }
        //for peace 20200708
        List<UnitConnectBean.UnitData> unitDataList = DataProcessTools.trimData(data).getUnits();
        if (unitDataList!=null&&unitDataList.size()>1){
            startUnit = unitDataList.get(0).getKm_spcd_unit();
            endUnit = unitDataList.get(unitDataList.size()-1).getKm_spcd_unit();
            initVirLinkInfos();
        }
        //绘图数据整理
        initDrawingData();
    }

    /**
     *  A-->B   A和C组成一对端口
     *  C-->D
     * @param mCtx
     * @param dbName
     * @param node_data
     */
    public void initData2(Activity mCtx,String dbName, List<OdfSwitchPassBean> node_data){
        final List<WholeCircuitBean.CubicleWholeBean> cubicleWholeBeans = new ArrayList<>();
        //全回路图数据转换为物理回路图数据
        for (int i = 0; i < node_data.size(); i++) {
            OdfSwitchPassBean node = node_data.get(i);
            //不进行高亮显示了2025-07-15
            /*if (i == 0 && node.getHighlightPorts()!=null){
                highlightPorts.addAll(node.getHighlightPorts());
            }*/

            WholeCircuitBean.CubicleWholeBean cubicleWholeBean = new WholeCircuitBean.CubicleWholeBean();
            cubicleWholeBean.setKm_spcd_cubicle(node.getKm_spcd_cubicle());

            WholeCircuitBean.UnitWholeBean theOppositePortInfo = node.getUnitWholeBean1();
            //包含在内的屏柜需要判断是否中间有其他屏柜，如果有则还需要添加进来,例如【A屏柜】【B屏柜】..【C屏柜】【A屏柜】【C屏柜】
            List<WholeCircuitBean.UnitWholeBean> unitWholeBeans = new ArrayList<>();
            if (i==0){
                /*unitWholeBean.getPort();//端口no
                unitWholeBean.getPort1();//端口n0
                unitWholeBean.getPortA();//端口direction
                unitWholeBean.getPortA1();//端口direction
                unitWholeBean.getBoard();//板卡no
                unitWholeBean.getBoard1();//板卡no
                unitWholeBean.getBoardPort();//板卡no-端口no*/

                WholeCircuitBean.UnitWholeBean unitWholeBean0= node.getUnitWholeBean();
                if (dataForType==TYPE_FOR_PHYSIC_LINK_ALL&&unitWholeBean0.getKm_spcd_unit().getDev_class().equals("SWITCH")){
                    if (theOppositePortInfo!=null) unitWholeBean0.setPortBDirection(theOppositePortInfo.getPortADirection());
                    unitWholeBean0.setPortCDirection(node.getUnitWholeBean().getPortA1Direction());//fix
                    if (theOppositePortInfo!=null) unitWholeBean0.setPortDDirection(theOppositePortInfo.getPortA1Direction());
                    if (theOppositePortInfo!=null) unitWholeBean0.setPortBDesc(theOppositePortInfo.getPortADesc());
                    unitWholeBean0.setPortCDesc(node.getUnitWholeBean().getPortBDesc());//fix
                    if (theOppositePortInfo!=null) unitWholeBean0.setPortDDesc(theOppositePortInfo.getPortBDesc());
                }else{
                    if (theOppositePortInfo!=null) {
                        unitWholeBean0.setPortBDirection(theOppositePortInfo.getPortADirection());
                        unitWholeBean0.setPortDDirection(theOppositePortInfo.getPortBDirection());
                        unitWholeBean0.setPortBDesc(theOppositePortInfo.getPortADesc());
                        unitWholeBean0.setPortDDesc(theOppositePortInfo.getPortBDesc());
                    }
                }
                FixedUnitWholeBeanDirectionError(unitWholeBean0);
                unitWholeBeans.add(unitWholeBean0);
                cubicleWholeBean.setUnitWholeBeans(unitWholeBeans);
                cubicleWholeBeans.add(cubicleWholeBean);
            }else {
                if (!node_data.get(i-1).getKm_spcd_cubicle().getId()
                        .equals(node_data.get(i).getKm_spcd_cubicle().getId())){
                    WholeCircuitBean.UnitWholeBean unitWholeBean0= node.getUnitWholeBean();
                    if (dataForType==TYPE_FOR_PHYSIC_LINK_ALL&&unitWholeBean0.getKm_spcd_unit().getDev_class().equals("SWITCH")){
                        if (theOppositePortInfo!=null) {
                            unitWholeBean0.setPortBDirection(theOppositePortInfo.getPortADirection());

                        }
                        unitWholeBean0.setPortCDirection(node.getUnitWholeBean().getPortA1Direction());//fix
                        if (theOppositePortInfo!=null){
                            unitWholeBean0.setPortDDirection(theOppositePortInfo.getPortA1Direction());
                            if((unitWholeBean0.getPortCDirection()!=null)//如果端口方向不一致修复它
                                    &&(unitWholeBean0.getPortDDirection()!=null)
                                    &&(!unitWholeBean0.getPortDDirection().equals("RT"))
                                    &&(!HandleForWholeCircuitsOptimizeEx.getTxRx(unitWholeBean0.getPortCDirection()).equals(unitWholeBean0.getPortDDirection())))
                                unitWholeBean0.setPortDDirection(HandleForWholeCircuitsOptimizeEx.getTxRx(unitWholeBean0.getPortCDirection()));
                        }
                        if (theOppositePortInfo!=null) unitWholeBean0.setPortBDesc(theOppositePortInfo.getPortADesc());
                        unitWholeBean0.setPortCDesc(node.getUnitWholeBean().getPortBDesc());//fix
                        if (theOppositePortInfo!=null) unitWholeBean0.setPortDDesc(theOppositePortInfo.getPortBDesc());
                    }else{
                        if (theOppositePortInfo!=null){
                            unitWholeBean0.setPortBDirection(theOppositePortInfo.getPortADirection());
                            unitWholeBean0.setPortDDirection(theOppositePortInfo.getPortBDirection());
                            unitWholeBean0.setPortBDesc(theOppositePortInfo.getPortADesc());
                            unitWholeBean0.setPortDDesc(theOppositePortInfo.getPortBDesc());
                        }
                    }
                    FixedUnitWholeBeanDirectionError(unitWholeBean0);
                    unitWholeBeans.add(unitWholeBean0);
                    cubicleWholeBean.setUnitWholeBeans(unitWholeBeans);

                    cubicleWholeBeans.add(cubicleWholeBean);
                } else{
                    WholeCircuitBean.UnitWholeBean unitWholeBean0= node.getUnitWholeBean();
                    if (dataForType==TYPE_FOR_PHYSIC_LINK_ALL&&unitWholeBean0.getKm_spcd_unit().getDev_class().equals("SWITCH")){
                        //portA
                        //portB
                        if (theOppositePortInfo!=null) unitWholeBean0.setPortBDirection(theOppositePortInfo.getPortADirection());
                        //portC
                        unitWholeBean0.setPortCDirection(node.getUnitWholeBean().getPortA1Direction());//fix
                        //portD
                        if (theOppositePortInfo!=null) unitWholeBean0.setPortDDirection(theOppositePortInfo.getPortA1Direction());
                        //portAdsc
                        //portBdsc
                        if (theOppositePortInfo!=null) unitWholeBean0.setPortBDesc(theOppositePortInfo.getPortADesc());
                        //portCdsc
                        unitWholeBean0.setPortCDesc(node.getUnitWholeBean().getPortBDesc());//fix
                        //portDdsc
                        if (theOppositePortInfo!=null) unitWholeBean0.setPortDDesc(theOppositePortInfo.getPortBDesc());
                    }else{
                        if (theOppositePortInfo!=null){
                            unitWholeBean0.setPortBDirection(theOppositePortInfo.getPortADirection());
                            unitWholeBean0.setPortDDirection(theOppositePortInfo.getPortBDirection());
                            unitWholeBean0.setPortBDesc(theOppositePortInfo.getPortADesc());
                            unitWholeBean0.setPortDDesc(theOppositePortInfo.getPortBDesc());
                        }
                    }
                    FixedUnitWholeBeanDirectionError(unitWholeBean0);
                    unitWholeBeans = cubicleWholeBeans.get(cubicleWholeBeans.size()-1).getUnitWholeBeans();
                    unitWholeBeans.add(unitWholeBean0);
                }
            }

            if (IS_DEBUG){
                StringBuffer stringBuffer = new StringBuffer("链路数据打印：");
                stringBuffer
                        .append(node.getKm_spcd_cubicle().getName()).append(",")
                        .append(node.getKm_spcd_unit().getName()).append(",")
                        .append("portA&B[")
                        .append(node.getPortA()).append(",")
                        .append(node.getUnitWholeBean().getKm_spcd_unit().getName()+"."+node.getUnitWholeBean().getBoard1()+"."+node.getUnitWholeBean().getPort1()+"-"+node.getUnitWholeBean().getPortA1Direction())
                        .append("]");

                Log.e(TAG, "initData2: "+stringBuffer.toString() );
            }
        }
        //链路数据问题
        if (node_data==null||node_data.size()<2) return;
        startUnit = node_data.get(0).getKm_spcd_unit();
        endUnit = node_data.get(node_data.size()-1).getKm_spcd_unit();
        //初始化 虚连接信息！
        initVirLinkInfos();

        //转换完成，赋值
        WholeCircuitBean instance = WholeCircuitBean.getInstance();
        instance.setCubicleWholeBeans(cubicleWholeBeans);
        //instance.setHighlightPorts(highlightPorts);
        instance.setType(-1);
        //
        data = instance;
        if (data == null){
            return;
        }
        //绘图数据整理
        initDrawingData();
    }

    /**
     * A-->B   A和C组成一对端口
     * C-->D
     * 认为PortA的方向是正确的，然后修正PortB  PortC portD PortADirection=PortDDirection
     * @param unitWholeBean
     */
    private void FixedUnitWholeBeanDirectionError(WholeCircuitBean.UnitWholeBean unitWholeBean){
        if((unitWholeBean.getPortA1Direction()!=null)
                &&(!unitWholeBean.getPortA1Direction().equals("RT"))
                &&(!HandleForWholeCircuitsOptimizeEx.getTxRx(unitWholeBean.getPortADirection()).equals(unitWholeBean.getPortA1Direction())))
            unitWholeBean.setPortA1Direction(HandleForWholeCircuitsOptimizeEx.getTxRx(unitWholeBean.getPortADirection()));
        if((unitWholeBean.getPortBDirection()!=null)//如果端口B方向不一致修复它
                &&(!unitWholeBean.getPortBDirection().equals("RT"))
                &&(!HandleForWholeCircuitsOptimizeEx.getTxRx(unitWholeBean.getPortADirection()).equals(unitWholeBean.getPortBDirection())))
            unitWholeBean.setPortBDirection(HandleForWholeCircuitsOptimizeEx.getTxRx(unitWholeBean.getPortADirection()));
        if((unitWholeBean.getPortCDirection()!=null)//如果端口C方向不一致修复它
                &&(!HandleForWholeCircuitsOptimizeEx.getTxRx(unitWholeBean.getPortADirection()).equals(unitWholeBean.getPortCDirection())))
            unitWholeBean.setPortCDirection(HandleForWholeCircuitsOptimizeEx.getTxRx(unitWholeBean.getPortADirection()));
        if((unitWholeBean.getPortDDirection()!=null)//如果端口B方向不一致修复它
                &&(!unitWholeBean.getPortDDirection().equals("RT"))
                &&(!unitWholeBean.getPortADirection().equals(unitWholeBean.getPortDDirection())))
            unitWholeBean.setPortDDirection(unitWholeBean.getPortADirection());
    }
    //数据加工20200704
    public Map<String,List<String>> map_appIds = new HashMap<>();
    public String [] appid_items;
    KIEDModel kIED = null;
    private void initVirLinkInfos() {
        if (startUnit!=null &&endUnit!=null){
            if (startUnit.getDev_class().equals("IED")&&endUnit.getDev_class().equals("IED")){
                String startIedName = startUnit.getIed_name();
                String endIedName = endUnit.getIed_name();
                //获取虚连接信息！
                kIED = DataProcessTools.getKIEDMode(startIedName);
                DataExchangeCachePool.getInstance().setKIED(kIED);
            } else {
                //Toast.makeText(mCtx, "起终点装置均为IED时才可以查看虚连接信息！", Toast.LENGTH_SHORT).show();
                return;
            }
        } else {
            //不是从ied的背板图点击进入的不看虚连接信息！
            return;
        }

        List<String> appids_send = new ArrayList<>();
        List<String> appids_recv = new ArrayList<>();
        Log.i(TAG, "1****************HOW ABOUT?**********************");
        if(kIED==null || kIED.getVIEDs()==null){
            Log.e(TAG, "kIED.getVIEDs() = null");
            return;
        }

        ExtIedBean tempExtIED = new ExtIedBean();
        for (KIEDModel.HLP_VircIED vIED : kIED.getVIEDs()) {
            if (vIED.sIedName.equals(endUnit.getIed_name())){int tempIedSvNum = vIED.sizeOfSendVCtrl(TYPE_SV);//Sum of SV send Ctrls
                int tempIedGsNum = vIED.sizeOfSendVCtrl(TYPE_GS);//Sum of Gs send Ctrls

                tempExtIED.setIdeName(vIED.sIedName);
                tempExtIED.setIedDescInfo(vIED.getVIedDescInfo());
                tempExtIED.setSubscriber(vIED.isHasRecv());
                tempExtIED.setSvGsSum(tempIedSvNum, tempIedGsNum);
                Log.e(TAG, "2****************HOW ABOUT?**********************");
                //SV CtrlBlocks Send to
                for (KIEDModel.HLP_VircCtrl vIedCtrl_SV : vIED.getVSendCtrls(TYPE_SV)) {//add sv ctrlBlocks
                    CtrlBlockBean tempCtrlBean_SV = new CtrlBlockBean();
                    String appid = vIedCtrl_SV.getAppId();

                    tempCtrlBean_SV.setAppID(appid)
                            .setGoose(false)
                            .setReciver(false);
                    tempExtIED.ctrlBlocks.add(tempCtrlBean_SV);
                    appids_send.add("SV 0x"+appid);
                    Log.i(TAG, "3****************HOW ABOUT?**********************");
                }
                map_appIds.put("send",appids_send);

                //GS CtrlBlocks Send to
                for (KIEDModel.HLP_VircCtrl vIedCtrl_GS : vIED.getVSendCtrls(TYPE_GS)) {//add sv ctrlBlocks
                    CtrlBlockBean tempCtrlBean_GS = new CtrlBlockBean();
                    String appid = vIedCtrl_GS.getAppId();

                    tempCtrlBean_GS.setAppID(appid)
                            .setGoose(true)
                            .setReciver(false);
                    tempExtIED.ctrlBlocks.add(tempCtrlBean_GS);
                    appids_send.add("G00SE 0x"+appid);
                    Log.i(TAG, "4****************HOW ABOUT?**********************");
                }
                map_appIds.put("send",appids_send);
                //在这里设置外部IED接收中心IED的控制块list
                ArrayList<String> recList = new ArrayList<String>();
                for (KIEDModel.HLP_VircCtrl vCtrl : vIED.getVRecCtrls(TYPE_SV_GS)) {
                    recList.add(vCtrl.getAppId());
                    if (vCtrl.getAppId().startsWith("4")){
                        appids_recv.add("SV 0x"+vCtrl.getAppId());
                    } else {
                        appids_recv.add("GOOSE 0x"+vCtrl.getAppId());
                    }
                }
                map_appIds.put("recv",appids_recv);
                tempExtIED.setRecvCIedCtrlBlist(recList);

                //gy 20200704不执行该代码
                if(tempExtIED.IsSubscriber()&&false){//Does Receive CtrlBlocks exit?  当还有接收到中心IED的控制块 则给该extIED添加一个接受块儿
                    CtrlBlockBean tempCtrlBean_Rec = new CtrlBlockBean();
                    tempCtrlBean_Rec.setReciver(true);
                    tempExtIED.ctrlBlocks.add(tempCtrlBean_Rec);
                }
                ArrayList<Integer> recIntList = new ArrayList<Integer>();
                for (KIEDModel.HLP_VircCtrl vCtrl : vIED.getVRecCtrls(TYPE_SV)) {
                    recIntList.add(TYPE_SV);
                }
                for (KIEDModel.HLP_VircCtrl vCtrl : vIED.getVRecCtrls(TYPE_GS)) {
                    recIntList.add(TYPE_GS);
                }

                tempExtIED.setRecvCtrlBsGsSVFromCIed(recIntList);

                break;
            }
        }

        String [] temp= new String[]{};
        List<String> ssss = map_appIds.get("send");
        if (ssss!=null){
            ssss.addAll(map_appIds.get("recv"));
            temp = ssss.toArray(temp);
            appid_items = temp;
        }
    }

    //判断屏柜列表中是否包含某屏柜
    boolean isCubiclesContainsCubicle(List<WholeCircuitBean.CubicleWholeBean> cubicleWholeBeans,
                                  WholeCircuitBean.CubicleWholeBean cubicleWholeBean){
        for (WholeCircuitBean.CubicleWholeBean e:cubicleWholeBeans){
            if (cubicleWholeBean.getKm_spcd_cubicle().getId().equals(e.getKm_spcd_cubicle().getId())){
                return true;
            }
        }
        return false;
    }
    //判断屏柜中是否包含某装置
    boolean isCubicleContainsUnit(WholeCircuitBean.CubicleWholeBean cubicleWholeBean,WholeCircuitBean.UnitWholeBean unitWholeBean) {
        for (WholeCircuitBean.UnitWholeBean e:cubicleWholeBean.getUnitWholeBeans()){
            if (e.getKm_spcd_unit().getId().equals(unitWholeBean.getKm_spcd_unit().getId())){
                return true;
            }
        }
        return false;
    }

    private void initDrawingData() {
        cubicles = data.getCubicleWholeBeans();

        if (data != null) {
            graphic_type = data.getType();
            //if (graphic_type == TYPE_GRAPHIC_TX) {
            //    highlightPorts = data.getHighlightPorts();
            //}
        }

        if (cubicles == null || cubicles.isEmpty()) return;

        //适应-1
        BaseDao<KM_SPCD_PORT, Integer> portDao = null;
        BaseDao<KM_SPCD_BOARD, Integer> boardDao = null;
        BaseDao<KM_SPCD_UNIT, Integer> unitDao = null;
        if (unitDao == null) unitDao = new BaseDaoImp(mCtx, KM_SPCD_UNIT.class, dbName);
        if (boardDao == null) boardDao = new BaseDaoImp(mCtx, KM_SPCD_BOARD.class, dbName);
        if (portDao == null) portDao = new BaseDaoImp(mCtx, KM_SPCD_PORT.class, dbName);

        //屏柜中装置个数初始化
        cub_device_size = new int[cubicles.size()];
        for (int i = 0; i < cubicles.size(); i++) {
            WholeCircuitBean.CubicleWholeBean cubicle = cubicles.get(i);
            cub_device_size[i] = cubicle.getUnitWholeBeans().size();

            //适应-2 屏柜中的所有交换机数据转换！！
            if (dataForType == TYPE_FOR_PHYSIC_LINK_ALL){
                for (int j = 0;j<cubicle.getUnitWholeBeans().size();j++) {
                    WholeCircuitBean.UnitWholeBean unitWholeBean = cubicle.getUnitWholeBeans().get(j);
                    if (unitWholeBean.getKm_spcd_unit().getDev_class().equals("SWITCH")){
                        unitWholeBean.getPort();//端口no
                        unitWholeBean.getPort1();//端口n0
                        unitWholeBean.getPortADirection();//端口direction
                        unitWholeBean.getPortA1Direction();//端口direction
                        unitWholeBean.getBoard();//板卡no
                        unitWholeBean.getBoard1();//板卡no
                        unitWholeBean.getBoardPort();//板卡no-端口no

                        unitWholeBean.setPortCDirection(unitWholeBean.getPortA1Direction());
                        String boardDescA = null;
                        String boardDescB = null;
                        String boardDescC = null;
                        String boardDescD = null;
                        try {
                            boardDescA = boardDao.getDao().queryBuilder().where()
                                    .eq("unit_id",unitWholeBean.getKm_spcd_unit().getId())
                                    .and()
                                    .eq("slot",unitWholeBean.getBoard()).queryForFirst().getDescription();
                            boardDescB = boardDao.getDao().queryBuilder().where()
                                    .eq("unit_id",unitWholeBean.getKm_spcd_unit().getId())
                                    .and()
                                    .eq("slot",unitWholeBean.getBoard()).queryForFirst().getDescription();
                            boardDescC = boardDao.getDao().queryBuilder().where()
                                    .eq("unit_id",unitWholeBean.getKm_spcd_unit().getId())
                                    .and()
                                    .eq("slot",unitWholeBean.getBoard()).queryForFirst().getDescription();
                            boardDescD = boardDao.getDao().queryBuilder().where()
                                    .eq("unit_id",unitWholeBean.getKm_spcd_unit().getId())
                                    .and()
                                    .eq("slot",unitWholeBean.getBoard()).queryForFirst().getDescription();
                        } catch (SQLException e) {
                            e.printStackTrace();
                        }

                        unitWholeBean.setPortADesc(boardDescA);//板卡desc-端口no
                        unitWholeBean.setPortBDesc(boardDescB);//板卡desc-端口no
                        unitWholeBean.setPortCDesc(boardDescC);
                        unitWholeBean.setPortDDesc(boardDescD);
                        unitWholeBean.setPortADirection(unitWholeBean.getPortADirection());
                        unitWholeBean.setPortBDirection(unitWholeBean.getPortA1Direction());
                        unitWholeBean.setPortCDirection(unitWholeBean.getPortA1Direction());
                        unitWholeBean.setPortDDirection(unitWholeBean.getPortADirection());

                        if (i==cubicles.size()-1&&j==cubicle.getUnitWholeBeans().size()-1&&unitWholeBean.getPortA1Direction()==null){
                            unitWholeBean.setPortCDesc(null);
                            unitWholeBean.setPortDDesc(null);
                            unitWholeBean.setPortADirection("Rx");
                            unitWholeBean.setPortBDirection("Tx");
                            unitWholeBean.setPortCDirection(null);
                            unitWholeBean.setPortDDirection(null);
                        }
                    }
                }
            }
        }
    }

    private void initGraphicCubicles_RealData(){
        graphic_Cubicles.clear();
        for (int i = 0; i < cubicles.size(); i++) {
            WholeCircuitBean.CubicleWholeBean cubicle = cubicles.get(i);

            CubicleBean_v2 graphic_Cubicle = new CubicleBean_v2();
            RectF cubicle_rect = cubicle_rects.get(i);
            graphic_Cubicle.setRectF(cubicle_rect);
            graphic_Cubicle.setInfo(cubicle.getKm_spcd_cubicle().getInfo());
            //获取到屏柜所属小室
            KM_SPCD_REGION region = regionDao.getById(cubicle.getKm_spcd_cubicle().getRegion_id());
            graphic_Cubicle.setRegionBeyondTo(region.getDescription());

            for (int j = 0; j < cub_device_size[i]; j++) {
                WholeCircuitBean.UnitWholeBean unit = cubicle.getUnitWholeBeans().get(j);

                DeviceBean_v2 graphic_Device = new DeviceBean_v2();
                graphic_Device.setInfo(unit.getKm_spcd_unit().getInfo());
                RectF device_rect = getDeviceRectFInCubicleByDeviceIndex(cubicle_rect,j);
                graphic_Device.setRectF(device_rect);
                //设置所属装置信息
                graphic_Device.setBeyondTo(unit.getKm_spcd_unit());
                if (i==cub_device_size.length-1){
                    graphic_Device.setLastCubicle(true);
                }
                if (j==cub_device_size[i]-1){
                    graphic_Device.setLastUnit(true);
                }

                List<String> lgj_ports = unit.getPortList();
                boolean isPortDataRight = true;
                try {
                    if (lgj_ports.size()!=unit.getSpcdPort(mCtx,dbName).size()){
                        //走到这个地方还有可能是spcd文件没有正确配置，因全回路信息都是通过intcore\cable-core来查找的，
                        // 可能最终通过intcore找到的port在intcore所在的cubicle中找不到指定的port。（p.s.但路径intcore却存在，所以lgj数据中的
                        // km_spcd_intcoreA在此种情况下存在则全回路数据是不正确的）
                        //throw new RuntimeException("所给的全回路数据中 portABCD和portABCD的dsc不为null的数据不对应所致");
                        Log.e(TAG, "initGraphicCubicles_RealData: 所给的物理全回路的数据中,装置：" + unit.getKm_spcd_unit().getInfo()
                                +" 端口错误！");
                        continue;//(continue会导致少绘制一个装置)
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }
                // guoyong 2019/11/1  设置装置内部Ports的数据。(需优化结构)
                PortsCanvasBean portsCanvasBean = new PortsCanvasBean();
                //guoyong 2019/11/1  获取装置内部ports的位置信息。
                PortsLocation portsLocation = PortsLocation.Begin;//PortsLocation.Middle_Through
                if (i==0&& j==0){
                    portsLocation = PortsLocation.Begin;
                } else if (i==cub_device_size.length-1 && j==cub_device_size[i]-1){
                    portsLocation = PortsLocation.End;
                } else {
                    if (lgj_ports.size()==2){
                        portsLocation = PortsLocation.Middle_Through;
                    } else if (lgj_ports.size()==4){
                        portsLocation = PortsLocation.Middle_ShortCircuited;
                    } else {
                        /*throw new RuntimeException("端口数据应该为2或者4");*/
                        portsLocation = PortsLocation.Middle_Through;
                    }
                }

                List<PortBaseBean> ports = new ArrayList<>();

                //if (lgj_ports.size()==lgj_ports_Desc.size()) throw new RuntimeException("端口和端口描述的数据不一致，请联系lgj处理");
                boolean is_only_draw_left = graphic_type!=TYPE_GRAPHIC_IED_SWITCH;
                for (int k = 0; k < lgj_ports.size(); k++) {
                    PortBaseBean portBaseBean = new PortBaseBean();
                    portBaseBean.setBeyondTo(graphic_Device);
                    //设置端口属性信息
                    portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);
                    portBaseBean.setDirectionType(k==0?PortDirectionType.Tx:PortDirectionType.Rx);
                    if (portsLocation == PortsLocation.Begin){
                        portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);
                        portBaseBean.setDirectionType(k==0?PortDirectionType.Tx:PortDirectionType.Rx);
                    } else if (portsLocation == PortsLocation.End){
                        if (unit.isODF()){
                            portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);
                            portBaseBean.setDirectionType(k==0?PortDirectionType.RT:PortDirectionType.RT);
                        } else {
                            portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);
                            portBaseBean.setDirectionType(k==0?PortDirectionType.Rx:PortDirectionType.Tx);
                        }
                    } else if (portsLocation == PortsLocation.Middle_Through){//非短接情况
                        portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);//getByPositionPortIndex(k)
                        portBaseBean.setDirectionType(k==0?PortDirectionType.RT:PortDirectionType.RT);
                    } else if (portsLocation == PortsLocation.Middle_ShortCircuited){//短接情况
                        //getByPositionPortIndex(k)
                        //lgj的四个portABCD的位置相对信息为：
                        //  portA  portB
                        //  portC  portD
                        if (k%4==0) portBaseBean.setPosition(PortBaseBean.Position.UP_Left);
                        if (k%4==2) portBaseBean.setPosition(PortBaseBean.Position.DOWN_Left);
                        if (k%4==1) portBaseBean.setPosition(PortBaseBean.Position.UP_Right);
                        if (k%4==3) portBaseBean.setPosition(PortBaseBean.Position.DOWN_Right);
                        portBaseBean.setDirectionType(k==0?PortDirectionType.RT:PortDirectionType.RT);
                    }

                    portBaseBean.setRectF(null);
                    KM_SPCD_PORT port = null;
                    try {
                        port = unit.getSpcdPort(mCtx,dbName).get(k);
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                    portBaseBean.setDetail(getPortA(port));//Done:设置端口描述信息！！"3n.1.12-Rx"
                    portBaseBean.setSpcdPort(port);//设置port
                    //portBaseBean.initRectWithParentDevRect(device_rect);
                    ports.add(portBaseBean);
                }
                portsCanvasBean.setPorts(ports);//设置ports并初始化其rects
                portsCanvasBean.setPortsLocation(portsLocation);
                portsCanvasBean.intiPortsRects(device_rect);

                graphic_Device.setPortsCanvasBean(portsCanvasBean);
                //graphic_Device.setSPCD_UNIT();
                graphic_Cubicle.addDevices(graphic_Device);

            }
            graphic_Cubicles.add(graphic_Cubicle);
        }
    }

    private void initGraphicCubicles_RealData_New(){
        graphic_Cubicles.clear();
        for (int i = 0; i < cubicles.size(); i++) {
            WholeCircuitBean.CubicleWholeBean cubicle = cubicles.get(i);

            CubicleBean_v2 graphic_Cubicle = new CubicleBean_v2();
            RectF cubicle_rect = cubicle_rects.get(i);
            graphic_Cubicle.setRectF(cubicle_rect);
            graphic_Cubicle.setInfo(cubicle.getKm_spcd_cubicle().getInfo());
            //获取到屏柜所属小室
            KM_SPCD_REGION region = regionDao.getById(cubicle.getKm_spcd_cubicle().getRegion_id());
            graphic_Cubicle.setRegionBeyondTo(region.getDescription());

            for (int j = 0; j < cub_device_size[i]; j++) {
                WholeCircuitBean.UnitWholeBean unit = cubicle.getUnitWholeBeans().get(j);

                DeviceBean_v2 graphic_Device = new DeviceBean_v2();
                graphic_Device.setInfo(unit.getKm_spcd_unit().getInfo());

                RectF device_rect = getDeviceRectFInCubicleByDeviceIndex(cubicle_rect,j);
                graphic_Device.setRectF(device_rect);
                //设置所属装置信息
                graphic_Device.setBeyondTo(unit.getKm_spcd_unit());
                if (i==cub_device_size.length-1){
                    graphic_Device.setLastCubicle(true);
                }
                if (j==cub_device_size[i]-1){
                    graphic_Device.setLastUnit(true);
                }

                List<String> lgj_ports = unit.getPortList();

                try {
                    if (lgj_ports.size()!=unit.getSpcdPort(mCtx,dbName).size()){
                        //走到这个地方还有可能是spcd文件没有正确配置，因全回路信息都是通过intcore\cable-core来查找的，
                        // 可能最终通过intcore找到的port在intcore所在的cubicle中找不到指定的port。（p.s.但路径intcore却存在，所以lgj数据中的
                        // km_spcd_intcoreA在此种情况下存在则全回路数据是不正确的）
                        //throw new RuntimeException("所给的全回路数据中 portABCD和portABCD的dsc不为null的数据不对应所致");
                        Log.e(TAG, "initGraphicCubicles_RealData: 所给的物理全回路的数据中,装置：" + unit.getKm_spcd_unit().getInfo()
                                +" 端口错误！");
                        continue;//(continue会导致少绘制一个装置)
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }
                // guoyong 2019/11/1  设置装置内部Ports的数据。(需优化结构)
                PortsCanvasBean portsCanvasBean = new PortsCanvasBean();
                //guoyong 2019/11/1  获取装置内部ports的位置信息。
                PortsLocation portsLocation = PortsLocation.Begin;//PortsLocation.Middle_Through
                if (i==0&& j==0){
                    portsLocation = PortsLocation.Begin;
                } else if (i==cub_device_size.length-1 && j==cub_device_size[i]-1){
                    portsLocation = PortsLocation.End;
                } else {
                    if (lgj_ports.size()==2){
                        portsLocation = PortsLocation.Middle_Through;
                    } else if (lgj_ports.size()==4){
                        portsLocation = PortsLocation.Middle_ShortCircuited;
                    } else {
                        /*throw new RuntimeException("端口数据应该为2或者4");*/
                        portsLocation = PortsLocation.Middle_Through;
                    }
                    //适应-4 特定情况
                    if(unit.getKm_spcd_unit().getDev_class().equals("SWITCH")&&dataForType==TYPE_FOR_PHYSIC_LINK_ALL){
                        portsLocation = PortsLocation.Middle_ShortCircuited;
                    }
                }

                List<PortBaseBean> ports = new ArrayList<>();

                //if (lgj_ports.size()==lgj_ports_Desc.size()) throw new RuntimeException("端口和端口描述的数据不一致，请联系lgj处理");
                boolean is_only_draw_left = graphic_type!=TYPE_GRAPHIC_IED_SWITCH;
                for (int k = 0; k < lgj_ports.size(); k++) {
                    //if (is_only_draw_left && (k%4==1||k%4==3)) return;

                    PortBaseBean portBaseBean = new PortBaseBean();
                    portBaseBean.setBeyondTo(graphic_Device);
                    //设置端口属性信息
                    portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);
                    portBaseBean.setDirectionType(k==0?PortDirectionType.Tx:PortDirectionType.Rx);
                    if (portsLocation == PortsLocation.Begin){
                        portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);
                        portBaseBean.setDirectionType(k==0?PortDirectionType.Tx:PortDirectionType.Rx);
                    } else if (portsLocation == PortsLocation.End){
                        if (unit.isODF()){
                            portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);
                            portBaseBean.setDirectionType(k==0?PortDirectionType.RT:PortDirectionType.RT);
                        } else {
                            portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);
                            portBaseBean.setDirectionType(k==0?PortDirectionType.Rx:PortDirectionType.Tx);
                        }
                    } else if (portsLocation == PortsLocation.Middle_Through){//非短接情况
                        portBaseBean.setPosition(k==0?PortBaseBean.Position.Left:PortBaseBean.Position.Right);//getByPositionPortIndex(k)
                        portBaseBean.setDirectionType(k==0?PortDirectionType.RT:PortDirectionType.RT);
                    } else if (portsLocation == PortsLocation.Middle_ShortCircuited){//短接情况
                        //getByPositionPortIndex(k)
                        //lgj的四个portABCD的位置相对信息为：
                        //  portA  portB
                        //  portC  portD
                        if (k%4==0) portBaseBean.setPosition(PortBaseBean.Position.UP_Left);
                        if (k%4==2) portBaseBean.setPosition(PortBaseBean.Position.DOWN_Left);
                        if (k%4==1) portBaseBean.setPosition(PortBaseBean.Position.UP_Right);
                        if (k%4==3) portBaseBean.setPosition(PortBaseBean.Position.DOWN_Right);
                        portBaseBean.setDirectionType(k==0?PortDirectionType.RT:PortDirectionType.RT);

                        //适应-5 特定情况
                        if(unit.getKm_spcd_unit().getDev_class().equals("SWITCH")&&dataForType==TYPE_FOR_PHYSIC_LINK_ALL){
                            if (k%4==0) portBaseBean.setPosition(PortBaseBean.Position.UP_Left);
                            if (k%4==2) portBaseBean.setPosition(PortBaseBean.Position.DOWN_Left);
                            if (k%4==1) portBaseBean.setPosition(PortBaseBean.Position.UP_Right);
                            if (k%4==3) portBaseBean.setPosition(PortBaseBean.Position.DOWN_Right);
                            portBaseBean.setDirectionType(k==0||k==3?PortDirectionType.Rx:PortDirectionType.Tx);
                        }
                    }

                    portBaseBean.setRectF(null);
                    KM_SPCD_PORT port = null;
                    try {
                        port = unit.getSpcdPort(mCtx,dbName).get(k);
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                    portBaseBean.setDetail(getPortA(port));//Done:设置端口描述信息！！"3n.1.12-Rx"
                    portBaseBean.setSpcdPort(port);//设置port
                    //portBaseBean.initRectWithParentDevRect(device_rect);
                    ports.add(portBaseBean);
                }
                portsCanvasBean.setPorts(ports);//设置ports并初始化其rects
                portsCanvasBean.setPortsLocation(portsLocation);
                portsCanvasBean.intiPortsRects(device_rect);

                graphic_Device.setPortsCanvasBean(portsCanvasBean);
                //graphic_Device.setSPCD_UNIT();
                graphic_Cubicle.addDevices(graphic_Device);

            }
            graphic_Cubicles.add(graphic_Cubicle);
        }
    }

    //TODO 通过SPCD_PORT 来找 portA
    public String getPortA(KM_SPCD_PORT port){
        if (port != null){
            BaseDao<KM_SPCD_BOARD, Integer> boardDao = new BaseDaoImp(mCtx, KM_SPCD_BOARD.class, dbName);
            BaseDao<KM_SPCD_UNIT, Integer> unitDao = new BaseDaoImp(mCtx, KM_SPCD_UNIT.class, dbName);
            KM_SPCD_BOARD board = boardDao.getById(port.getBoard_id());
            KM_SPCD_UNIT unit = unitDao.getById(board.getUnit_id());
            return unit.getName()+"."+board.getSlot()+"."+port.getNo()+"-"+port.getDirection();
        }
        return null;//"3n.1.12-Rx"
    }

    private void initGraphicCables() {
    }

    private void updateAllRects() {
    }

    public PortBaseBean.Position getByPositionPortIndex(int index){
        switch (index){
            case 0:
                return PortBaseBean.Position.UP_Left;
            case 1:
                return PortBaseBean.Position.UP_Right;
            case 2:
                return PortBaseBean.Position.DOWN_Left;
            case 3:
                return PortBaseBean.Position.DOWN_Right;
            default:
                return null;
        }
    }

    /**
     * 获取屏柜的RectF
     * @param index_cubicle
     * @param cub_device_size
     * @return
     */
    public RectF getCubicleRectFByDeviceNum(int index_cubicle, final int[] cub_device_size){
        RectF rectF = null;
        int deviceNum = cub_device_size[index_cubicle];
        float cubicle_height = getCubicleHeigth(deviceNum);
        if (index_cubicle==0){
            rectF = new RectF(CircuitConstants_v2.CUBICLE_PADDING_LEFT_OR_RIGHT,CircuitConstants_v2.CUBICLE_PADDING_TOP,
                    width-CircuitConstants_v2.CUBICLE_PADDING_LEFT_OR_RIGHT,CircuitConstants_v2.CUBICLE_PADDING_TOP+cubicle_height);

            cubicle_rects.add(rectF);
            return rectF;
        }


        RectF preCubicleRectF = getCubicleRectFByDeviceNum(index_cubicle-1,cub_device_size);

        int pre_deviceNum = cub_device_size[index_cubicle-1];
        float newTop = preCubicleRectF.bottom+CircuitConstants_v2.CUBICLEs_SPACING_VERTICAL;
        rectF = new RectF(preCubicleRectF.left,newTop,
                width-CircuitConstants_v2.CUBICLE_PADDING_LEFT_OR_RIGHT,newTop+cubicle_height);

        cubicle_rects.add(rectF);
        return rectF;
    }

    private float getCubicleHeigth(int deviceNum){
        float cubicle_height;
        cubicle_height = CircuitConstants_v2.DEVICE_MARGIN_TOP
                +deviceNum*CircuitConstants_v2.DEVICE_HEIGHT
                +(deviceNum-1)*CircuitConstants_v2.DEVICEs_SPACING_VERTICAL
                +CircuitConstants_v2.DEVICE_MARGIN_BOTTOM;
        if (cubicle_height<CircuitConstants_v2.CUBICLE_MIN_HEIGHT){
            cubicle_height = CircuitConstants_v2.CUBICLE_MIN_HEIGHT;
        }
        return cubicle_height;
    }

    /**
     * 获取屏柜内装置的RectF
     * @param cubicleRectF
     * @param index_device
     * @return
     */
    public RectF getDeviceRectFInCubicleByDeviceIndex(RectF cubicleRectF,int index_device){
        if (index_device<0/* || index_device >1*/) throw new IndexOutOfBoundsException("设备index>=0");
        RectF rectF = null;
        RectF rectF0 = null;
        RectF rectF1 = null;

        rectF0 = new RectF(cubicleRectF.left+CircuitConstants_v2.DEVICE_MARGIN_LEFT_OR_RIGHT,
                cubicleRectF.top + CircuitConstants_v2.DEVICE_MARGIN_TOP,
                cubicleRectF.right-CircuitConstants_v2.DEVICE_MARGIN_LEFT_OR_RIGHT,
                cubicleRectF.top + CircuitConstants_v2.DEVICE_MARGIN_TOP+CircuitConstants_v2.DEVICE_HEIGHT
        );
        rectF1 = new RectF(cubicleRectF.left+CircuitConstants_v2.DEVICE_MARGIN_LEFT_OR_RIGHT,
                rectF0.top+ (CircuitConstants_v2.DEVICE_HEIGHT+CircuitConstants_v2.DEVICEs_SPACING_VERTICAL)*index_device,
                cubicleRectF.right-CircuitConstants_v2.DEVICE_MARGIN_LEFT_OR_RIGHT,
                rectF0.bottom+ (CircuitConstants_v2.DEVICEs_SPACING_VERTICAL+CircuitConstants_v2.DEVICE_HEIGHT)*index_device
        );

        if (index_device == 0){
            rectF = rectF0;
        } else {
            rectF = rectF1;
        }
        return rectF;
    }

    //获取第index个屏柜的Rect
    /*public RectF getCubicleRectFByIndex(int index){
        if (index<0) throw new IndexOutOfBoundsException("index 越界");
        if (index==0){
            return new RectF(CircuitConstants_v2.CUBICULE_PADDING_LEFT, CircuitConstants_v2.CUBICULE_PADDING_TOP,
                    CircuitConstants_v2.CUBICULE_PADDING_LEFT + CircuitConstants_v2.CUBICULE_WIDTH, CircuitConstants_v2.CUBICULE_PADDING_TOP+CircuitConstants.CUBICULE_HEIGHT);
        }
        RectF pre = getCubicleRectFByIndex(index-1);
        RectF rectF = new RectF(pre.right+CircuitConstants.CUBICULE_SPACING_HORIZON,
                pre.top,
                pre.right+CircuitConstants.CUBICULE_SPACING_HORIZON+CircuitConstants.CUBICULE_WIDTH,
                pre.bottom);
        return rectF;
    }*/

    //获取第index_cubicle个屏柜的第index_ied个装置的Rect
    /*public RectF getCubicleIedRectByIndex(int index_cubicle, int index_ied){
        if (index_ied<0) throw new IndexOutOfBoundsException("index_ied 越界");
        RectF cubicleRect = getCubicleRectFByIndex(index_cubicle);
        if (index_ied==0){
            return new RectF(cubicleRect.left+CircuitConstants.DEVICE_PADDING_LEFT_OR_RIGHT,
                    cubicleRect.top+CircuitConstants.CUBICULE_NAME_HEIGHT+CircuitConstants.DEVICE_PADDING_TOP,
                    cubicleRect.right-CircuitConstants.DEVICE_PADDING_LEFT_OR_RIGHT,
                    cubicleRect.top+CircuitConstants.CUBICULE_NAME_HEIGHT+CircuitConstants.DEVICE_PADDING_TOP +CircuitConstants.CUBICULE_DEVICE_HEIGHT
                    );
        }
        RectF pre = getCubicleIedRectByIndex(index_cubicle,index_ied-1);
        RectF rectF = new RectF(pre.left,
                pre.bottom + CircuitConstants.DEVICEs_SPACING_VERTICAL,
                pre.right,
                pre.bottom + CircuitConstants.DEVICEs_SPACING_VERTICAL + CircuitConstants.CUBICULE_DEVICE_HEIGHT);
        return rectF;
    }*/

    List<KM_SPCD_INTCORE> intcores = new ArrayList<>();
    @Deprecated
    private void initOpticalFiber(){
        List<PortBaseBean> the_whole_ports1 = new ArrayList<>();
        for (int i = 0; i < graphic_Cubicles.size(); i++) {
            CubicleBean_v2 cubicleBean_v2 = graphic_Cubicles.get(i);
            for (DeviceBean_v2 deviceBean_v2 : cubicleBean_v2.getDevices()){
                List<PortBaseBean> portBaseBeans = deviceBean_v2.getPortsCanvasBean().getPorts();
                the_whole_ports1.add(portBaseBeans.get(0));
            }
        }

        for (int i = 0; i < the_whole_ports1.size()-1; i++) {
            PortBaseBean port_start = the_whole_ports1.get(i);
            PortBaseBean port_stop = the_whole_ports1.get(i+1);
            LinePort2PortBean line = new LinePort2PortBean();

            line.setDrawArrow(true);
            line.setDesc("测试***********！！");
            line.setStartEnd(port_start.getRectF(),port_stop.getRectF());

            //赋值跳纤的回路图 着色绘制
            List<KM_SPCD_PORT> highlight_ports = data.getHighlightPorts();
            if (highlight_ports==null || highlight_ports.isEmpty()){
                line.setHighlight(false);
            } else {
                if (highlight_ports.get(0).getId()==port_start.getSpcdPort().getId()
                 && highlight_ports.get(1).getId()==port_start.getSpcdPort().getId()){
                    line.setHighlight(true);
                }
            }
            linePort2PortBeanList.add(line);
        }

        //if (graphic_type != TYPE_GRAPHIC_IED_SWITCH) return;
        List<PortBaseBean> the_whole_ports2 = new ArrayList<>();
        for (int i = graphic_Cubicles.size()-1; i >= 0; i--) {
            CubicleBean_v2 cubicleBean_v2 = graphic_Cubicles.get(i);
            for (int j = cubicleBean_v2.getDevices().size()-1; j >= 0; j--) {
                DeviceBean_v2 deviceBean_v2 = cubicleBean_v2.getDevices().get(j);
                List<PortBaseBean> portBaseBeans = deviceBean_v2.getPortsCanvasBean().getPorts();
                the_whole_ports2.add(portBaseBeans.get(1));
            }
        }
        for (int i = 0; i < the_whole_ports2.size()-1; i++) {
            PortBaseBean port_start = the_whole_ports2.get(i);
            PortBaseBean port_stop = the_whole_ports2.get(i+1);
            LinePort2PortBean line = new LinePort2PortBean();

            line.setDrawArrow(true);
            line.setDesc("测试************2！！");
            line.setStartEnd(port_start.getRectF(),port_stop.getRectF());
            linePort2PortBeanList.add(line);
        }
        // TODO: guoyong 2019/11/4 需要获取到相关联的IntCores, Cores; 然后找出对于的连接关系。
        //屏柜内部连接
        //IntCorePort2PortBean linePort2PortBean_in = new IntCorePort2PortBean();
        //屏柜外部连接
        //CorePort2PortBean linePort2PortBean_out = new CorePort2PortBean();
        //linePort2PortBeanList.add(linePort2PortBean_in);
    }
    public static String getCoreDetailByPortA_B(String portA, String portB){
        return "be better!";
    }
    private void initOpticalFiber_RealData(){
        linePort2PortBeanList.clear();
        //由上而下
        List<PortBaseBean> the_whole_ports1 = new ArrayList<>();
        for (int i = 0; i < graphic_Cubicles.size(); i++) {
            CubicleBean_v2 cubicleBean_v2 = graphic_Cubicles.get(i);
            for (DeviceBean_v2 deviceBean_v2 : cubicleBean_v2.getDevices()){
                List<PortBaseBean> portBaseBeans = deviceBean_v2.getPortsCanvasBean().getPorts();
                if (dataForType!=TYPE_FOR_PHYSIC_LINK_ALL){
                    the_whole_ports1.add(portBaseBeans.get(0));
                } else {
                    if (portBaseBeans.size()==4){
                        the_whole_ports1.add(portBaseBeans.get(0));
                        the_whole_ports1.add(portBaseBeans.get(2));
                    } else {
                        the_whole_ports1.add(portBaseBeans.get(0));
                    }
                }
            }
        }

        for (int i = 0; i < the_whole_ports1.size()-1; i++) {
            PortBaseBean port_start = the_whole_ports1.get(i);
            PortBaseBean port_stop = the_whole_ports1.get(i+1);
            LinePort2PortBean line = new LinePort2PortBean();

            //两个port间是否存在连接关系(fix:此处可以不加此判断)
            Object is_linked = null;
            try {
                is_linked = DataProcessTools.hasLinked(mCtx,port_start.getSpcdPort(),port_stop.getSpcdPort());
            } catch (SQLException e) {
                e.printStackTrace();
            }
            assert is_linked!=null;
            if (is_linked==null){
                continue;
            }
            final String fiber_name = getFiberName(is_linked);

            line.setDrawArrow(true);
            line.setDesc(fiber_name);
            line.setStartEnd(port_start.getRectF(),port_stop.getRectF());

            //line中光纤名称位置的确定
            setOpticalFiberNameLocation(true,port_start,port_stop,line);

            //赋值跳纤的回路图 着色绘制
            if (data != null){
                //line.setHighlight(isDrawRed(data.getHighlightPorts(),port_start,port_stop));
                linePort2PortBeanList.add(line);
            }
        }

        //if (graphic_type != TYPE_GRAPHIC_IED_SWITCH) return;
        //由下而上
        List<PortBaseBean> the_whole_ports2 = new ArrayList<>();
        for (int i = graphic_Cubicles.size()-1; i >= 0; i--) {
            CubicleBean_v2 cubicleBean_v2 = graphic_Cubicles.get(i);
            for (int j = cubicleBean_v2.getDevices().size()-1; j >= 0; j--) {
                DeviceBean_v2 deviceBean_v2 = cubicleBean_v2.getDevices().get(j);
                List<PortBaseBean> portBaseBeans = deviceBean_v2.getPortsCanvasBean().getPorts();
                if (portBaseBeans.size() < 2){
                    return;
                }
                if (portBaseBeans.size()==4){
                    the_whole_ports2.add(portBaseBeans.get(3));
                    the_whole_ports2.add(portBaseBeans.get(1));
                } else {
                    the_whole_ports2.add(portBaseBeans.get(1));
                }
            }
        }
        for (int i = 0; i < the_whole_ports2.size()-1; i++) {
            PortBaseBean port_start = the_whole_ports2.get(i);
            PortBaseBean port_stop = the_whole_ports2.get(i+1);
            LinePort2PortBean line = new LinePort2PortBean();

            Object is_linked = null;
            try {
                is_linked = DataProcessTools.hasLinked(mCtx,port_start.getSpcdPort(),port_stop.getSpcdPort());
            } catch (SQLException e) {
                e.printStackTrace();
            }
            if (is_linked == null){
                continue;
            }
            final String fiber_name = getFiberName(is_linked);
            line.setDrawArrow(true);
            line.setDesc(fiber_name);
            line.setStartEnd(port_start.getRectF(),port_stop.getRectF());

            //line中光纤名称位置的确定
            setOpticalFiberNameLocation(false, port_stop, port_start, line);

            //赋值跳纤的回路图 着色绘制
            if (data != null){
                //line.setHighlight(isDrawRed(data.getHighlightPorts(),port_start,port_stop));
                linePort2PortBeanList.add(line);
            }
        }
        // TODO: guoyong 2019/11/4 需要获取到相关联的IntCores, Cores; 然后找出对于的连接关系。
        //屏柜内部连接
        //IntCorePort2PortBean linePort2PortBean_in = new IntCorePort2PortBean();
        //屏柜外部连接
        //CorePort2PortBean linePort2PortBean_out = new CorePort2PortBean();
        //linePort2PortBeanList.add(linePort2PortBean_in);
    }

    /***
     * 设置光纤线段中光纤名称绘制的等分点位置
     * @param isUp2Down 是否自上而下（方向）
     * @param port_start 起点端口
     * @param port_stop 终点端口
     * @param line 光纤对象
     */
    private static void setOpticalFiberNameLocation(boolean isUp2Down, PortBaseBean port_start, PortBaseBean port_stop, LinePort2PortBean line) {
        float location_percent = 0.5f;

        if (port_start.getBeyondTo().getBeyondTo().getCubicle_id() == port_stop.getBeyondTo().getBeyondTo().getCubicle_id()){
            if (port_start.getBeyondTo().getBeyondTo().getDev_class().equals(Constants.UnitClass.ODF.getText())
                    && port_stop.getBeyondTo().getBeyondTo().getDev_class().equals(Constants.UnitClass.ODF.getText())
            ){
                //前一个装置为odf，且后一个装置为odf（无遮挡）
            } else if (!port_start.getBeyondTo().getBeyondTo().getDev_class().equals(Constants.UnitClass.ODF.getText())
                    && port_stop.getBeyondTo().getBeyondTo().getDev_class().equals(Constants.UnitClass.ODF.getText())){
                //前一个装置不为odf，且后一个装置为odf（无遮挡）
                line.setUseLocation_per(true);
                location_percent = 1/3f;
            } else if (port_start.getBeyondTo().getBeyondTo().getDev_class().equals(Constants.UnitClass.ODF.getText())
                    && !port_stop.getBeyondTo().getBeyondTo().getDev_class().equals(Constants.UnitClass.ODF.getText())){
                //前一个装置为odf，且后一个装置不为odf（无遮挡）
                line.setUseLocation_per(true);
                location_percent = 3/4f;
            } else {
                //前一个装置非odf，且后一个装置非odf（无遮挡）
                line.setUseLocation_per(true);
                location_percent = 1/2f;
            }
        } else {//非同一屏柜
            if (port_start.getBeyondTo().getBeyondTo().getDev_class().equals(Constants.UnitClass.ODF.getText())
                    && port_stop.getBeyondTo().getBeyondTo().getDev_class().equals(Constants.UnitClass.ODF.getText())
            ){
                //前一个装置为odf，且后一个装置为odf（无遮挡）
                line.setUseLocation_per(true);
                location_percent = 7/16f;
            } else if (!port_start.getBeyondTo().getBeyondTo().getDev_class().equals(Constants.UnitClass.ODF.getText())
                    && port_stop.getBeyondTo().getBeyondTo().getDev_class().equals(Constants.UnitClass.ODF.getText())){
                //前一个装置不为odf，且后一个装置为odf（无遮挡）
                line.setUseLocation_per(true);
                location_percent = 1/4f;
            } else if (port_start.getBeyondTo().getBeyondTo().getDev_class().equals(Constants.UnitClass.ODF.getText())
                    && !port_stop.getBeyondTo().getBeyondTo().getDev_class().equals(Constants.UnitClass.ODF.getText())){
                //前一个装置为odf，且后一个装置不为odf（无遮挡）
                line.setUseLocation_per(true);
                location_percent = 1/2f;
            } else {
                //前一个装置非odf，且后一个装置非odf（无遮挡）
                line.setUseLocation_per(true);
                location_percent = 0.36f;
            }
        }
        line.setLocation_per(isUp2Down?location_percent:(1-location_percent));
    }

    public boolean isDrawRed(List<KM_SPCD_PORT> highlight_ports,
                             PortBaseBean port_start, PortBaseBean port_stop){
        if (highlight_ports==null || highlight_ports.size()<2||data.getType()==1){
            return false;
        } else {
            if (highlight_ports.get(0).getId().intValue() == port_start.getSpcdPort().getId().intValue()
                    && highlight_ports.get(1).getId().intValue() == port_stop.getSpcdPort().getId().intValue() ||
                    (highlight_ports.get(1).getId().intValue() == port_start.getSpcdPort().getId().intValue()
                            && highlight_ports.get(0).getId().intValue() == port_stop.getSpcdPort().getId().intValue())) {
                return true;
            }
        }
        return false;
    }

    //获取intcore|core的name
    //@see #Link(com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.DataProcessTools.getFiberName)
    public String getFiberName(Object obj){
        if (obj == null) return "N/A";
        if (obj instanceof KM_SPCD_CORE){
            KM_SPCD_CORE core = (KM_SPCD_CORE) obj;
            BaseDao<KM_SPCD_CABLE, Integer> cableDao = new BaseDaoImp(mCtx, KM_SPCD_CABLE.class, dbName);
            KM_SPCD_CABLE cable = cableDao.getById(core.getCable_id());
            return String.format(Locale.CHINA,"%s(%d-%s)",cable.getName(),cable.getCores_num(),core.getNo());
        } else if (obj instanceof KM_SPCD_INTCORE){
            return ((KM_SPCD_INTCORE) obj).getName();
        }
        return "N/A";
    }


    public static void clear(){
        if (sInstance!=null){
            sInstance = null;
        }
    }
}
