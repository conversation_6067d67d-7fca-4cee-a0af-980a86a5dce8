package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2;

public class CircuitConstants_v2 {

    public static final float CUBICLE_PADDING_LEFT_OR_RIGHT = 20;
    public static final float CUBICLE_PADDING_TOP = 30;

    public static final float CUBICLE_MIN_HEIGHT = 260;
    public static final float CUBICLE_NAME_RECT_WIDTH = 0;//设置占据屏宽的3/8
    public static final float CUBICLE_NAME_RECT_HEIGHT = 100;

    public static final float CUBICLEs_SPACING_VERTICAL = 80;//屏柜间垂直间隔


    public static final float DEVICE_MARGIN_LEFT_OR_RIGHT = 10;
    public static final float DEVICE_MARGIN_TOP = CUBICLE_NAME_RECT_HEIGHT;
    public static final float DEVICE_HEIGHT = 200;
    public static final float DEVICE_MARGIN_BOTTOM = 20;
    public static final float DEVICEs_SPACING_VERTICAL = 80;

    public static final float PORT_WIDTH = 70;
    public static final float PORT_HEIGHT = 45;


}
