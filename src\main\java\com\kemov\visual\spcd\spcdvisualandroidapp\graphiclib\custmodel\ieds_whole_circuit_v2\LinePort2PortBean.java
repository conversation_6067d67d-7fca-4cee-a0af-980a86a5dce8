package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.PointF;
import android.graphics.RectF;
import android.text.TextPaint;

/**
 *功能：端口对端口的连线
 *Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/10/31 8:49
 */
public class LinePort2PortBean implements CanvasDraw{

    public static final int triangleHeight = 15;
    public static final int triangleWeight = triangleHeight*2/3;
    float startX;
    float startY;
    float endX;
    float endY;
    String desc = "";
    boolean drawArrow = true;
    int line_color = 0xFFFF0000;
    boolean isHighlight = false;
    float location_per = 0.5f;//文字绘制的位置等分点
    boolean useLocation_per = false;


    public float getLocation_per() {
        return location_per;
    }

    public void setLocation_per(float location_per) {
        this.location_per = location_per;
    }

    public LinePort2PortBean() {
    }


    public void setLine_color(int line_color) {
        this.line_color = line_color;
    }

    public void setHighlight(boolean highlight) {
        isHighlight = highlight;
    }

    public void setStartEnd(RectF startRectF, RectF endRectF) {
        if (startRectF.centerY()<endRectF.centerY()){
            this.startX = startRectF.centerX();
            this.startY = startRectF.bottom;

            this.endX = endRectF.centerX();
            this.endY = endRectF.top;
        } else {
            this.startX = startRectF.centerX();
            this.startY = startRectF.top;

            this.endX = endRectF.centerX();
            this.endY = endRectF.bottom;
        }
    }


    public void setDrawArrow(boolean drawArrow) {
        this.drawArrow = drawArrow;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public void draw(Canvas canvas, Paint mPaint, TextPaint txtPaint) {
        Paint.Style preStyle = mPaint.getStyle();
        float preStrokeWidth = mPaint.getStrokeWidth();
        Paint.Align perAlign = txtPaint.getTextAlign();

        mPaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mPaint.setStrokeWidth(4);

        int pre_color = mPaint.getColor();
        if(isHighlight){
            mPaint.setColor(line_color);
        }

        canvas.drawLine(startX,startY,endX,endY,mPaint);

        if (drawArrow){//绘制箭头+光纤描述信息(有方向则绘制文字描述，odf短接的情况不绘制文字和方向)
            boolean isUp = startY>endY;
            canvas.drawPath(getTrianglePath(endX,endY,isUp,triangleWeight,triangleHeight),mPaint);
            mPaint.setStyle(preStyle);

            float textReferenceX = (startX+endX)/2,textReferenceY = (startY+endY)/2;//描述信息参照点
            //设置线段描述信息的绘制位置。
            if (useLocation_per){
                textReferenceY = startY+(endY-startY)*location_per;
            }

            final float offsetX = -5f;

            if (isUp){
                textReferenceX -= offsetX;
                txtPaint.setTextAlign(Paint.Align.LEFT);
            } else {
                textReferenceX += offsetX;
                txtPaint.setTextAlign(Paint.Align.RIGHT);
            }
            canvas.drawText(desc,textReferenceX,textReferenceY,txtPaint);

        }

        txtPaint.setTextAlign(perAlign);
        mPaint.setStrokeWidth(preStrokeWidth);
        mPaint.setStyle(preStyle);
        mPaint.setColor(pre_color);
    }

    /**
     *
     * @param vertexX
     * @param vertexY
     * @param isUp 朝向，true表示箭头朝上
     * @param h
     * @param w
     * @return
     */
    private Path getTrianglePath(float vertexX,float vertexY,boolean isUp, float w, float h){
        Path path = new Path();
        path.moveTo(vertexX,vertexY);
        if (isUp){
            path.lineTo(vertexX+w/2f,vertexY+h);
            path.lineTo(vertexX-w/2f,vertexY+h);
        } else {
            path.lineTo(vertexX-w/2f,vertexY-h);
            path.lineTo(vertexX+w/2f,vertexY-h);
        }
        path.close();

        return path;
    }

    public void setUseLocation_per(boolean b) {
        this.useLocation_per = b;
    }
}
