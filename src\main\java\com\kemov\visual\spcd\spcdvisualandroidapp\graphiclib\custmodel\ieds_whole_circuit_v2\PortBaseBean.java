package com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.text.TextPaint;

import com.kemov.visual.spcd.spcdvisualandroidapp.database.beans.KM_SPCD_PORT;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.utils.DrawUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Constants;

import java.util.Map;

/**
 *功能：单个端口的模型，对应spcd中的一个port.
 *Created by <PERSON><PERSON><PERSON><PERSON>-<PERSON> on 2019/10/29 14:38
 */
public class PortBaseBean {
    KM_SPCD_PORT spcdPort;
    RectF rectF;
    PortDirectionType directionType;
    //(端口的简要描述信息，例：5-3（ied），A11(odf)等)
    String info ="unknown";
    //(端口的详细描述信息，例：3n.1.15-Tx（ied），DF.1.7-RT(odf)等)
    String detail="unknown";

    //端口所属屏柜及装置的连接关系信息
    DeviceBean_v2 beyondTo;

    Position position;

    public RectF getRectF() {
        return rectF;
    }

    public void setRectF(RectF rectF) {
        this.rectF = rectF;
    }

    public PortDirectionType getDirectionType() {
        return directionType;
    }

    public void setDirectionType(PortDirectionType directionType) {
        this.directionType = directionType;
    }

    public String getDetail() {
        return detail;
    }

    /**
     * 注：可能还需要赋值端口的屏柜-装置
     * @param detail 端口的详细信息，例如：3n.1.1-Rx
     */
    public void setDetail(String detail) {
        if (detail==null) return;
        this.detail = detail;
        //1-40n.1.4-RT
        detail = detail.substring(0,detail.length()-3);
        String[] splits = detail.split("\\.");
        this.setInfo(splits[1]+"-"+splits[2]);

        // TODO: guoyong 2019/11/4 如果是ODF,此处描述信息则需要修改
    }

    public DeviceBean_v2 getBeyondTo() {
        return beyondTo;
    }

    public void setBeyondTo(DeviceBean_v2 beyondTo) {
        this.beyondTo = beyondTo;
    }

    public KM_SPCD_PORT getSpcdPort() {
        return spcdPort;
    }

    public void setSpcdPort(KM_SPCD_PORT spcdPort) {
        this.spcdPort = spcdPort;
    }

    public String getInfo() {
        return info;
    }

    private void setInfo(String info) {
        this.info = info;
    }

    public Position getPosition() {
        return position;
    }

    public void setPosition(Position position) {
        this.position = position;
    }

    //<IntCore type="TX" portA="ODF.1.1-RT" name="TX1" portB="2n.1.3-Rx"/>
    //5-3:board.slot-port.no（应该没用，只有portA的信息不能唯一定位到一个Port）
    public boolean isEqualToPort(String portA_B) {
        return detail.equals(portA_B);
    }

    public void initRectWithParentDevRect(RectF device_rect,PortsLocation device_portsLocation) {
        if (directionType == null || position == null ||device_portsLocation == null){
            throw new NullPointerException("directionType， position and device_portsLocation can't bu null!");
        }

        float port_rect_left=0;
        float port_rect_top=0;
        float port_rect_right=0;
        float port_rect_bottom=0;
        if (device_portsLocation == PortsLocation.Begin && position == Position.Left){//左下
            port_rect_right = device_rect.centerX();
            port_rect_bottom = device_rect.bottom;
            port_rect_left = port_rect_right-CircuitConstants_v2.PORT_WIDTH;
            port_rect_top = port_rect_bottom-CircuitConstants_v2.PORT_HEIGHT;
        }
        if (device_portsLocation == PortsLocation.Begin && position == Position.Right){//右下
            port_rect_left = device_rect.centerX();
            port_rect_bottom = device_rect.bottom;
            port_rect_right = port_rect_left+CircuitConstants_v2.PORT_WIDTH;
            port_rect_top = port_rect_bottom-CircuitConstants_v2.PORT_HEIGHT;
        }
        if (device_portsLocation == PortsLocation.End && position == Position.Left){//左上
            port_rect_right = device_rect.centerX();
            port_rect_top = device_rect.top;
            port_rect_left = port_rect_right-CircuitConstants_v2.PORT_WIDTH;
            port_rect_bottom = port_rect_top+CircuitConstants_v2.PORT_HEIGHT;
        }
        if (device_portsLocation == PortsLocation.End && position == Position.Right){//右上
            port_rect_left = device_rect.centerX();
            port_rect_top = device_rect.top;
            port_rect_right = port_rect_left+CircuitConstants_v2.PORT_WIDTH;
            port_rect_bottom = port_rect_top+CircuitConstants_v2.PORT_HEIGHT;
        }
        if (device_portsLocation == PortsLocation.Middle_Through && position == Position.Left){//左中
            port_rect_right = device_rect.centerX();
            port_rect_top = device_rect.top+device_rect.height()/2-CircuitConstants_v2.PORT_WIDTH/2;
            port_rect_left = port_rect_right-CircuitConstants_v2.PORT_WIDTH;
            port_rect_bottom = port_rect_top+CircuitConstants_v2.PORT_HEIGHT;
        }
        if (device_portsLocation == PortsLocation.Middle_Through && position == Position.Right){//右中
            port_rect_left = device_rect.centerX();
            port_rect_top = device_rect.top+device_rect.height()/2-CircuitConstants_v2.PORT_WIDTH/2;
            port_rect_right = port_rect_left+CircuitConstants_v2.PORT_WIDTH;
            port_rect_bottom = port_rect_top+CircuitConstants_v2.PORT_HEIGHT;
        }

        // TODO: guoyong 2019/11/1  有4个port
        if (device_portsLocation == PortsLocation.Middle_ShortCircuited && position == Position.UP_Left){//左上
            port_rect_right = device_rect.centerX();
            port_rect_top = device_rect.top;
            port_rect_left = port_rect_right-CircuitConstants_v2.PORT_WIDTH;
            port_rect_bottom = port_rect_top+CircuitConstants_v2.PORT_HEIGHT;
        }
        if (device_portsLocation == PortsLocation.Middle_ShortCircuited && position == Position.UP_Right){//右上
            port_rect_left = device_rect.centerX();
            port_rect_top = device_rect.top;
            port_rect_right = port_rect_left+CircuitConstants_v2.PORT_WIDTH;
            port_rect_bottom = port_rect_top+CircuitConstants_v2.PORT_HEIGHT;
        }
        if (device_portsLocation == PortsLocation.Middle_ShortCircuited && position == Position.DOWN_Left){//左下
            port_rect_right = device_rect.centerX();
            port_rect_bottom = device_rect.bottom;
            port_rect_left = port_rect_right-CircuitConstants_v2.PORT_WIDTH;
            port_rect_top = port_rect_bottom-CircuitConstants_v2.PORT_HEIGHT;
        }
        if (device_portsLocation == PortsLocation.Middle_ShortCircuited && position == Position.DOWN_Right){//右下
            port_rect_left = device_rect.centerX();
            port_rect_bottom = device_rect.bottom;
            port_rect_right = port_rect_left+CircuitConstants_v2.PORT_WIDTH;
            port_rect_top = port_rect_bottom-CircuitConstants_v2.PORT_HEIGHT;
        }
        this.rectF = new RectF(port_rect_left,port_rect_top,port_rect_right,port_rect_bottom);
    }

    public void draw(Canvas canvas, Paint mPaint, TextPaint txtPaint){
        int pre_color = mPaint.getColor();
        Paint.Style pre_Style = mPaint.getStyle();
        canvas.drawRect(rectF,mPaint);
        mPaint.setColor(0xff000000);
        mPaint.setStyle(Paint.Style.STROKE);
        canvas.drawRect(rectF,mPaint);
        mPaint.setColor(pre_color);
        mPaint.setStyle(pre_Style);
        //DrawUtils.textCenterDefault(directionType.getText(),txtPaint,canvas, rectF);
        DrawUtils.textCenterDefault(spcdPort.getDirection(),txtPaint,canvas, rectF);

        Paint.Align align= txtPaint.getTextAlign();
        if (this.getPosition() == Position.Left){
            txtPaint.setTextAlign(Paint.Align.RIGHT);
            canvas.drawText(info,rectF.left-4,rectF.centerY()+3,txtPaint);
        } else if (this.getPosition() == Position.Right){
            txtPaint.setTextAlign(Paint.Align.LEFT);
            canvas.drawText(info,rectF.right+4,rectF.centerY()+3,txtPaint);
        } else if (this.getPosition() == Position.UP_Left){
            txtPaint.setTextAlign(Paint.Align.RIGHT);
            canvas.drawText(info,rectF.left-4,rectF.centerY()+3,txtPaint);
        } else if (this.getPosition() == Position.DOWN_Left){
            txtPaint.setTextAlign(Paint.Align.RIGHT);
            canvas.drawText(info,rectF.left-4,rectF.centerY()+3,txtPaint);
        } else if (this.getPosition() == Position.UP_Right){
            txtPaint.setTextAlign(Paint.Align.LEFT);
            canvas.drawText(info,rectF.right+4,rectF.centerY()+3,txtPaint);
        } else if (this.getPosition() == Position.DOWN_Right){
            txtPaint.setTextAlign(Paint.Align.LEFT);
            canvas.drawText(info,rectF.right+4,rectF.centerY()+3,txtPaint);
        }

        txtPaint.setTextAlign(align);
    }

    public boolean isOnTouch(float x, float y, float totalOffX, float totalOffY, float mSclTot){
        x -= totalOffX;
        y -= totalOffY;
        if(x>= rectF.left*mSclTot && x<= rectF.right*mSclTot
                && y>= rectF.top*mSclTot && y<= rectF.bottom*mSclTot)
            return true;
        else {
            return false;
        }
    }

    public enum Position {
        Left,
        Right,
        UP_Left,
        UP_Right,
        DOWN_Left,
        DOWN_Right
    }
}
