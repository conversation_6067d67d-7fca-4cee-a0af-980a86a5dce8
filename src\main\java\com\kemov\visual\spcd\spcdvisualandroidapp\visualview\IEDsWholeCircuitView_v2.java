package com.kemov.visual.spcd.spcdvisualandroidapp.visualview;


import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.os.SystemClock;
import android.support.annotation.Nullable;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Toast;

import com.baseres.base.PubApp;
import com.kemov.parsescl.KIEDModel;
import com.kemov.sclaata.common.app.PubUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.R;
import com.kemov.visual.spcd.spcdvisualandroidapp.activity.IEDVisual.DataProcessTools;
import com.kemov.visual.spcd.spcdvisualandroidapp.datacachepool.IEDsWholeCircuitDataPool_v2;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.base.Port;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.CircuitConstants_v2;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.CubicleBean_v2;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.DeviceBean_v2;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.LinePort2PortBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.custmodel.ieds_whole_circuit_v2.PortBaseBean;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.utils.DrawUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.graphiclib.utils.ResourceUtils;
import com.kemov.visual.spcd.spcdvisualandroidapp.utils.Constants;
import com.share.mycustomviewlib.bean.VirPortsLinksDlgStyleActivity;

import org.apache.http.message.BasicNameValuePair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *功能：新版设计的全回路图。
 *Created by RuanJian-GuoYong on 2019/10/29 9:53
 */
public class IEDsWholeCircuitView_v2 extends View {
    private static final String TAG = "IEDsWholeCircuitViewNew";

    Context mCtx;
    IEDsWholeCircuitDataPool_v2 dataPool = null;
    List<CubicleBean_v2> cubicleBeans = new ArrayList<>();

    boolean hasProblem = false;
    String problem = "无";
    RectF problemRect = null;

    private float eventX, eventY;

    Paint mPaint = null;
    TextPaint txtPaint = null;
    TextPaint leftTxtPaint = null;
    TextPaint rightTxtPaint = null;

    float mWidth,mHeight;

    public IEDsWholeCircuitView_v2(Context context) {
        this(context,null);
    }

    public IEDsWholeCircuitView_v2(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }

    public IEDsWholeCircuitView_v2(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr,0);
    }

    public IEDsWholeCircuitView_v2(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        this.mCtx = context;
        init();
    }

    private void init() {
        dataPool = IEDsWholeCircuitDataPool_v2.getInstance(mCtx, PubApp.getDbName());//测试
        if (dataPool.cubicles!=null && dataPool.cubicles.size()!=0){
            dataPool.cubicles = null;
        }

        txtPaint = new TextPaint();
        txtPaint.setAntiAlias(true);
        //txtPaint.setStrokeWidth(DisplayUtil.dip2sp(mCtx,2));
        txtPaint.setTextAlign(Paint.Align.CENTER);
        txtPaint.setTextSize(18);

        leftTxtPaint = new TextPaint(txtPaint);
        leftTxtPaint.setTextAlign(Paint.Align.LEFT);
        rightTxtPaint = new TextPaint(txtPaint);
        rightTxtPaint.setTextAlign(Paint.Align.RIGHT);

        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        //mPaint.setColor(0x7aff0000);
        mPaint.setColor(0xff000000);//0x7a008080 IED_BG_COLOR
        mPaint.setStrokeWidth(3);
        mPaint.setStyle(Paint.Style.STROKE);
    }

    public void setPortId(String dbName,int portId,int type){
        long t0 = System.currentTimeMillis();
        dataPool.setPortId(mCtx, dbName, portId,type);
        long t1 = System.currentTimeMillis();
        Log.d(TAG, String.format("It takes time : <%s><耗时%dms>","装置端口的物理回路图数据处理",t1-t0));
        this.requestLayout();
    }

    //TODO:从多条路径中选择一条后，再初始化数据
    public void setPhysicLinkData(Object physicLinkData){
        long t0 = System.currentTimeMillis();
        dataPool.setPhysicLinkData(physicLinkData);
        long t1 = System.currentTimeMillis();
        Log.d(TAG, String.format("It takes time : <%s><耗时%dms>","装置端口的物理全回路图数据处理",t1-t0));
        this.requestLayout();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        long t0 = System.currentTimeMillis();
        if (dataPool.cubicles == null)return;

        dataPool.initGraphic(mWidth,mHeight);
        cubicleBeans = dataPool.graphic_Cubicles;
        long t1 = System.currentTimeMillis();
        Log.d(TAG, String.format("It takes time : <%s><耗时%dms>","装置端口的物理回路图onLayout",t1-t0));
    }

    private boolean canDrag = true;
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        long t0 = System.currentTimeMillis();
        if (dataPool.cubicles == null){
            canvas.drawText("指定端口无物理回路，请检查spcd文件配置！",mWidth/2,mHeight/2,txtPaint);
            canDrag = false;
            return;
        }
        //canvas.translate(zoomCenterX,zoomCenterX);
        canvas.scale(mScale, mScale);
        //canvas.translate(-zoomCenterX,-zoomCenterY);
        for (CubicleBean_v2 cubicle: cubicleBeans) {
            cubicle.draw(canvas,mPaint,txtPaint);
        }

        for (LinePort2PortBean line : dataPool.linePort2PortBeanList){
            line.draw(canvas,mPaint,txtPaint);
        }

        //错误预警！
        if(hasProblem){
            Bitmap bitmap= ResourceUtils.getBitmapFromDrawable(
                    PubApp.getContext(), R.mipmap.indicator_input_error, 0,0);
            //获取到屏柜的矩形框
            RectF firstCubicleRect = cubicleBeans.get(0).getRectF();
            problemRect = new RectF(firstCubicleRect.right-bitmap.getWidth(),firstCubicleRect.top,
                    firstCubicleRect.right,firstCubicleRect.top+bitmap.getHeight());

            Rect src = new Rect(0,0, bitmap.getWidth(),bitmap.getHeight());
            canvas.drawBitmap(bitmap,src,problemRect,mPaint);
        }
        long t1 = System.currentTimeMillis();
        Log.d(TAG, String.format("It takes time : <%s><耗时%dms>","装置端口的物理回路图onDraw",t1-t0));
    }

    private void drawPort(Canvas canvas, Port port){
        Log.d(TAG, "drawPort: "+port);
        DrawUtils.drawPointWithFilled(canvas,port.point,mPaint);
        canvas.drawText(port.desc, port.point.x + 15, port.point.y,txtPaint);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        mWidth = w;
        mHeight = h;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        PointF curPointF = new PointF(event.getX(),event.getY());

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                /*eventX = (int) event.getX()-mWidth/2;
                eventY = (int) event.getY()-mHeight/2;*/
                eventX = (int) event.getX();
                eventY = (int) event.getY();
                Log.e("out for", "eventX,eventY="+eventX+" , "+eventY);

                //当点击到插件上，双击弹出对话框
                doubleClickOnPlugSvg();

                break;

            default:
                break;
        }
        if (canDrag)
        DargAndZoom(event);
        invalidate();
        return true;
    }


    //Dragging
    private static final int NONE = 0;
    private static final int DRAG = 1;
    private static final int ZOOM = 2;
    private int mode = NONE;

    private PointF startPoint = new PointF();
    private PointF middlePoint = new PointF();

    private float oriDis = 1f;//初始的俩指触点间距
    private float mScale = 1.0f;
    private float mPreScale = 1.0f;
    private float mMinScale = 0.2f;
    private float mMaxScale = 3.0f;
    //float zoomCenterX=0.0f;
    //float zoomCenterY=0.0f;
    private void DargAndZoom(MotionEvent event){
        //middlePoint = null;
        switch (event.getAction()&event.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
                startPoint.set(event.getX(), event.getY());
                mode = DRAG;
                break;
            case MotionEvent.ACTION_POINTER_DOWN:
                oriDis = DrawUtils.distance(event);
                if (oriDis > 10f) {
                    middlePoint = DrawUtils.mid(event,null);
                    //zoomCenterX=(event.getX(0)+event.getX(1))/2;
                    //zoomCenterY=(event.getY(0)+event.getY(1))/2;
                    mode = ZOOM;
                }

                break;
            case MotionEvent.ACTION_POINTER_UP:
                mPreScale = mScale;
                //if (event.getPointerCount()<2) mode = DRAG;
                break;

            case MotionEvent.ACTION_MOVE:

                if (mode == DRAG) {
                    onDrag(event);
                } else if((mode == ZOOM)){
                    onZoom(event);
                }
                if (mode == DRAG) {
                    startPoint.set(event.getX(), event.getY());
                }
                getParent().requestDisallowInterceptTouchEvent(true);
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                mode = NONE;
                break;
            default:
                break;
        }
    }

    private float totalOffX = 0;
    private float totalOffY = 0;
    float offX = 0;
    float offY = 0;
    private void onDrag(MotionEvent event) {
        //边界处理：(以中心IED的中心不超过View边界为参考)
        offX = event.getX() - startPoint.x;
        offY = event.getY() - startPoint.y;

        scrollBy(-(int)offX, -(int)offY);
        totalOffX += offX;
        totalOffY += offY;
    }

    /**
     * 把View上的点的坐标转化为Canvas上的坐标点 for拾取
     * @return
     */
    private void onZoom(MotionEvent event) {
        float newDist = DrawUtils.distance(event);
        if (newDist > 10f) {
            float scale = newDist/oriDis;

            mScale = scale * mPreScale;
            if (mScale > mMaxScale) {
                mScale = mMaxScale;
            }
            if (mScale < mMinScale) {
                mScale = mMinScale;
            }
        }
    }

    //横向图形显示完整
    public void RequestLayout_FillWidth(){
        scrollTo(0,0);
        mScale = 1;
        mPreScale = mScale;
        this.totalOffX = 0;
        this.totalOffY = 0;
        invalidate();
    }
    //所有图形显示全局
    public void RequestLayout_FillWidthHeight(){
        scrollTo(0,0);
        if (cubicleBeans == null || cubicleBeans.size() == 0){
            return;
        }
        if (cubicleBeans.get(cubicleBeans.size()-1).getRectF() == null){
            return;
        }
        float h =  cubicleBeans.get(cubicleBeans.size()-1).getRectF().bottom;
        float mScale_h = mHeight/(h+ CircuitConstants_v2.CUBICLE_PADDING_TOP);
        if (mScale_h<1){
            mScale = mScale_h;
        } else {
            RequestLayout_FillWidth();
            return;
        }
        mPreScale = mScale;
        this.totalOffX = 0;
        this.totalOffY = 0;
        invalidate();
    }


    //存储时间的数组
    long[] mHitsPlugSvg = new long[2];
    private void doubleClickOnPlugSvg() {
        System.arraycopy(mHitsPlugSvg, 1, mHitsPlugSvg, 0, mHitsPlugSvg.length - 1);
        mHitsPlugSvg[mHitsPlugSvg.length - 1] = SystemClock.uptimeMillis();
        //双击事件的时间间隔500ms
        if (mHitsPlugSvg[0] >= (SystemClock.uptimeMillis() - Constants.CONSTANT_DOUBLE_CLICK_INTERVAL_MS)) {
            Log.i(TAG, "doubleClickOnCtrlBlock: point=("+eventX+","+eventY+")");
            for (CubicleBean_v2 cubicle: cubicleBeans) {
                for (DeviceBean_v2 dev : cubicle.getDevices()) {
                    for (PortBaseBean port:dev.getPortsCanvasBean().getPorts()) {
                        if (port.isOnTouch(eventX, eventY, totalOffX, totalOffY, mScale)){
                            //Toast.makeText(mCtx, "已点击，弹出AppIDs！！！", Toast.LENGTH_SHORT).show();
                            if (dataPool.startUnit!=null && dataPool.endUnit!=null){
                                if (dataPool.startUnit.getDev_class().equals("IED")&&dataPool.endUnit.getDev_class().equals("IED")){
                                    String startIedName = dataPool.startUnit.getIed_name();
                                    String endIedName = dataPool.endUnit.getIed_name();
                                    index_CB = 0;

                                    showDialogCAppID(startIedName,endIedName);
                                } else {
                                    Toast.makeText(mCtx, "起终点装置均为IED时才可以查看虚连接信息！", Toast.LENGTH_SHORT).show();
                                }
                            } else {
                                //不是从ied的背板图点击进入的不看虚连接信息！
                            }
                        }
                    }

                }
            }
            if (hasProblem && isProblemOnTouch(problemRect,eventX, eventY, totalOffX, totalOffY, mScale)){
                Toast.makeText(mCtx, "已点击，弹出错误预警提示:"+problem+"！！！", Toast.LENGTH_SHORT).show();
            }
        }
    }

    int index_CB =0;
    private void showDialogCAppID(final String leftIEDName,final String rightIEDName){
        Map<String,Object> map = DataProcessTools.getThings(leftIEDName,rightIEDName);
        if (map == null) return;

        final String[] ctrlBlocks_With2IEDs = (String[]) map.get("ctrlBlocks_With2IEDs");
        final String[] appIds = (String[]) map.get("appIds");
        final int[] types = (int[]) map.get("types");

        if (appIds.length==1){
            //GotoVirPortsLinksActivity(leftIEDName, rightIEDName, appId[0], type[index_CB]);
            GotoVirPortsLinksDialog(leftIEDName, rightIEDName, appIds[0], types[index_CB]);
            return;
        } else if (appIds.length==0){
            Toast.makeText(mCtx, "此两IED间没有AppID", Toast.LENGTH_SHORT).show();
            return;
        }

        // 创建对话框构建器
        AlertDialog.Builder builder = new AlertDialog.Builder(this.mCtx);
        // 设置参数
        builder.setIcon(R.drawable.ic_launcher)
                .setTitle("请选择对应的控制块的AppId")
                .setSingleChoiceItems(ctrlBlocks_With2IEDs, 0, new android.app.AlertDialog.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        index_CB = which;
                    }
                })
                .setPositiveButton("确定", new DialogInterface.OnClickListener() {

                    @Override
                    public void onClick(DialogInterface arg0, int arg1) {
                        //GotoVirPortsLinksActivity(leftIEDName, rightIEDName, appId[index_CB], type[index_CB]);//相当于是c2e
                        GotoVirPortsLinksDialog(leftIEDName, rightIEDName, appIds[index_CB], types[index_CB]);//相当于是c2e
                    }
                })
                .setNegativeButton("取消", new DialogInterface.OnClickListener(){
                    @Override
                    public void onClick(DialogInterface arg0, int arg1) {
                    }
                });
        builder.create().show();
    }
    private void GotoVirPortsLinksDialog(String cIEDName, String vIEDName, String sAppId, int type) {
        BasicNameValuePair[] keyValues = new BasicNameValuePair[]{
                new BasicNameValuePair("cIEDName", cIEDName),
                new BasicNameValuePair("vIEDName", vIEDName),
                new BasicNameValuePair("sAPPID", sAppId),
                new BasicNameValuePair("type", type+"")
        };
        KIEDModel kiedModel = PubUtils.getPubUtils().GetKIEDModel(cIEDName);
        if (kiedModel == null) {
            Toast.makeText(mCtx, "未找到该控制块"+sAppId, Toast.LENGTH_SHORT).show();
            return;
        }
        start_Activity((Activity)mCtx, VirPortsLinksDlgStyleActivity.class,true,keyValues);
    }

    public static void start_Activity(Activity activity, Class<?> cls,
                                      boolean isDlgStyle,
                                      BasicNameValuePair... name) {
        Intent intent = new Intent();
        intent.setClass(activity, cls);
        if (name != null)
            for (int i = 0; i < name.length; i++) {
                intent.putExtra(name[i].getName(), name[i].getValue());
            }
        activity.startActivity(intent);
        if (isDlgStyle){
            activity.overridePendingTransition(com.kemov.sclaata.R.anim.dialog_enter,
                    com.kemov.sclaata.R.anim.dialog_exit);
        } else {
            activity.overridePendingTransition(com.kemov.sclaata.R.anim.push_left_in,
                    com.kemov.sclaata.R.anim.push_left_out);
        }

    }
    private String getAppid(String strAppid){
        String appid=strAppid;
        int idx=appid.indexOf("0x");
        if(idx>=0)
            appid=appid.substring(idx+"0x".length());
        idx=appid.indexOf("0X");
        if(idx>=0)
            appid=appid.substring(idx+"0x".length());
        appid = appid.toUpperCase().replaceAll("[^A-Fa-f0-9]","");
        return appid;
    }

    private boolean isGoose(String cbname){
        if(cbname.substring(0, 2).equals("GO")){
            return true;
        }else if(cbname.substring(0, 2).equals("SV")) {
            return false;
        }else {
            Log.e(TAG, "type err!");
            return true;
        }

    }

    public boolean isProblemOnTouch(RectF rectF,float x, float y, float totalOffX, float totalOffY, float mSclTot){
        x -= totalOffX;
        y -= totalOffY;
        if(x>= rectF.left*mSclTot && x<= rectF.right*mSclTot
                && y>= rectF.top*mSclTot && y<= rectF.bottom*mSclTot)
            return true;
        else {
            return false;
        }
    }

}
